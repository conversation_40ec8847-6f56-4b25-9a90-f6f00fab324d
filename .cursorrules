When writing frontend and backend code, you need to follow the following rules

// Backend Rules
Here are some best practices and rules you must follow:

- Use Python 3.12
- Frameworks:
  - pydantic
  - fastapi
  - sqlalchemy
- Use uv for dependency management
- Use fastapi-users for user management
- Use python-jose for authentication
- Use fastapi-mail for email sending
- Use fastapi-cache for caching
- Use fastapi-limiter for rate limiting
- Use fastapi-pagination for pagination

// Recommended Directory Structure
backend/
  models/
  services/
  apis/
  utils/
  sql/
  tests/

// API Design
- Follow RESTful principles: use plural resource names, proper status codes, support pagination, filtering, and sorting
- All routes, parameters, and response bodies must have type annotations and documentation

// Configuration & Security
- Sensitive information must be managed via .env or config.py, hardcoding is strictly prohibited

// Logging & Exception Handling
- Use a unified logger (backend/utils/logger.py), catch exceptions and return standard error responses

// Testing
- Every service, API, and model must have unit tests, placed in the tests/ directory

1. Use Meaningful Names: Choose descriptive variable, function, and class names.
2. Follow PEP 8: Adhere to the Python Enhancement Proposal 8 style guide for formatting.
3. Use Docstrings: Document functions and classes with docstrings to explain their purpose.
4. Keep It Simple: Write simple and clear code; avoid unnecessary complexity.
5. Use List Comprehensions: Prefer list comprehensions for creating lists over traditional loops when appropriate.
6. Handle Exceptions: Use try-except blocks to handle exceptions gracefully.
7. Use Virtual Environments: Isolate project dependencies using virtual environments (e.g., venv).
8. Write Tests: Implement unit tests to ensure code reliability.
9. Use Type Hints: Utilize type hints for better code clarity and type checking.
10. Avoid Global Variables: Limit the use of global variables to reduce side effects.

These rules will help you write clean, efficient, and maintainable Python code.

// Frontend Rules
// Vue 3 Composition API Best Practices

const vue3CompositionApiBestPractices = [
  "Use setup() function for component logic",
  "Utilize ref and reactive for reactive state",
  "Implement computed properties with computed()",
  "Use watch and watchEffect for side effects",
  "Implement lifecycle hooks with onMounted, onUpdated, etc.",
  "Utilize provide/inject for dependency injection",
];

// Recommended Directory Structure
frontend/src/
  components/
  views/
  composables/
  router/
  store/
  assets/
  types/
  App.vue
  main.js

// TypeScript
- TypeScript is mandatory. All components, stores, APIs, props, and emits must have type declarations.

// Styles & Theme
- Use SCSS for styles, manage theme variables centrally
- Follow BEM naming conventions, avoid global style pollution

// Component Development
- All component props, emits, and slots must have type definitions and comments
- Components must have default values for props/emits
- All components must have unit tests

// API Calls & Data Management
- Use axios for all API calls, handle errors and loading states globally
- All API type definitions should be placed in the types/ directory

// Routing & Permissions
- All route files should be placed in router/, implement route guards for authentication and redirection

// Code Style
- Use eslint/prettier for code style, auto-format before commit

// Others
- Use npm for development and build
- If internationalization is needed, use i18n and place language packs in locales/

const additionalInstructions = `
1. Use TypeScript for type safety
2. Implement proper props and emits definitions
3. Utilize Vue 3's Teleport component when needed
4. Use Suspense for async components
5. Implement proper error handling
6. Follow Vue 3 style guide and naming conventions
7. Use Vite for fast development and building
`;

