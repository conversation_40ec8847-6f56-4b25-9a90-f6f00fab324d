# KOL Hub - 全流程营销协作与数据归因平台

KOL Hub 是一个面向品牌方和KOL的全流程营销协作与数据归因平台，支持KOL库管理、任务协同、自动结算、API Key管理、数据看板、Twitter集成等功能。前后端分离架构，后端基于 FastAPI + SQLAlchemy，前端基于 Vue3 + Element Plus。

## 🚀 主要特性

- **KOL库管理**：多维度筛选、标签搜索、卡片式展示
- **任务协同**：任务创建、认领、进度跟踪、状态流转
- **数据归因**：实时统计、多维度分析、Top5排行
- **自动结算**：KOL结算、历史记录、费用明细
- **消息系统**：站内消息、通知中心、实时提醒
- **Twitter集成**：OAuth绑定、数据同步、粉丝统计
- **文件存储**：支持阿里云OSS和AWS S3
- **定时任务**：日度结算、数据同步、统计生成
- **API管理**：API Key生成、权限控制、使用统计

---

## 📁 目录结构

```
kol-hub/
├── backend/                    # 后端服务（FastAPI）
│   ├── api.py                 # FastAPI 主入口
│   ├── apis/                  # 业务接口模块
│   │   ├── bit_apis.py        # BIT营销任务接口
│   │   ├── bit_profile_apis.py # BIT用户资料接口
│   │   ├── kol_apis.py        # KOL相关接口
│   │   ├── kol_profile_apis.py # KOL资料管理接口
│   │   ├── marketing_task_apis.py # 营销任务接口
│   │   ├── message_apis.py    # 消息系统接口
│   │   ├── oss_apis.py        # 文件上传接口
│   │   ├── settlement_apis.py  # 结算相关接口
│   │   ├── stats_apis.py      # 统计数据接口
│   │   ├── twitter_apis.py    # Twitter集成接口
│   │   └── user_apis.py       # 用户管理接口
│   ├── models/                # ORM模型
│   │   ├── bit_profile.py     # BIT用户资料模型
│   │   ├── kol_profile.py     # KOL资料模型
│   │   ├── MarketingTask.py   # 营销任务模型
│   │   ├── Message.py         # 消息模型
│   │   ├── settlement.py      # 结算模型
│   │   ├── stats.py          # 统计模型
│   │   ├── user.py           # 用户模型
│   │   └── db.py             # 数据库配置
│   ├── services/              # 业务服务层
│   │   ├── api_key_service.py # API Key服务
│   │   ├── bit_profile_service.py # BIT资料服务
│   │   ├── kol_profile_service.py # KOL资料服务
│   │   ├── kol_settlement_service.py # KOL结算服务
│   │   ├── kol_task_service.py # KOL任务服务
│   │   ├── marketing_task_service.py # 营销任务服务
│   │   ├── message_service.py # 消息服务
│   │   ├── stats_service.py   # 统计服务
│   │   ├── twitter_service.py # Twitter服务
│   │   └── user_service.py    # 用户服务
│   ├── repositories/          # 数据访问层
│   │   ├── base_repository.py # 基础仓储类
│   │   ├── bit_profile_repository.py # BIT资料仓储
│   │   ├── kol_profile_repository.py # KOL资料仓储
│   │   ├── marketing_task_repository.py # 营销任务仓储
│   │   ├── message_repository.py # 消息仓储
│   │   └── user_repository.py # 用户仓储
│   ├── jobs/                  # 定时任务模块
│   │   ├── scheduler.py       # 任务调度器
│   │   ├── daily_settlement.py # 日度结算任务
│   │   ├── twitter_metrics_sync.py # Twitter数据同步
│   │   └── test_job.py        # 测试任务
│   ├── storage/               # 文件存储模块
│   │   ├── service.py         # 存储服务接口
│   │   ├── factory.py         # 存储提供商工厂
│   │   ├── cli.py            # 命令行工具
│   │   └── providers/         # 存储提供商实现
│   │       ├── oss_provider.py # 阿里云OSS
│   │       └── s3_provider.py  # AWS S3
│   ├── sql/                   # 数据库脚本
│   │   └── create_table.sql   # 建表脚本
│   ├── utils/                 # 工具类
│   │   ├── config.py          # 配置管理
│   │   ├── jwt.py            # JWT认证
│   │   ├── logger.py         # 日志工具
│   │   ├── security.py       # 安全工具
│   │   └── exceptions.py     # 异常定义
│   ├── tests/                 # 测试文件
│   │   ├── test_jobs.py      # 任务测试
│   │   ├── test_storage.py   # 存储测试
│   │   └── twitter/          # Twitter测试
│   └── pyproject.toml         # Python依赖与配置
├── frontend/                  # 前端项目（Vue3）
│   ├── src/
│   │   ├── components/        # 通用组件
│   │   │   ├── Login.vue     # 登录组件
│   │   │   ├── Register.vue  # 注册组件
│   │   │   ├── KOLRegister.vue # KOL注册组件
│   │   │   ├── TaskDetailDialog.vue # 任务详情弹窗
│   │   │   ├── TwitterOAuthBinding.vue # Twitter绑定组件
│   │   │   ├── MessageList.vue # 消息列表
│   │   │   ├── NotificationCenter.vue # 通知中心
│   │   │   └── TaskStatusFlow.vue # 任务状态流转
│   │   ├── views/             # 页面视图
│   │   │   ├── Dashboard.vue  # 数据看板
│   │   │   ├── KOLCenter.vue  # KOL中心
│   │   │   ├── KOLDetail.vue  # KOL详情
│   │   │   ├── KOLPersonalCenter.vue # KOL个人中心
│   │   │   ├── TaskCenter.vue # 任务中心
│   │   │   ├── TaskMarket.vue # 任务市场
│   │   │   ├── MyTasks.vue    # 我的任务
│   │   │   ├── Settlement.vue # 结算中心
│   │   │   ├── AssetAndSettlement.vue # 资产结算
│   │   │   ├── PerformanceCenter.vue # 绩效中心
│   │   │   ├── MessageCenter.vue # 消息中心
│   │   │   ├── APIKeyManage.vue # API Key管理
│   │   │   ├── UserProfile.vue # 用户资料
│   │   │   └── UserVerify.vue  # 用户认证
│   │   ├── router/            # 路由配置
│   │   ├── store/             # 状态管理
│   │   │   ├── user.js       # 用户状态
│   │   │   └── message.js    # 消息状态
│   │   ├── utils/             # 工具类
│   │   │   ├── api.js        # API调用封装
│   │   │   └── errorHandler.js # 错误处理
│   │   ├── config/            # 配置文件
│   │   └── assets/            # 静态资源
│   ├── package.json           # 前端依赖与脚本
│   └── vue.config.js         # Vue配置
├── docs/                      # 文档目录
│   ├── API_Documentation_add_kol_stats.md # API文档
│   ├── BASIC_ENV.md          # 基础环境配置
│   └── NGINX_ENV.md          # Nginx配置
├── scripts/                   # 部署脚本
│   ├── deploy.sh             # 部署脚本
│   ├── manage.sh             # 管理脚本
│   ├── kol-hub-backend.service # 后端系统服务
│   ├── kol-hub-frontend.service # 前端系统服务
│   └── conf.d/               # Nginx配置
└── README.md                  # 项目说明
```

---

## 🛠 技术栈

### 后端技术
- **核心框架**：Python 3.12, FastAPI, SQLAlchemy
- **数据库**：MySQL 8.0+, PyMySQL
- **认证授权**：JWT, bcrypt, python-jose
- **任务调度**：APScheduler
- **文件存储**：阿里云OSS, AWS S3
- **邮件服务**：fastapi-mail
- **缓存系统**：fastapi-cache
- **限流控制**：fastapi-limiter
- **分页组件**：fastapi-pagination
- **HTTP服务**：Uvicorn
- **数据验证**：Pydantic
- **依赖管理**：uv

### 前端技术
- **核心框架**：Vue 3 (Composition API)
- **UI组件库**：Element Plus
- **HTTP客户端**：Axios
- **路由管理**：Vue Router
- **状态管理**：Pinia
- **样式预处理**：SCSS
- **构建工具**：Webpack
- **包管理器**：npm

### 基础设施
- **Web服务器**：Nginx
- **进程管理**：systemd
- **容器化**：Docker (可选)
- **版本控制**：Git

---

## 🚀 环境准备

### 1. 克隆项目

```bash
git clone https://your.repo.url/kol-hub.git
cd kol-hub
```

### 2. 数据库初始化

创建 MySQL 数据库并执行建表脚本：

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE kol_hub CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 执行建表脚本
mysql -u youruser -p kol_hub < backend/sql/create_table.sql
```

### 3. 后端环境配置

#### 安装依赖

确保已安装 Python 3.12 和 uv：

```bash
# 安装uv (如果未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 进入后端目录并安装依赖
cd backend
uv sync
```

#### 配置环境变量

在 `backend/` 目录下创建 `.env` 文件：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=youruser
DB_PASSWORD=yourpassword
DB_NAME=kol_hub

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 文件存储配置 (选择其一)
# 阿里云OSS
CLOUD_STORAGE_PROVIDER=oss
OSS_BUCKET_NAME=your-bucket-name
OSS_REGION=ap-southeast-1
OSS_ACCESS_KEY_ID=your_key_id
OSS_ACCESS_KEY_SECRET=your_key_secret

# 或AWS S3
# CLOUD_STORAGE_PROVIDER=s3
# AWS_S3_BUCKET_NAME=your-bucket-name
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_key_id
# AWS_SECRET_ACCESS_KEY=your_key_secret

# Twitter API配置 (可选)
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_REDIRECT_URI=http://localhost:8002/api/kol/twitter/oauth/callback

# 邮件配置 (可选)
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.example.com
MAIL_STARTTLS=True
MAIL_SSL_TLS=False

# Redis配置 (用于缓存和限流，可选)
REDIS_URL=redis://localhost:6379/0
```

#### 启动后端服务

```bash
# 开发模式启动
uv run api.py

# 或指定端口和主机
uv run api.py --reload --host 0.0.0.0 --port 8002

# 生产模式启动
uv run uvicorn api:app --host 0.0.0.0 --port 8002 --workers 4
```

### 4. 前端环境配置

#### 安装依赖

```bash
cd frontend
npm install
```

#### 配置API地址

修改 `frontend/src/config/index.js`：

```javascript
const config = {
  // 开发环境
  development: {
    API_BASE_URL: 'http://localhost:8002'
  },
  // 生产环境
  production: {
    API_BASE_URL: 'https://your-api-domain.com'
  }
}
```

#### 启动前端开发服务器

```bash
# 开发模式
npm run serve

# 构建生产版本
npm run build
```

默认端口为 `8080`，可通过 `http://localhost:8080` 访问。

---

## 📊 主要功能模块

### 1. 用户管理系统
- **用户注册/登录**：支持商户和KOL两种用户类型
- **用户资料管理**：个人信息维护、头像上传
- **用户认证**：身份验证、实名认证
- **权限控制**：基于角色的访问控制

### 2. KOL库管理
- **KOL资料管理**：完整的KOL档案信息
- **多维度筛选**：按平台、标签、粉丝数等筛选
- **卡片式展示**：深色主题、响应式设计
- **Twitter集成**：OAuth绑定、数据同步、粉丝统计
- **合作状态跟踪**：合作历史、互动率统计

### 3. 任务协同系统
- **任务创建**：支持多种任务类型（推文、视频、文章、直播、AMA活动）
- **任务分配**：KOL邀请、任务认领
- **状态流转**：草稿→已发布→已分配→待审核→审核通过→已完成
- **内容提交**：草稿提交、链接提交、内容审核
- **进度跟踪**：实时状态更新、任务进度可视化

### 4. 数据归因看板
- **实时统计**：点击量、注册量、FTT值、入金金额
- **多维度分析**：按任务、按KOL、按时间维度
- **KOL排行榜**：转化率Top5、收益Top5
- **图表展示**：趋势图、饼图、柱状图
- **数据导出**：支持Excel导出

### 5. 自动结算系统
- **结算规则**：基础奖励+效果奖励
- **自动计算**：根据数据归因自动计算收益
- **结算记录**：历史结算、状态管理
- **费用明细**：详细的收益构成
- **批量结算**：支持批量处理

### 6. 消息通知系统
- **站内消息**：系统通知、任务提醒
- **实时通知**：WebSocket实时推送
- **消息分类**：任务消息、系统消息、结算消息
- **消息状态**：已读/未读状态管理
- **通知中心**：统一的消息管理界面

### 7. API管理系统
- **API Key生成**：自动生成唯一密钥
- **权限控制**：基于API Key的访问控制
- **使用统计**：API调用次数统计
- **密钥管理**：显示/隐藏、复制、更新

### 8. 文件存储系统
- **多云支持**：阿里云OSS、AWS S3
- **统一接口**：抽象存储接口，支持无缝切换
- **文件管理**：上传、下载、删除、存在检查
- **命令行工具**：便捷的文件操作CLI
- **批量操作**：支持批量上传/删除

### 9. 定时任务系统
- **任务调度**：基于APScheduler的定时任务
- **日度结算**：每日自动生成结算统计
- **数据同步**：Twitter数据定时同步
- **任务监控**：任务状态监控、异常处理

---

## 🔌 主要API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `PUT /api/user/profile` - 更新用户资料
- `GET /api/user/verify/status` - 获取认证状态
- `POST /api/user/verify` - 提交认证信息

### KOL相关
- `GET /api/kol/profiles` - 获取KOL列表
- `POST /api/kol/profile` - 创建KOL资料
- `PUT /api/kol/profile` - 更新KOL资料
- `GET /api/kol/profile/{id}/detail` - 获取KOL详情
- `GET /api/kol/performance` - 获取KOL绩效数据

### 任务相关
- `POST /api/bit/marketing_task/create` - 创建营销任务
- `GET /api/bit/marketing_task` - 获取任务列表
- `POST /api/kol/task/apply` - 申请任务
- `POST /api/kol/task/{id}/submit_draft` - 提交草稿
- `POST /api/kol/task/{id}/submit_links` - 提交发布链接
- `GET /api/kol/my-tasks` - 获取我的任务

### 统计相关
- `GET /api/stats/dashboard/summary` - 获取看板统计
- `GET /api/stats/dashboard/kol_top5` - 获取KOL Top5
- `GET /api/stats/task/{id}/api-stats` - 获取任务API统计
- `GET /api/stats/task/{id}/channel-stats` - 获取任务渠道统计

### Twitter集成
- `GET /api/kol/twitter/oauth/url` - 获取OAuth授权链接
- `POST /api/kol/twitter/sync` - 同步Twitter数据
- `DELETE /api/kol/twitter/unbind` - 解绑Twitter账号
- `GET /api/kol/twitter/status` - 获取绑定状态

### 文件存储
- `POST /api/oss/upload` - 文件上传
- `GET /api/oss/download/{object_name}` - 获取下载链接
- `DELETE /api/oss/delete/{object_name}` - 删除文件
- `GET /api/oss/exists/{object_name}` - 检查文件是否存在

### 消息系统
- `GET /api/message/` - 获取消息列表
- `POST /api/message/{id}/read` - 标记消息已读
- `POST /api/message/read-all` - 标记所有消息已读
- `GET /api/message/unread/count` - 获取未读消息数量

---

## 📊 数据库表结构

详见 [`backend/sql/create_table.sql`](backend/sql/create_table.sql)，主要表：

### 用户相关表
- `user_info`：用户基础信息（商户、KOL）
- `user_api_key`：API Key管理表
- `bit_profile`：BIT用户资料表
- `kol_profile`：KOL基础信息表

### 任务相关表
- `marketing_task`：营销任务主表
- `kol_task`：KOL任务关联表
- `task_content`：任务内容提交记录

### 结算相关表
- `kol_settlement`：KOL结算信息表
- `settlement_history`：结算历史记录表
- `settlement_stats`：结算统计表

### 统计相关表
- `kol_marketing_stats`：营销数据归因统计表
- `task_stats`：任务统计表
- `daily_stats`：日度统计表

### 消息相关表
- `message`：消息表
- `notification`：通知表

---

## 🚀 部署配置

### 使用系统服务部署

1. **复制服务文件**
```bash
sudo cp scripts/kol-hub-backend.service /etc/systemd/system/
sudo cp scripts/kol-hub-frontend.service /etc/systemd/system/
```

2. **启动服务**
```bash
sudo systemctl daemon-reload
sudo systemctl enable kol-hub-backend
sudo systemctl enable kol-hub-frontend
sudo systemctl start kol-hub-backend
sudo systemctl start kol-hub-frontend
```

3. **配置Nginx**
```bash
sudo cp scripts/conf.d/kol-hub.conf /etc/nginx/conf.d/
sudo nginx -t
sudo systemctl reload nginx
```

### 使用部署脚本

```bash
# 一键部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh

# 使用管理脚本
chmod +x scripts/manage.sh
./scripts/manage.sh start    # 启动服务
./scripts/manage.sh stop     # 停止服务
./scripts/manage.sh restart  # 重启服务
./scripts/manage.sh status   # 查看状态
```

---

## 🔧 开发指南

### 后端开发规范

1. **代码结构**
   - 遵循分层架构：Controller → Service → Repository → Model
   - API层禁止直接操作数据库，所有数据库操作在Service层完成
   - 单行代码长度不超过100字符

2. **数据库设计**
   - 禁止使用ENUM类型，推荐使用VARCHAR或TINYINT
   - 所有表必须有主键和时间戳字段
   - 外键关系通过应用层维护

3. **API设计**
   - 遵循RESTful原则
   - 所有接口必须有类型注解和文档
   - 统一的错误响应格式
   - 支持分页、过滤、排序

### 前端开发规范

1. **Vue 3最佳实践**
   - 使用Composition API
   - 组件必须有类型定义和注释
   - 遵循Vue 3风格指南

2. **代码风格**
   - 使用ESLint和Prettier
   - 遵循BEM命名规范
   - 避免全局样式污染

3. **API调用**
   - 统一使用apiService进行API调用
   - 全局错误处理和加载状态管理
   - 请求和响应拦截器

---

## 📝 常见问题

### 端口冲突
如果8002/8080端口被占用，可以修改配置：
```bash
# 后端
uv run api.py --port 8003

# 前端
npm run serve -- --port 8081
```

### 数据库连接失败
1. 检查MySQL服务是否启动
2. 验证`.env`文件中的数据库配置
3. 确认数据库用户权限

### 跨域问题
开发环境已配置CORS，生产环境建议使用Nginx代理：
```nginx
location /api/ {
    proxy_pass http://localhost:8002;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 文件上传失败
1. 检查存储服务配置（OSS/S3）
2. 验证访问密钥和权限
3. 确认bucket存在且可访问

### Twitter集成问题
1. 确认Twitter Developer账号和应用配置
2. 检查回调URL设置
3. 验证Client ID和Secret

---

## 🤝 贡献指南

1. Fork 本仓库并创建功能分支
2. 遵循代码规范和最佳实践
3. 编写单元测试和文档
4. 提交PR前确保所有测试通过
5. PR标题使用约定式提交格式

### 提交信息格式
```
type(scope): description

feat(api): add user authentication
fix(ui): resolve login form validation
docs(readme): update installation guide
```

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

## 📞 联系与支持

- **项目维护者**：[Your Name](mailto:<EMAIL>)
- **问题反馈**：[GitHub Issues](https://github.com/your-repo/kol-hub/issues)
- **技术文档**：[项目Wiki](https://github.com/your-repo/kol-hub/wiki)

---

## 🎯 路线图

### v1.1 (计划中)
- [ ] 移动端适配
- [ ] 多语言支持
- [ ] 高级数据分析
- [ ] 批量任务操作

### v1.2 (计划中)
- [ ] 微信小程序
- [ ] 更多社交平台集成
- [ ] AI内容推荐
- [ ] 高级权限管理

---

*最后更新：2024年1月*