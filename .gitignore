# 现有的 gitignore 内容
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Frontend
frontend/node_modules/
frontend/dist/
frontend/.temp/
frontend/.cache/
frontend/.nuxt/
frontend/.output/
frontend/.vuepress/dist
frontend/.serverless/
frontend/coverage/
frontend/*.log
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Backend
backend/.venv/
backend/__pycache__/
backend/*.pyc
backend/*.pyo
backend/*.pyd
backend/.Python
backend/env/
backend/venv/
backend/.env
backend/.pytest_cache/
backend/htmlcov/
backend/.coverage
backend/.coverage.*
backend/coverage.xml
backend/*.cover
backend/.hypothesis/

# 启停脚本生成的文件和目录
pids/
logs/
*.pid
*.log

# 临时文件
*.tmp
*.temp
debug-info.txt

# 备份文件
*.bak
*.backup

# augment
.augment