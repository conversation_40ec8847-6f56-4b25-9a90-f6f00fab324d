/* Element Plus 深色主题全局样式 */

/* 对话框样式 */
.el-dialog {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.el-dialog__header {
  background: #23242a !important;
  border-bottom: 1px solid #555 !important;
}

.el-dialog__title {
  color: #fff !important;
}

.el-dialog__body {
  background: #23242a !important;
  color: #fff !important;
}

.el-dialog__footer {
  background: #23242a !important;
  border-top: 1px solid #555 !important;
}

/* 表单样式 */
.el-form-item__label {
  color: #fff !important;
  font-weight: normal !important;
}

.el-form-item__content {
  color: #fff !important;
}

/* 输入框样式 */
.el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
  box-shadow: none !important;
}

.el-input__wrapper:hover {
  border-color: #409EFF !important;
}

.el-input__wrapper.is-focus {
  border-color: #409EFF !important;
  box-shadow: 0 0 0 1px #409EFF !important;
}

.el-input__inner {
  background: #181818 !important;
  color: #fff !important;
  border: none !important;
}

.el-input__inner::placeholder {
  color: #aaa !important;
}

/* 文本域样式 */
.el-textarea__inner {
  background: #181818 !important;
  color: #fff !important;
  border: 1px solid #555 !important;
}

.el-textarea__inner:hover {
  border-color: #409EFF !important;
}

.el-textarea__inner:focus {
  border-color: #409EFF !important;
  box-shadow: 0 0 0 1px #409EFF !important;
}

/* 下拉框样式 */
.el-select .el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
}

.el-select .el-input__inner {
  background: #181818 !important;
  color: #fff !important;
}

.el-select-dropdown {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.el-option {
  background: #23242a !important;
  color: #fff !important;
}

.el-option:hover {
  background: #2a2b32 !important;
}

.el-option.is-selected {
  background: #409EFF !important;
  color: #fff !important;
}

/* 按钮样式 */
.el-button {
  border: 1px solid #555 !important;
  color: #fff !important;
  background: #2a2b32 !important;
}

.el-button:hover {
  background: #3a3b42 !important;
  border-color: #666 !important;
}

.el-button--primary {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: #fff !important;
}

.el-button--primary:hover {
  background: #66b3ff !important;
  border-color: #66b3ff !important;
}

.el-button--success {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
  color: #fff !important;
}

.el-button--success:hover {
  background: #66bb6a !important;
  border-color: #66bb6a !important;
}

/* 标签样式 */
.el-tag {
  background: #2a2b32 !important;
  border-color: #555 !important;
  color: #fff !important;
}

.el-tag--success {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
  color: #fff !important;
}

.el-tag--warning {
  background: #ff9800 !important;
  border-color: #ff9800 !important;
  color: #fff !important;
}

.el-tag--danger {
  background: #f44336 !important;
  border-color: #f44336 !important;
  color: #fff !important;
}

.el-tag--info {
  background: #2196F3 !important;
  border-color: #2196F3 !important;
  color: #fff !important;
}

/* 表格样式 */
.el-table {
  background: #23242a !important;
  color: #fff !important;
}

.el-table th {
  background: #2a2b32 !important;
  color: #fff !important;
  border-color: #555 !important;
}

.el-table td {
  background: #23242a !important;
  color: #fff !important;
  border-color: #555 !important;
}

.el-table tr:hover td {
  background: #2a2b32 !important;
}

/* 分页样式 */
.el-pagination {
  color: #fff !important;
}

.el-pagination .el-pager li {
  background: #2a2b32 !important;
  color: #fff !important;
  border: 1px solid #555 !important;
}

.el-pagination .el-pager li:hover {
  background: #409EFF !important;
}

.el-pagination .el-pager li.is-active {
  background: #409EFF !important;
  color: #fff !important;
}

/* 消息提示样式 */
.el-message {
  background: #23242a !important;
  border: 1px solid #555 !important;
  color: #fff !important;
}

.el-message--success {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
}

.el-message--warning {
  background: #ff9800 !important;
  border-color: #ff9800 !important;
}

.el-message--error {
  background: #f44336 !important;
  border-color: #f44336 !important;
}

/* 菜单样式 */
.el-menu {
  background: #23242a !important;
  border-color: #555 !important;
}

.el-menu-item {
  background: #23242a !important;
  color: #fff !important;
}

.el-menu-item:hover {
  background: #2a2b32 !important;
}

.el-menu-item.is-active {
  background: #409EFF !important;
  color: #fff !important;
}

/* 卡片样式 */
.el-card {
  background: #23242a !important;
  border-color: #555 !important;
}

.el-card__header {
  background: #2a2b32 !important;
  color: #fff !important;
  border-color: #555 !important;
}

.el-card__body {
  background: #23242a !important;
  color: #fff !important;
}

/* 加载样式 */
.el-loading-mask {
  background: rgba(35, 36, 42, 0.8) !important;
}

.el-loading-text {
  color: #fff !important;
}

/* 工具提示样式 */
.el-tooltip__popper {
  background: #23242a !important;
  border: 1px solid #555 !important;
  color: #fff !important;
}

.el-tooltip__popper .el-tooltip__arrow::before {
  border-top-color: #23242a !important;
}

/* 统一消息提示样式 - 方案1：简约现代风格 */
/* 使用最高优先级选择器确保样式生效 */

/* Element Plus 消息提示样式覆盖 - 超高优先级 */
html body .el-message.el-message--success,
html body .el-message--success.el-message,
html body div.el-message.el-message--success,
body .el-message.el-message--success {
  background: linear-gradient(135deg, #67C23A, #85CE61) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
  border-radius: 8px !important;
}

html body .el-message.el-message--error,
html body .el-message--error.el-message,
html body div.el-message.el-message--error,
body .el-message.el-message--error {
  background: linear-gradient(135deg, #F56C6C, #F78989) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
  border-radius: 8px !important;
}

html body .el-message.el-message--warning,
html body .el-message--warning.el-message,
html body div.el-message.el-message--warning,
body .el-message.el-message--warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4) !important;
  border-radius: 8px !important;
}

html body .el-message.el-message--info,
html body .el-message--info.el-message,
html body div.el-message.el-message--info,
body .el-message.el-message--info {
  background: linear-gradient(135deg, #409EFF, #73B3FF) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
  border-radius: 8px !important;
}

/* 消息提示图标和关闭按钮样式优化 - 超高优先级 */
html body .el-message.el-message--success .el-message__icon,
html body .el-message.el-message--error .el-message__icon,
html body .el-message.el-message--warning .el-message__icon,
html body .el-message.el-message--info .el-message__icon,
body .el-message .el-message__icon {
  color: #fff !important;
}

html body .el-message.el-message--success .el-message__closeBtn,
html body .el-message.el-message--error .el-message__closeBtn,
html body .el-message.el-message--warning .el-message__closeBtn,
html body .el-message.el-message--info .el-message__closeBtn,
body .el-message .el-message__closeBtn {
  color: #fff !important;
  opacity: 0.8 !important;
}

html body .el-message.el-message--success .el-message__closeBtn:hover,
html body .el-message.el-message--error .el-message__closeBtn:hover,
html body .el-message.el-message--warning .el-message__closeBtn:hover,
html body .el-message.el-message--info .el-message__closeBtn:hover,
body .el-message .el-message__closeBtn:hover {
  opacity: 1 !important;
}

/* 统一消息提示的显示时间和位置 - 增强优先级 */
.el-message.el-message,
.el-message {
  min-width: 300px !important;
  max-width: 500px !important;
}

/* 额外的样式覆盖，确保在所有情况下都能生效 */
div.el-message.el-message--warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4) !important;
  border-radius: 8px !important;
}

div.el-message.el-message--success {
  background: linear-gradient(135deg, #67C23A, #85CE61) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
  border-radius: 8px !important;
}

div.el-message.el-message--error {
  background: linear-gradient(135deg, #F56C6C, #F78989) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
  border-radius: 8px !important;
}

div.el-message.el-message--info {
  background: linear-gradient(135deg, #409EFF, #73B3FF) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
  border-radius: 8px !important;
}

/* 终极覆盖样式 - 确保在任何情况下都能生效 */
[class*="el-message"] {
  border-radius: 8px !important;
}

[class*="el-message--warning"] {
  background: linear-gradient(135deg, #E6A23C, #EEBE77) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4) !important;
}

[class*="el-message--success"] {
  background: linear-gradient(135deg, #67C23A, #85CE61) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
}

[class*="el-message--error"] {
  background: linear-gradient(135deg, #F56C6C, #F78989) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
}

[class*="el-message--info"] {
  background: linear-gradient(135deg, #409EFF, #73B3FF) !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
}

/* 强制覆盖所有消息框的文字和图标颜色 */
[class*="el-message"] * {
  color: #fff !important;
}
