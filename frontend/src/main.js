import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
// import './assets/element-dark-theme.css'  // 导入深色主题样式
import router from './router'
import { createPinia } from 'pinia'
import config from './config'

// 打印当前配置（开发环境下）
if (config.ENV === 'development') {
  config.printConfig()
}

const app = createApp(App)
app.use(createPinia())
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(router)

// 将配置挂载到全局，便于组件访问
app.config.globalProperties.$config = config

// 全局错误处理 - 忽略 ResizeObserver 错误
const originalError = console.error
console.error = (...args) => {
  if (
    args.length > 0 &&
    typeof args[0] === 'string' &&
    args[0].includes('ResizeObserver loop completed with undelivered notifications')
  ) {
    // 忽略 ResizeObserver 错误
    return
  }
  originalError.apply(console, args)
}

// 处理未捕获的错误
window.addEventListener('error', (event) => {
  if (
    event.message &&
    event.message.includes('ResizeObserver loop completed with undelivered notifications')
  ) {
    event.preventDefault()
    return false
  }
})

app.mount('#app')
