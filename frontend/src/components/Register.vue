<template>
  <el-drawer v-model="visible" title="注册新用户" size="45%" :close-on-click-modal="false">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" autocomplete="off" />
        <div style="font-size:12px; color:#909399; margin-top:4px;">
          3-20个字符，只能包含字母、数字和下划线
        </div>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="form.password" type="password" autocomplete="off" />
        <div style="font-size:12px; color:#909399; margin-top:4px;">
          至少8个字符，必须包含字母和数字
        </div>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" autocomplete="off" />
      </el-form-item>
      <el-form-item label="用户类型" prop="user_type">
        <el-select v-model="form.user_type" placeholder="请选择用户类型">
          <el-option label="商户" value="merchant" />
          <el-option label="KOL主播" value="kol" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" autocomplete="off" />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="form.nickname" autocomplete="off" />
      </el-form-item>
      <el-form-item label="头像URL" prop="avatar">
        <el-input v-model="form.avatar" autocomplete="off" />
      </el-form-item>
      <el-form-item label="收款地址" prop="wallet_address">
        <el-input v-model="form.wallet_address" autocomplete="off" placeholder="请输入ERC20收款钱包地址" />
      </el-form-item>
      <div v-if="errorMsg" style="color:#f56c6c; text-align:center; margin-bottom:10px;">{{ errorMsg }}</div>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="onRegister">注册</el-button>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'
import { extractRegisterErrorMessage } from '@/utils/errorHandler'

const emit = defineEmits(['registration-complete'])

const visible = ref(false)
const formRef = ref()
const form = ref({
  username: '',
  password: '',
  email: '',
  user_type: '',
  phone: '',
  nickname: '',
  avatar: '',
  wallet_address: ''
})
const errorMsg = ref('')

// 用户名验证器
const validateUsername = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  }
  if (value.length < 3) {
    return callback(new Error('用户名至少需要3个字符'))
  }
  if (value.length > 20) {
    return callback(new Error('用户名不能超过20个字符'))
  }
  const usernamePattern = new RegExp('^[a-zA-Z0-9_]+$')
  if (!usernamePattern.test(value)) {
    return callback(new Error('用户名只能包含字母、数字和下划线'))
  }
  callback()
}

// 密码验证器
const validatePassword = (rule, value, callback) => {
  try {
    if (!value) {
      callback(new Error('请输入密码'))
      return
    }
    if (value.length < 8) {
      callback(new Error('密码至少需要8个字符'))
      return
    }
    if (value.length > 50) {
      callback(new Error('密码不能超过50个字符'))
      return
    }

    // 检查是否包含字母和数字
    let hasLetter = false
    let hasNumber = false

    for (let i = 0; i < value.length; i++) {
      const char = value[i]
      if ((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')) {
        hasLetter = true
      } else if (char >= '0' && char <= '9') {
        hasNumber = true
      }
    }

    if (!hasLetter) {
      callback(new Error('密码必须包含字母'))
      return
    }
    if (!hasNumber) {
      callback(new Error('密码必须包含数字'))
      return
    }

    callback()
  } catch (error) {
    console.error('Password validation error:', error)
    callback(new Error('密码验证出错，请重试'))
  }
}

const rules = {
  username: [{ required: true, validator: validateUsername, trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword, trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  user_type: [{ required: true, message: '请选择用户类型', trigger: 'change' }],
  // wallet_address: [{ required: true, message: '请输入收款钱包地址', trigger: 'blur' }]
}

function open() {
  visible.value = true
  errorMsg.value = ''
  Object.assign(form.value, { username: '', password: '', email: '', user_type: '', phone: '', nickname: '', avatar: '', wallet_address: '' })
}
defineExpose({ open })

async function onRegister() {
  try {
    // 验证表单
    await formRef.value.validate()

    // 如果验证通过，则进行注册
    const response = await apiService.register(form.value)
    visible.value = false
    errorMsg.value = ''
    // 🔧 传递注册响应数据（现在包含 Token）
    emit('registration-complete', response.data)
  } catch (e) {
    // 如果是表单验证错误，不显示错误信息（Element Plus会自动显示）
    if (e && typeof e === 'object' && !e.response) {
      console.log('表单验证失败:', e)
      return
    }
    // 如果是API调用错误，使用统一的错误处理工具
    errorMsg.value = extractRegisterErrorMessage(e)
  }
}
</script>

<style>
/* 全局样式 - 不使用scoped，确保能覆盖Element Plus */
/* .el-dialog {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.el-dialog__header {
  background: #23242a !important;
  border-bottom: 1px solid #555 !important;
}

.el-dialog__title {
  color: #fff !important;
}

.el-dialog__body {
  background: #23242a !important;
  color: #fff !important;
}

.el-dialog__footer {
  background: #23242a !important;
  border-top: 1px solid #555 !important;
} */

/* 表单样式 */
/* .el-form-item__label {
  color: #fff !important;
  font-weight: normal !important;
}

.el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
  box-shadow: none !important;
}

.el-input__inner {
  background: #181818 !important;
  color: #fff !important;
  border: none !important;
}

.el-input__inner::placeholder {
  color: #aaa !important;
}

.el-select .el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
}

.el-select .el-input__inner {
  background: #181818 !important;
  color: #fff !important;
} */

/* 按钮样式 */
/* .el-button {
  border: 1px solid #555 !important;
  color: #fff !important;
}

.el-button--primary {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: #fff !important;
} */

/* 下拉框样式 */
/* .el-select-dropdown {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.el-option {
  background: #23242a !important;
  color: #fff !important;
}

.el-option:hover {
  background: #2a2b32 !important;
} */
</style>
