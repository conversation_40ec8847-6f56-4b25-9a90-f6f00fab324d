<template>
  <div class="twitter-oauth-binding">
    <!-- Twitter信息卡片 -->
    <el-card class="twitter-card">
      <div class="card-title">🐦 Twitter账号</div>

      <!-- 未绑定状态 -->
      <div v-if="!twitterBound" class="unbind-state">
        <el-empty description="暂未绑定Twitter账号">
          <template #image>
            <div class="empty-icon">🐦</div>
          </template>
          <el-button type="primary" @click="startBinding">立即绑定</el-button>
        </el-empty>
      </div>

      <!-- 已绑定状态 -->
      <div v-else class="bind-state">
        <div class="twitter-profile">
          <div class="profile-header">
            <img :src="twitterData.profile_image_url" class="avatar" />
            <div class="profile-info">
              <div class="display-name">
                {{ twitterData.platform_name }}
                <el-tag v-if="twitterData.verified" type="success" size="small">
                  <el-icon>
                    <Check />
                  </el-icon>
                  已认证
                </el-tag>
              </div>
              <p class="username">@{{ twitterData.platform_username }}</p>
            </div>
          </div>

          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(twitterData.followers_count) }}</span>
              <span class="stat-label">粉丝</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(twitterData.following_count) }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(twitterData.tweet_count) }}</span>
              <span class="stat-label">推文</span>
            </div>
          </div>

          <div class="last-sync">
            <span class="sync-text">最后同步: {{ formatDateTime(twitterData.last_synced_at) }}</span>
          </div>

          <div class="actions">
            <el-button @click="syncData" :loading="syncing" type="primary" plain>
              <el-icon>
                <Refresh />
              </el-icon>
              &nbsp;同步数据
            </el-button>
            <el-button @click="unbindAccount" type="danger" plain style="border: none;">
              <el-icon>
                <Close />
              </el-icon>
              &nbsp;解除绑定
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- OAuth绑定对话框 -->
    <el-drawer v-model="bindDialogVisible" title="绑定Twitter账号" width="500px" :close-on-click-modal="false"
      @close="resetBindingState">
      <div class="oauth-binding-content">
        <!-- 绑定说明 -->
        <div class="binding-intro">
          <div class="intro-icon">🐦</div>
          <h3>正在连接您的Twitter账号</h3>
          <p v-if="currentStep === 1">正在获取授权链接，请稍候...</p>
          <p v-else-if="currentStep === 2">请在新窗口中完成Twitter授权</p>
          <p v-else>授权完成，正在绑定账号...</p>

          <!-- 调试信息 -->
          <div v-if="debugInfo"
            style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; font-size: 12px; text-align: left;">
            <strong>调试信息：</strong><br>
            <pre>{{ debugInfo }}</pre>
          </div>
        </div>

        <!-- OAuth认证步骤 -->
        <div class="oauth-steps">
          <div class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">获取授权链接</div>
              <div class="step-desc">系统自动获取Twitter授权链接</div>
            </div>
          </div>
          <div class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">完成Twitter授权</div>
              <div class="step-desc">在新窗口中点击"授权应用"</div>
            </div>
          </div>
          <div class="step-item" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">完成绑定</div>
              <div class="step-desc">授权完成后自动绑定并显示账号信息</div>
            </div>
          </div>
        </div>

        <!-- OAuth授权按钮 -->
        <div class="oauth-action" v-if="currentStep === 1 && !oauthing">
          <el-button type="primary" size="large" @click="startOAuth" :loading="false">
            <el-icon>
              <Link />
            </el-icon>
            &nbsp;手动开始授权
          </el-button>
        </div>

        <!-- 进度指示 -->
        <div class="oauth-progress" v-else>
          <el-progress :percentage="(currentStep / 3) * 100" :show-text="false" :stroke-width="6" color="#409EFF" />
          <p class="progress-text">{{ getProgressText() }}</p>
        </div>

        <!-- 安全提示 -->
        <div class="security-notice">
          <el-alert title="安全提示" type="info" :closable="false" show-icon>
            <template #default>
              <p>• 我们只会获取您的基本账号信息和公开数据</p>
              <p>• 不会发布任何内容到您的Twitter账号</p>
              <p>• 您可以随时在设置中解除绑定</p>
            </template>
          </el-alert>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Refresh, Close, Link } from '@element-plus/icons-vue'
import apiService from '@/utils/api'

// 响应式数据
const twitterBound = ref(false)
const twitterData = ref(null)
const syncing = ref(false)

// OAuth绑定状态
const bindDialogVisible = ref(false)
const currentStep = ref(1)
const oauthing = ref(false)
const authWindow = ref(null)
const pollInterval = ref(null)
const debugInfo = ref('')

// 格式化数字
function formatNumber(num) {
  if (!num) return '0'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化日期时间
function formatDateTime(dateStr) {
  if (!dateStr) return '未知'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

// 获取按钮文本
function getButtonText() {
  if (oauthing.value) {
    return currentStep.value === 1 ? '正在获取授权链接...' : '等待授权完成...'
  }
  return currentStep.value === 1 ? '开始Twitter授权' : '授权进行中...'
}

// 获取进度文本
function getProgressText() {
  switch (currentStep.value) {
    case 1:
      return oauthing.value ? '正在获取授权链接...' : '准备开始授权'
    case 2:
      return '等待您在新窗口中完成授权...'
    case 3:
      return '授权成功，正在绑定账号...'
    default:
      return '处理中...'
  }
}

// 开始绑定流程
function startBinding() {
  console.log('startBinding called - opening dialog')
  bindDialogVisible.value = true
  resetBindingState()
  console.log('Dialog should be visible now, starting OAuth in 500ms...')
  // 自动开始OAuth流程
  setTimeout(() => {
    console.log('Timeout reached, calling startOAuth...')
    startOAuth()
  }, 500) // 延迟500ms让对话框完全显示
}

// 重置绑定状态
function resetBindingState() {
  currentStep.value = 1
  oauthing.value = false
  stopAuthPolling()
}

// 启动OAuth认证
async function startOAuth() {
  try {
    oauthing.value = true
    currentStep.value = 1

    console.log('Starting OAuth flow...')

    // 获取OAuth授权URL
    const response = await apiService.getTwitterOAuthUrl()

    console.log('OAuth URL response:', response)
    console.log('Response data:', response.data)
    console.log('Response data type:', typeof response.data)

    // 显示调试信息
    debugInfo.value = `响应状态: ${response.status}\n响应数据类型: ${typeof response.data}\n响应数据: ${JSON.stringify(response.data, null, 2)}`

    // 检查响应是否是HTML
    if (typeof response.data === 'string' && response.data.includes('<html>')) {
      console.error('Received HTML instead of JSON:', response.data.substring(0, 200) + '...')
      debugInfo.value += '\n\n错误: 服务器返回了HTML页面而不是JSON数据'
      throw new Error('服务器返回了HTML页面而不是JSON数据')
    }

    if (response.data && response.data.auth_url) {
      currentStep.value = 2

      console.log('Opening auth window with URL:', response.data.auth_url)

      // 打开授权窗口
      authWindow.value = window.open(
        response.data.auth_url,
        'twitter_auth',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      )

      if (!authWindow.value) {
        throw new Error('无法打开授权窗口，请检查浏览器弹窗设置')
      }

      // 开始轮询检查授权状态
      startAuthPolling()
    } else {
      console.error('Invalid response structure:', response.data)
      throw new Error('未获取到授权URL')
    }
  } catch (error) {
    console.error('OAuth URL error:', error)
    let errorMessage = '获取授权链接失败'

    if (error.response?.data) {
      // 如果响应数据是字符串且包含HTML，说明返回了HTML页面
      if (typeof error.response.data === 'string' && error.response.data.includes('<html>')) {
        errorMessage += '：服务器返回了HTML页面，请检查API配置'
      } else if (error.response.data.detail) {
        errorMessage += '：' + error.response.data.detail
      } else {
        errorMessage += '：' + error.response.status + ' ' + error.response.statusText
      }
    } else {
      errorMessage += '：' + error.message
    }

    ElMessage.error(errorMessage)
    resetBindingState()
  }
}

// 开始轮询授权状态
function startAuthPolling() {
  // 监听窗口消息
  const messageHandler = (event) => {
    if (event.data.type === 'twitter_auth_success') {
      window.removeEventListener('message', messageHandler)
      stopAuthPolling()
      handleOAuthSuccess(event.data)
    } else if (event.data.type === 'twitter_auth_error') {
      window.removeEventListener('message', messageHandler)
      stopAuthPolling()
      ElMessage.error('Twitter授权失败：' + event.data.error)
      resetBindingState()
    }
  }

  window.addEventListener('message', messageHandler)

  // 定期检查窗口是否关闭
  pollInterval.value = setInterval(async () => {
    try {
      // 检查授权窗口是否关闭
      if (authWindow.value && authWindow.value.closed) {
        window.removeEventListener('message', messageHandler)
        stopAuthPolling()

        // 检查绑定状态
        await checkBindingStatus()
        return
      }
    } catch (error) {
      console.log('Auth polling error:', error)
    }
  }, 2000) // 每2秒检查一次
}

// 停止轮询
function stopAuthPolling() {
  if (pollInterval.value) {
    clearInterval(pollInterval.value)
    pollInterval.value = null
  }

  if (authWindow.value) {
    authWindow.value.close()
    authWindow.value = null
  }

  oauthing.value = false
}

// 处理OAuth成功
async function handleOAuthSuccess(data) {
  try {
    currentStep.value = 3

    // 重新加载Twitter状态
    await loadTwitterStatus()

    // 关闭对话框
    bindDialogVisible.value = false

    ElMessage.success('Twitter账号绑定成功！')

  } catch (error) {
    ElMessage.error('绑定处理失败：' + error.message)
  }
}

// 检查绑定状态
async function checkBindingStatus() {
  try {
    await loadTwitterStatus()
    if (twitterBound.value) {
      currentStep.value = 3
      bindDialogVisible.value = false
      ElMessage.success('Twitter账号绑定成功！')
    } else {
      ElMessage.warning('授权未完成，请重试')
      resetBindingState()
    }
  } catch (error) {
    ElMessage.error('检查绑定状态失败')
    resetBindingState()
  }
}

// 同步数据
async function syncData() {
  try {
    syncing.value = true
    const response = await apiService.syncTwitter()

    if (response.data.success) {
      // 重新加载最新数据
      await loadTwitterStatus()
      ElMessage.success('数据同步成功！')
    }
  } catch (error) {
    ElMessage.error('同步失败：' + (error.response?.data?.detail || error.message))
  } finally {
    syncing.value = false
  }
}

// 解除绑定
async function unbindAccount() {
  try {
    await ElMessageBox.confirm(
      '确定要解除Twitter账号绑定吗？解除后将无法获取相关数据。',
      '确认解除绑定',
      {
        confirmButtonText: '确定解除',
        cancelButtonText: '取消',
        type: 'warning',
        // 🔧 修复：自定义样式确保文字清晰可见
        customClass: 'twitter-unbind-confirm',
        dangerouslyUseHTMLString: false,
        center: true,
      }
    )

    await apiService.unbindTwitter()

    // 🔧 恢复原始逻辑：立即重置状态，确保UI立即更新
    twitterBound.value = false
    twitterData.value = null

    ElMessage.success('Twitter账号解绑成功')

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('解绑失败：' + (error.response?.data?.detail || error.message))
    }
  }
}

// 加载Twitter状态
async function loadTwitterStatus() {
  try {
    // 🔧 修复：使用正确的接口获取当前用户的KOL详情
    const profileResponse = await apiService.getCurrentUserKolDetail()
    // const profileResponse = {
    //   data: {
    //     platform: 'twitter',
    //     platform_username: 'WMQ',
    //     platform_name: 'aaa',
    //     profile_image_url: 'http://192.168.2.24:8080/img/logo.57e52b02.png',
    //     followers_count: 123,
    //     following_count: 321,
    //     tweet_count: 12312,
    //     verified: true,
    //     verified_type: '1',
    //     last_synced_at: +new Date()
    //   }
    // }
    if (profileResponse.data && profileResponse.data.platform === 'twitter') {
      twitterBound.value = true
      twitterData.value = {
        platform_username: profileResponse.data.platform_username,
        platform_name: profileResponse.data.platform_name,
        profile_image_url: profileResponse.data.profile_image_url,
        followers_count: profileResponse.data.followers_count,
        following_count: profileResponse.data.following_count,
        tweet_count: profileResponse.data.tweet_count,
        verified: profileResponse.data.verified,
        verified_type: profileResponse.data.verified_type,
        last_synced_at: profileResponse.data.last_synced_at
      }
      console.log('✅ Twitter 数据加载成功:', twitterData.value)
    } else {
      twitterBound.value = false
      twitterData.value = null
      console.log('ℹ️ 当前用户没有绑定 Twitter 或平台不是 Twitter')
    }
  } catch (error) {
    console.error('获取 KOL 详情失败:', error)
    // 如果没有KOL Profile，检查Twitter绑定状态
    try {
      const statusResponse = await apiService.getTwitterStatus()
      twitterBound.value = statusResponse.data.bound
      twitterData.value = statusResponse.data.data
      console.log('✅ 从 Twitter 状态接口获取数据:', twitterData.value)
    } catch (statusError) {
      console.error('Failed to load Twitter status:', statusError)
      twitterBound.value = false
      twitterData.value = null
    }
  }
}

// 组件挂载时加载状态
onMounted(() => {
  loadTwitterStatus()
})

// 组件卸载时清理
onUnmounted(() => {
  stopAuthPolling()
})
</script>

<style scoped>
.twitter-oauth-binding {
  flex: 1;
  margin: 0 24px;
}
.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.twitter-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 20px;
  box-shadow: #eee 2px 2px 2px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.unbind-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
}

.profile-info {
  flex: 1;
}

.display-name {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: bold;
}

.username {
  margin: 0 0 8px 0;
  color: #aaa;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f6f9f8;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #aaa;
}

.last-sync {
  margin-bottom: 20px;
  text-align: center;
}

.sync-text {
  font-size: 12px;
  color: #666;
}

.actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* OAuth对话框样式 */

.binding-intro {
  text-align: center;
  margin-bottom: 30px;
}

.intro-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.binding-intro h3 {
  margin: 0 0 10px 0;
}

.binding-intro p {
  margin: 0;
  color: #aaa;
}

.oauth-steps {
  margin-bottom: 30px;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background: #ddd;
  border-radius: 8px;
  border-left: 4px solid #aaa;
  transition: all 0.3s;
}

.step-item.active {
  border-left-color: #409EFF;
  background: #e7f4ff;
}

.step-item.completed {
  border-left-color: #4CAF50;
  background: #e7f4ff;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #aaa;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: #409EFF;
}

.step-item.completed .step-number {
  background: #4CAF50;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.step-desc {
  font-size: 14px;
  color: #aaa;
}

.oauth-action {
  text-align: center;
  margin-bottom: 30px;
}

.oauth-progress {
  text-align: center;
  margin-bottom: 30px;
}

.progress-text {
  margin-top: 15px;
  color: #409EFF;
  font-size: 14px;
  font-weight: 500;
}

.security-notice {
  margin-top: 20px;
}

.security-notice p {
  margin: 5px 0;
  font-size: 14px;
}

/* 🔧 修复：Twitter 解绑确认对话框样式 */
:deep(.twitter-unbind-confirm) {
  background-color: #ffffff !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.twitter-unbind-confirm .el-message-box__header) {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 20px 24px 16px !important;
}

:deep(.twitter-unbind-confirm .el-message-box__title) {
  color: #343a40 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

:deep(.twitter-unbind-confirm .el-message-box__content) {
  background-color: #ffffff !important;
  padding: 20px 24px !important;
}

:deep(.twitter-unbind-confirm .el-message-box__message) {
  color: #495057 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

:deep(.twitter-unbind-confirm .el-message-box__btns) {
  background-color: #f8f9fa !important;
  border-top: 1px solid #e9ecef !important;
  padding: 16px 24px 20px !important;
}

:deep(.twitter-unbind-confirm .el-button--primary) {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: #ffffff !important;
}

:deep(.twitter-unbind-confirm .el-button--primary:hover) {
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

:deep(.twitter-unbind-confirm .el-button--default) {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #ffffff !important;
}

:deep(.twitter-unbind-confirm .el-button--default:hover) {
  background-color: #5a6268 !important;
  border-color: #545b62 !important;
}
</style>
