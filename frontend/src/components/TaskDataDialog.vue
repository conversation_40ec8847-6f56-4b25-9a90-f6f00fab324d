<template>
  <el-drawer v-model="visible" :title="(statsData?.task_info?.task_name || '') + '任务数据详情'" size="50%"
    :before-close="handleClose">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="statsData" class="stats-content">
      <!-- 任务基本信息 -->
      <div class="task-basic-info">
        <div class="info-item">
          <span class="label">渠道码：</span>
          <span class="value">{{ statsData.task_info?.channel_code }}</span>
        </div>
      </div>

      <!-- 总体数据概览 -->
      <div class="stats-section">
        <h3>📊 总体数据概览</h3>
        <div class="stats-grid">
          <div class="stat-item card1">
            <div class="stat-value">{{ formatNumber(statsData.total_stats?.total_clicks) }}</div>
            <div class="stat-label">点击次数</div>
          </div>
          <div class="stat-item card2">
            <div class="stat-value">{{ formatNumber(statsData.total_stats?.total_registers) }}</div>
            <div class="stat-label">注册用户</div>
          </div>
          <div class="stat-item card3">
            <div class="stat-value">${{ formatNumber(statsData.total_stats?.total_deposit) }}</div>
            <div class="stat-label">入金金额</div>
          </div>
          <div class="stat-item card4">
            <div class="stat-value">{{ formatNumber(statsData.total_stats?.total_ftt) }}</div>
            <div class="stat-label">FTT 值</div>
          </div>
        </div>
      </div>

      <!-- Twitter 帖子指标 -->
      <div class="stats-section">
        <h3>🐦 Twitter 帖子指标</h3>

        <!-- 各帖子详细指标 -->
        <div class="posts-container">
          <!-- 有数据时显示实际帖子 -->
          <template v-if="getPlatformPosts().length">
            <div v-for="(post, index) in getPlatformPosts()" :key="post.post_id || index"
              class="post-card">
              <div class="post-url">
                <a v-if="post.post_url" :href="post.post_url" target="_blank" class="url-link">
                  🔗 {{ post.post_url }}
                </a>
                <span v-else class="url-placeholder">🔗 暂无链接</span>
              </div>

              <div class="post-metrics" v-if="post.metrics">
                <div class="metrics-grid">
                  <div class="metric-item">
                    <div class="metric-value">{{ formatNumber(post.metrics.view_count) }}</div>
                    <div class="metric-label">查看数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value">{{ formatNumber(post.metrics.retweet_count) }}</div>
                    <div class="metric-label">转发数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value">{{ formatNumber(post.metrics.like_count) }}</div>
                    <div class="metric-label">收藏数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value">{{ formatNumber(post.metrics.quote_count) }}</div>
                    <div class="metric-label">引用数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value">{{ formatNumber(post.metrics.reply_count) }}</div>
                    <div class="metric-label">评论数</div>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 无数据时显示占位符 -->
          <template v-else>
            <div class="post-card">
              <div class="post-url">
                <span class="url-placeholder">🔗 暂无链接</span>
              </div>

              <div class="post-metrics">
                <div class="metrics-grid">
                  <div class="metric-item">
                    <div class="metric-value placeholder">-</div>
                    <div class="metric-label">查看数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value placeholder">-</div>
                    <div class="metric-label">转发数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value placeholder">-</div>
                    <div class="metric-label">收藏数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value placeholder">-</div>
                    <div class="metric-label">引用数</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-value placeholder">-</div>
                    <div class="metric-label">评论数</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 转化率分析 -->
      <div class="stats-section">
        <h3>🎯 转化率分析</h3>
        <div class="conversion-rates">
          <div class="rate-item">
            <span class="rate-label">注册转化率：</span>
            <span class="rate-value">{{ statsData.conversion_rates?.register_conversion_rate }}%</span>
          </div>
          <div class="rate-item">
            <span class="rate-label">入金转化率：</span>
            <span class="rate-value">{{ statsData.conversion_rates?.deposit_conversion_rate }}%</span>
          </div>
        </div>
      </div>

      <!-- 按日期统计 -->
      <div class="stats-section" v-if="statsData.daily_stats?.length">
        <h3>📈 按日期统计</h3>
        <div class="daily-stats-table">
          <el-table :data="statsData.daily_stats" stripe border>
            <el-table-column prop="stat_date" label="日期">
              <template #default="{ row }">
                {{ formatDate(row.stat_date) }}
              </template>
            </el-table-column>
            <el-table-column prop="daily_clicks" label="点击" />
            <el-table-column prop="daily_registers" label="注册" />
            <el-table-column prop="daily_deposit" label="入金">
              <template #default="{ row }">
                ${{ formatNumber(row.daily_deposit) }}
              </template>
            </el-table-column>
            <el-table-column prop="daily_ftt" label="FTT">
              <template #default="{ row }">
                {{ formatNumber(row.daily_ftt) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'

// Props
const props = defineProps({
  modelValue: Boolean,
  task: Object
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const statsData = ref(null)

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.task) {
    fetchTaskStats()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
async function fetchTaskStats() {
  if (!props.task?.id) return

  loading.value = true
  try {
    const response = await apiService.getKolTaskStats(props.task.id)
    // const response = {
    //   data: {
    //     task_info: {
    //       task_name: '巴拉 black 名字',
    //       channel_code: 'c23d'
    //     },
    //     total_stats: {
    //       total_clicks: 2233,
    //       total_registers: 5566,
    //       total_deposit: 212,
    //       total_ftt: 555,
    //     },
    //     platform_metrics: {
    //       posts: [
    //         {
    //           post_id: 1,
    //           post_url: 'http://wwww.a123.com',
    //           metrics: {
    //             view_count: 322,
    //             retweet_count: 123,
    //             like_count: 231231,
    //             quote_count: 2312312,
    //             reply_count: 123123
    //           }
    //         },
    //         {
    //           post_id: 1,
    //           post_url: 'aaaa',
    //           metrics: {
    //             view_count: 322,
    //             retweet_count: 123,
    //             like_count: 231231,
    //             quote_count: 2312312,
    //             reply_count: 123123
    //           }
    //         },
    //       ]
    //     },
    //     conversion_rates: {
    //       register_conversion_rate: 0.34,
    //       deposit_conversion_rate: 0.22,
    //     },
    //     daily_stats: [
    //       {
    //         stat_date: + new Date(),
    //         daily_clicks: 123,
    //         daily_registers: 321,
    //         daily_deposit: 4345,
    //         daily_ftt: 12312
    //       },
    //       {
    //         stat_date: + new Date(),
    //         daily_clicks: 123,
    //         daily_registers: 321,
    //         daily_deposit: 4345,
    //         daily_ftt: 12312
    //       },
    //       {
    //         stat_date: + new Date(),
    //         daily_clicks: 123,
    //         daily_registers: 321,
    //         daily_deposit: 4345,
    //         daily_ftt: 12312
    //       },
    //     ]
    //   }
    // }

    console.log('📊 TaskDataDialog - 原始API响应:', response.data)
    console.log('📊 TaskDataDialog - platform_posts:', response.data.platform_posts)
    console.log('📊 TaskDataDialog - platform_metrics:', response.data.platform_metrics)

    statsData.value = response.data

    console.log('📊 TaskDataDialog - 处理后的statsData:', statsData.value)
    console.log('📊 TaskDataDialog - getPlatformPosts()结果:', getPlatformPosts())
  } catch (error) {
    console.error('获取任务统计失败:', error)
    ElMessage.error('获取任务统计失败')
  } finally {
    loading.value = false
  }
}

function handleClose() {
  visible.value = false
  statsData.value = null
}

// 数据处理方法
function getPlatformPosts() {
  if (!statsData.value) return []

  // 兼容新的数据结构：platform_posts
  if (statsData.value.platform_posts && Array.isArray(statsData.value.platform_posts)) {
    return statsData.value.platform_posts
  }

  // 兼容旧的数据结构：platform_metrics.posts
  if (statsData.value.platform_metrics?.posts && Array.isArray(statsData.value.platform_metrics.posts)) {
    return statsData.value.platform_metrics.posts
  }

  return []
}

// 工具方法
function formatNumber(num) {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString()
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getTaskTypeIcon(taskType) {
  const iconMap = {
    'tweet': '📝',
    'video': '📹',
    'article': '📰',
    'live': '📺',
    'ama': '🎤'
  }
  return iconMap[taskType] || '📋'
}

function getTaskTypeLabel(taskType) {
  const labelMap = {
    'tweet': '推文',
    'video': '视频',
    'article': '文章',
    'live': '直播',
    'ama': 'AMA活动'
  }
  return labelMap[taskType] || taskType
}
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.task-basic-info {
  margin-bottom: 24px;
  /* padding: 12px; */
  /* display: flex; */
  /* background: #f6f9f8; */
  /* justify-content: space-between; */
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
}


.label {
  font-weight: 500;
  min-width: 80px;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  color: #fff;
    background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

    &.card2 {
      background: linear-gradient(to bottom right, #e74888, #bc53a1);
    }

    &.card3 {
      background: linear-gradient(to bottom right, #825dbf, #5345b4);
    }

    &.card4 {
      background: linear-gradient(to bottom right, #fbb728, #f68254);
    }
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
}

.conversion-rates {
  display: flex;
  gap: 24px;
}

.rate-item {
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #efefef;
  border-left: 4px solid #409eff;
}

.rate-label {
  margin-right: 8px;
}

.rate-value {
  font-weight: 600;
  color: #409eff;
}

.daily-stats-table {
  max-height: 300px;
  overflow-y: auto;
}

/* 平台指标样式 */
.posts-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.post-card {
  border-radius: 4px;
  border: 1px solid #efefef;
  background: #f6f9f8;
  padding: 12px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.post-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.post-icon {
  font-size: 16px;
}

.post-label {
  font-weight: 600;
}

.post-date {
  font-size: 12px;
}

.post-url {
  margin-bottom: 16px;
}

.url-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  word-break: break-all;
}

.url-link:hover {
  text-decoration: underline;
}

.url-placeholder {
  color: #666;
  font-size: 14px;
  word-break: break-all;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.metric-item {
  text-align: center;
  padding: 12px 8px;
  border: 1px solid #efefef;
  background: #fff;;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #aaa;
}

.metric-value.placeholder {
  color: #666;
}
</style>
