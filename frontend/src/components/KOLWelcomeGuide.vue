<template>
  <el-dialog 
    v-model="visible" 
    title="🎉 欢迎加入KOL Hub平台！" 
    width="800px" 
    :close-on-click-modal="false"
    class="welcome-guide-dialog"
  >
    <div class="welcome-content">
      <!-- 欢迎信息 -->
      <div class="welcome-header">
        <div class="welcome-title">恭喜您成功注册KOL Hub平台！</div>
        <div class="welcome-subtitle">您现在可以开始接受营销任务并获得丰厚收益</div>
      </div>

      <!-- 功能介绍 -->
      <div class="features-section">
        <div class="section-title">🚀 平台功能介绍</div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <div class="feature-title">业绩中心</div>
            <div class="feature-desc">查看您的推广数据、收益统计和业绩排名</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📝</div>
            <div class="feature-title">任务看板</div>
            <div class="feature-desc">浏览可申请的营销任务，管理进行中的项目</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💰</div>
            <div class="feature-title">资产结算</div>
            <div class="feature-desc">查看收益明细，申请提现到您的钱包</div>
          </div>
          <div class="feature-card">
            <div class="feature-icon">👤</div>
            <div class="feature-title">个人中心</div>
            <div class="feature-desc">管理个人资料，完善专业信息</div>
          </div>
        </div>
      </div>

      <!-- 收益模式 -->
      <div class="earning-section">
        <div class="section-title">💎 收益模式</div>
        <div class="earning-cards">
          <div class="earning-card">
            <div class="earning-icon">🎯</div>
            <div class="earning-title">基础报酬</div>
            <div class="earning-desc">完成任务即可获得固定报酬，通常在$100-$2000 USDT</div>
          </div>
          <div class="earning-card">
            <div class="earning-icon">📈</div>
            <div class="earning-title">效果奖励</div>
            <div class="earning-desc">根据推广效果获得额外奖励，每个有效注册$5-$50 USDT</div>
          </div>
          <div class="earning-card">
            <div class="earning-icon">🏆</div>
            <div class="earning-title">长期合作</div>
            <div class="earning-desc">表现优秀的KOL可获得长期合作机会和更高报酬</div>
          </div>
        </div>
      </div>

      <!-- 下一步操作 -->
      <div class="next-steps-section">
        <div class="section-title">🎯 建议的下一步操作</div>
        <div class="steps-list">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">完善个人资料</div>
              <div class="step-desc">在个人中心添加更多专业标签和详细简介</div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">浏览可用任务</div>
              <div class="step-desc">在任务看板查看适合您的营销任务</div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">申请第一个任务</div>
              <div class="step-desc">选择感兴趣的项目并提交申请</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系支持 -->
      <div class="support-section">
        <div class="support-card">
          <div class="support-icon">💬</div>
          <div class="support-content">
            <div class="support-title">需要帮助？</div>
            <div class="support-desc">如有任何问题，请随时联系我们的客服团队</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="goToProfile">完善个人资料</el-button>
        <el-button type="primary" @click="goToTaskBoard">开始浏览任务</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import { useRouter } from 'vue-router'

const emit = defineEmits(['close'])
const router = useRouter()

const visible = ref(false)

function open() {
  visible.value = true
}

function close() {
  visible.value = false
  emit('close')
}

function goToProfile() {
  close()
  router.push('/kol-profile')
}

function goToTaskBoard() {
  close()
  router.push('/kol-task')
}

defineExpose({ open, close })
</script>

<style scoped>
.welcome-content {
  color: #fff;
}

.welcome-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #409EFF, #66b3ff);
  border-radius: 12px;
}

.welcome-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
}

.welcome-subtitle {
  font-size: 16px;
  color: #f0f8ff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.features-section {
  margin-bottom: 30px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.feature-card {
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
}

.feature-card:hover {
  border-color: #409EFF;
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.feature-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #fff;
}

.feature-desc {
  font-size: 14px;
  color: #ccc;
  line-height: 1.5;
}

.earning-section {
  margin-bottom: 30px;
}

.earning-cards {
  display: flex;
  gap: 15px;
}

.earning-card {
  flex: 1;
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.earning-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.earning-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #4CAF50;
}

.earning-desc {
  font-size: 14px;
  color: #ccc;
  line-height: 1.5;
}

.next-steps-section {
  margin-bottom: 30px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  display: flex;
  align-items: center;
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 20px;
}

.step-number {
  width: 40px;
  height: 40px;
  background: #409EFF;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #fff;
}

.step-desc {
  font-size: 14px;
  color: #ccc;
}

.support-section {
  margin-bottom: 20px;
}

.support-card {
  display: flex;
  align-items: center;
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 20px;
}

.support-icon {
  font-size: 32px;
  margin-right: 20px;
}

.support-content {
  flex: 1;
}

.support-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #fff;
}

.support-desc {
  font-size: 14px;
  color: #ccc;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 对话框样式 */
.welcome-guide-dialog :deep(.el-dialog) {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.welcome-guide-dialog :deep(.el-dialog__header) {
  background: #23242a !important;
  border-bottom: 1px solid #555 !important;
}

.welcome-guide-dialog :deep(.el-dialog__title) {
  color: #fff !important;
}

.welcome-guide-dialog :deep(.el-dialog__body) {
  background: #23242a !important;
  color: #fff !important;
}

.welcome-guide-dialog :deep(.el-dialog__footer) {
  background: #23242a !important;
  border-top: 1px solid #555 !important;
}

.welcome-guide-dialog :deep(.el-button) {
  border: 1px solid #555 !important;
  color: #fff !important;
}

.welcome-guide-dialog :deep(.el-button--primary) {
  background: #409EFF !important;
  border-color: #409EFF !important;
}
</style>
