<template>
  <el-dialog v-model="visible" title="注册新用户" width="500px" :close-on-click-modal="false" class="register-dialog">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="* 用户名" prop="username">
        <el-input v-model="form.username" autocomplete="off" />
        <div style="font-size:12px; color:#909399; margin-top:4px;">
          3-20个字符，只能包含字母、数字和下划线
        </div>
      </el-form-item>
      <el-form-item label="* 密码" prop="password">
        <el-input v-model="form.password" type="password" autocomplete="off" />
        <div style="font-size:12px; color:#909399; margin-top:4px;">
          至少8个字符，必须包含字母和数字
        </div>
      </el-form-item>
      <el-form-item label="* 邮箱" prop="email">
        <el-input v-model="form.email" autocomplete="off" />
      </el-form-item>
      <el-form-item label="* 用户类型" prop="user_type">
        <el-select v-model="form.user_type" placeholder="请选择用户类型" disabled>
          <el-option label="KOL主播" value="kol" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" autocomplete="off" />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="form.nickname" autocomplete="off" />
      </el-form-item>
      <el-form-item label="头像URL" prop="avatar">
        <el-input v-model="form.avatar" autocomplete="off" />
      </el-form-item>
      <el-form-item label="收款地址" prop="wallet_address">
        <el-input v-model="form.wallet_address" autocomplete="off" placeholder="请输入ERC20收款钱包地址" />
      </el-form-item>
      <div v-if="errorMsg" style="color:#f56c6c; text-align:center; margin-bottom:10px;">{{ errorMsg }}</div>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="onRegister" :loading="registering">注册</el-button>
    </template>
  </el-dialog>

  <!-- KOL信息完善弹窗 -->
  <el-dialog v-model="showKolSetup" title="完善KOL信息" width="600px" :close-on-click-modal="false" class="kol-setup-dialog">
    <div style="color:#fff; margin-bottom:20px;">
      <div style="font-size:18px; margin-bottom:10px;">🎉 注册成功！欢迎加入KOL Hub平台</div>
    </div>

    <!-- Twitter账号绑定 -->
    <div class="setup-section">
      <div class="section-title">🐦 Twitter账号绑定 *必填</div>
      <el-form :model="kolForm" label-width="120px">
        <el-form-item label="Twitter用户名">
          <el-input v-model="kolForm.platform_username" placeholder="请输入@后面的用户名" style="width:70%;">
            <template #prepend>@</template>
          </el-input>
          <el-button type="primary" @click="verifyTwitter" :loading="verifying" style="margin-left:10px;">验证账号</el-button>
        </el-form-item>

        <div v-if="twitterVerified" class="twitter-info">
          <div style="color:#4CAF50; margin-bottom:10px;">✅ 验证成功！获取到的账号信息：</div>
          <div class="twitter-details">
            <div>• 显示名称: {{ kolForm.platform_name }}</div>
            <div>• 粉丝数量: {{ formatNumber(kolForm.followers_count) }}</div>
            <div>• 关注数量: {{ formatNumber(kolForm.following_count) }}</div>
            <div>• 推文数量: {{ formatNumber(kolForm.tweet_count) }}</div>
            <div>• 认证状态: {{ kolForm.verified ? '✅蓝V认证' : '未认证' }}</div>
            <div>• 账号创建: {{ formatDate(kolForm.account_created_at) }}</div>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 专业信息 -->
    <div class="setup-section">
      <div class="section-title">📝 专业信息 *必填</div>
      <el-form :model="kolForm" label-width="120px">
        <el-form-item label="专业标签">
          <div style="margin-bottom:10px; color:#aaa;">最多3个，可自定义填写</div>
          <div class="tags-container">
            <el-tag
              v-for="tag in selectedTags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right:8px; margin-bottom:8px;"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="inputRef"
              v-model="inputValue"
              size="small"
              style="width:120px;"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
          </div>
          <div class="preset-tags">
            <el-tag
              v-for="tag in presetTags"
              :key="tag"
              :type="selectedTags.includes(tag) ? 'success' : 'info'"
              @click="toggleTag(tag)"
              style="margin-right:8px; margin-bottom:8px; cursor:pointer;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="个人简介">
          <el-input
            v-model="kolForm.description"
            type="textarea"
            :rows="4"
            placeholder="请简单介绍您的专业领域、经验和内容创作能力..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 收款设置 -->
    <div class="setup-section">
      <div class="section-title">💰 收款设置 *必填</div>
      <el-form :model="kolForm" label-width="120px">
        <el-form-item label="收款钱包地址">
          <el-input v-model="kolForm.wallet_address" placeholder="0x742d35Cc6634C0532925a3b8D4C6..." />
        </el-form-item>
        <el-form-item label="网络选择">
          <el-select v-model="kolForm.payment_network" placeholder="请选择网络">
            <el-option label="Ethereum (ERC-20)" value="ethereum" />
            <el-option label="BSC (BEP-20)" value="bsc" />
            <el-option label="Polygon (MATIC)" value="polygon" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="skipSetup">跳过</el-button>
      <el-button type="primary" @click="saveKolInfo" :loading="saving">保存信息</el-button>
      <el-button type="success" @click="completeSetup" :loading="saving">进入平台</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits, nextTick } from 'vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['registration-complete'])

const visible = ref(false)
const showKolSetup = ref(false)
const formRef = ref()
const inputRef = ref()
const registering = ref(false)
const verifying = ref(false)
const saving = ref(false)
const twitterVerified = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')
const registeredUser = ref(null)

const form = ref({
  username: '',
  password: '',
  email: '',
  user_type: 'kol',
  phone: '',
  nickname: '',
  avatar: '',
  wallet_address: ''
})

const kolForm = ref({
  user_id: null,
  platform: 'twitter',
  profile_url: '',
  platform_id: '',
  platform_username: '',
  platform_name: '',
  description: '',
  location: '',
  profile_image_url: '',
  verified: false,
  verified_type: '',
  account_created_at: null,
  followers_count: 0,
  following_count: 0,
  tweet_count: 0,
  listed_count: 0,
  like_count: 0,
  tag_name: '',
  wallet_address: '',
  payment_network: 'ethereum'
})

const selectedTags = ref([])
const presetTags = ['DeFi', 'NFT', 'GameFi', 'Layer2', '交易', '投资', '技术分析', '项目评测', '教育科普', '新闻资讯']
const errorMsg = ref('')

// 用户名验证器
const validateUsername = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  }
  if (value.length < 3) {
    return callback(new Error('用户名至少需要3个字符'))
  }
  if (value.length > 20) {
    return callback(new Error('用户名不能超过20个字符'))
  }
  const usernamePattern = new RegExp('^[a-zA-Z0-9_]+$')
  if (!usernamePattern.test(value)) {
    return callback(new Error('用户名只能包含字母、数字和下划线'))
  }
  callback()
}

// 密码验证器
const validatePassword = (rule, value, callback) => {
  try {
    if (!value) {
      callback(new Error('请输入密码'))
      return
    }
    if (value.length < 8) {
      callback(new Error('密码至少需要8个字符'))
      return
    }
    if (value.length > 50) {
      callback(new Error('密码不能超过50个字符'))
      return
    }

    // 检查是否包含字母和数字
    let hasLetter = false
    let hasNumber = false

    for (let i = 0; i < value.length; i++) {
      const char = value[i]
      if ((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')) {
        hasLetter = true
      } else if (char >= '0' && char <= '9') {
        hasNumber = true
      }
    }

    if (!hasLetter) {
      callback(new Error('密码必须包含字母'))
      return
    }
    if (!hasNumber) {
      callback(new Error('密码必须包含数字'))
      return
    }

    callback()
  } catch (error) {
    console.error('Password validation error:', error)
    callback(new Error('密码验证出错，请重试'))
  }
}

const rules = {
  username: [{ required: true, validator: validateUsername, trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword, trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  user_type: [{ required: true, message: '请选择用户类型', trigger: 'change' }]
}

function open() {
  visible.value = true
  errorMsg.value = ''
  resetForm()
}

function resetForm() {
  Object.assign(form.value, {
    username: '',
    password: '',
    email: '',
    user_type: 'kol',
    phone: '',
    nickname: '',
    avatar: '',
    wallet_address: ''
  })

  Object.assign(kolForm.value, {
    user_id: null,
    platform: 'twitter',
    profile_url: '',
    platform_id: '',
    platform_username: '',
    platform_name: '',
    description: '',
    location: '',
    profile_image_url: '',
    verified: false,
    verified_type: '',
    account_created_at: null,
    followers_count: 0,
    following_count: 0,
    tweet_count: 0,
    listed_count: 0,
    like_count: 0,
    tag_name: '',
    wallet_address: '',
    payment_network: 'ethereum'
  })

  selectedTags.value = []
  twitterVerified.value = false

  // 清理注册数据
  registeredUser.value = null
}

async function onRegister() {
  try {
    // 验证表单
    await formRef.value.validate()

    registering.value = true
    const response = await apiService.register(form.value)

    if (response.data) {
      // 保存注册成功的响应（现在包含 Token）
      registeredUser.value = response.data
      visible.value = false
      showKolSetup.value = true
      kolForm.value.wallet_address = form.value.wallet_address
      kolForm.value.user_id = response.data.user.id  // 🔧 注意：现在用户信息在 user 字段中
      errorMsg.value = ''
      ElMessage.success('注册成功！请完善KOL信息')
    }
  } catch (e) {
    registering.value = false

    // 如果是表单验证错误，不显示错误信息（Element Plus会自动显示）
    if (e && typeof e === 'object' && !e.response) {
      console.log('表单验证失败:', e)
      return
    }

    // 如果是API调用错误，显示错误信息
    errorMsg.value = e.response?.data?.detail || '注册失败'
  }
}

async function verifyTwitter() {
  if (!kolForm.value.platform_username) {
    ElMessage.warning('请输入Twitter用户名')
    return
  }

  verifying.value = true
  try {
    // 模拟Twitter验证API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟获取到的Twitter数据
    const mockTwitterData = {
      platform_id: `twitter_${kolForm.value.platform_username}_${Date.now()}`,
      platform_name: 'CryptoExpert',
      profile_url: `https://twitter.com/${kolForm.value.platform_username}`,
      profile_image_url: 'https://pbs.twimg.com/profile_images/example.jpg',
      location: 'Global',
      followers_count: 125678,
      following_count: 1234,
      tweet_count: 5678,
      listed_count: 89,
      like_count: 12345,
      verified: true,
      verified_type: 'blue',
      account_created_at: '2019-03-15T00:00:00Z'
    }

    // 更新kolForm数据
    Object.assign(kolForm.value, mockTwitterData)

    twitterVerified.value = true
    ElMessage.success('Twitter账号验证成功！')
  } catch (error) {
    ElMessage.error('Twitter账号验证失败，请检查用户名')
  } finally {
    verifying.value = false
  }
}

function toggleTag(tag) {
  if (selectedTags.value.includes(tag)) {
    removeTag(tag)
  } else if (selectedTags.value.length < 3) {
    selectedTags.value.push(tag)
  } else {
    ElMessage.warning('最多只能选择3个标签')
  }
}

function removeTag(tag) {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  }
}

function showInput() {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function handleInputConfirm() {
  if (inputValue.value && selectedTags.value.length < 3) {
    if (!selectedTags.value.includes(inputValue.value)) {
      selectedTags.value.push(inputValue.value)
    }
  } else if (selectedTags.value.length >= 3) {
    ElMessage.warning('最多只能选择3个标签')
  }
  inputVisible.value = false
  inputValue.value = ''
}

async function saveKolInfo() {
  if (!twitterVerified.value) {
    ElMessage.warning('请先验证Twitter账号')
    return
  }

  if (selectedTags.value.length === 0) {
    ElMessage.warning('请至少选择一个专业标签')
    return
  }

  if (!kolForm.value.wallet_address) {
    ElMessage.warning('请填写收款钱包地址')
    return
  }

  saving.value = true
  try {
    const kolData = {
      ...kolForm.value,
      tag_name: selectedTags.value.join(', ')
    }

    // 创建KOL资料
    await apiService.createKolProfile(kolData)

    // 更新用户的收款地址
    if (registeredUser.value) {
      await apiService.updateProfile({
        id: registeredUser.value.id,
        wallet_address: kolForm.value.wallet_address
      })
    }

    ElMessage.success('KOL信息保存成功！欢迎加入KOL Hub平台！')

    // 保存成功后，返回用户信息以便自动登录
    return true
  } catch (error) {
    console.error('保存KOL信息失败:', error)
    ElMessage.error(error.response?.data?.detail || '保存失败，请重试')
    return false
  } finally {
    saving.value = false
  }
}

function skipSetup() {
  showKolSetup.value = false
  // 🔧 简化：直接传递注册响应（现在已包含 Token）
  emit('registration-complete', registeredUser.value)
}

async function completeSetup() {
  const success = await saveKolInfo()
  if (success) {
    showKolSetup.value = false
    // 🔧 简化：直接传递注册响应（现在已包含 Token）
    emit('registration-complete', registeredUser.value)
  }
}

function formatNumber(num) {
  return num ? num.toLocaleString() : '0'
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })
}

defineExpose({ open })
</script>

<style>
/* 全局样式 - 不使用scoped，确保能覆盖Element Plus */
/* .register-dialog.el-dialog {
  background: #23242a !important;
  border: 1px solid #555 !important;
} */

/* .register-dialog .el-dialog__header {
  background: #23242a !important;
  border-bottom: 1px solid #555 !important;
}

.register-dialog .el-dialog__title {
  color: #fff !important;
}

.register-dialog .el-dialog__body {
  background: #23242a !important;
  color: #fff !important;
}

.register-dialog .el-dialog__footer {
  background: #23242a !important;
  border-top: 1px solid #555 !important;
} */

/* 表单标签样式 */
/* .register-dialog .el-form-item__label {
  color: #fff !important;
  font-weight: normal !important;
} */

/* 输入框样式 */
/* .register-dialog .el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
  box-shadow: none !important;
} */

/* .register-dialog .el-input__inner {
  background: #181818 !important;
  color: #fff !important;
  border: none !important;
} */

/* .register-dialog .el-input__inner::placeholder {
  color: #aaa !important;
} */

/* 下拉框样式 */
/* .register-dialog .el-select .el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
} */

/* .register-dialog .el-select .el-input__inner {
  background: #181818 !important;
  color: #fff !important;
} */

/* 按钮样式 */
/* .register-dialog .el-button {
  border: 1px solid #555 !important;
  color: #fff !important;
}

.register-dialog .el-button--primary {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: #fff !important;
} */

/* KOL设置对话框样式 */
.kol-setup-dialog.el-dialog {
  background: #23242a !important;
  border: 1px solid #555 !important;
}

.kol-setup-dialog .el-dialog__header {
  background: #23242a !important;
  border-bottom: 1px solid #555 !important;
}

.kol-setup-dialog .el-dialog__title {
  color: #fff !important;
}

.kol-setup-dialog .el-dialog__body {
  background: #23242a !important;
  color: #fff !important;
}

.kol-setup-dialog .el-dialog__footer {
  background: #23242a !important;
  border-top: 1px solid #555 !important;
}

.kol-setup-dialog .el-form-item__label {
  color: #fff !important;
}

.kol-setup-dialog .el-input__wrapper {
  background: #181818 !important;
  border: 1px solid #555 !important;
}

.kol-setup-dialog .el-input__inner {
  background: #181818 !important;
  color: #fff !important;
}

.kol-setup-dialog .el-textarea__inner {
  background: #181818 !important;
  color: #fff !important;
  border: 1px solid #555 !important;
}

.kol-setup-dialog .el-button {
  border: 1px solid #555 !important;
  color: #fff !important;
}

.kol-setup-dialog .el-button--primary {
  background: #409EFF !important;
  border-color: #409EFF !important;
}
</style>

<style scoped>
.register-dialog :deep(.el-dialog) {
  background: #23242a;
  border: 1px solid #555;
}

.register-dialog :deep(.el-dialog__header) {
  background: #23242a;
  border-bottom: 1px solid #555;
}

.register-dialog :deep(.el-dialog__title) {
  color: #fff;
}

.register-dialog :deep(.el-dialog__body) {
  background: #23242a;
  color: #fff;
}

.register-dialog :deep(.el-dialog__footer) {
  background: #23242a;
  border-top: 1px solid #555;
}

.kol-setup-dialog :deep(.el-dialog) {
  background: #23242a;
  border: 1px solid #555;
}

.kol-setup-dialog :deep(.el-dialog__header) {
  background: #23242a;
  border-bottom: 1px solid #555;
}

.kol-setup-dialog :deep(.el-dialog__title) {
  color: #fff;
}

.kol-setup-dialog :deep(.el-dialog__body) {
  background: #23242a;
  color: #fff;
}

.kol-setup-dialog :deep(.el-dialog__footer) {
  background: #23242a;
  border-top: 1px solid #555;
}

.setup-section {
  border: 1px solid #555;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #2a2b32;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15px;
}

.twitter-info {
  background: #2a2b32;
  border: 1px solid #4CAF50;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;
}

.twitter-details {
  color: #fff;
  line-height: 1.6;
}

.twitter-details div {
  margin-bottom: 5px;
}

.tags-container {
  margin-bottom: 15px;
}

.preset-tags {
  border-top: 1px solid #555;
  padding-top: 10px;
}

/* 表单样式 - 增强优先级 */
.register-dialog :deep(.el-form-item__label) {
  color: #fff !important;
  font-weight: normal !important;
}

.register-dialog :deep(.el-input__wrapper) {
  background: #181818 !important;
  border-color: #555 !important;
  box-shadow: 0 0 0 1px #555 inset !important;
}

.register-dialog :deep(.el-input__inner) {
  background: #181818 !important;
  color: #fff !important;
}

.register-dialog :deep(.el-input__inner::placeholder) {
  color: #aaa !important;
}

.register-dialog :deep(.el-textarea__inner) {
  background: #181818 !important;
  color: #fff !important;
  border-color: #555 !important;
}

.register-dialog :deep(.el-select .el-input__wrapper) {
  background: #181818 !important;
  border-color: #555 !important;
}

.register-dialog :deep(.el-select .el-input__inner) {
  background: #181818 !important;
  color: #fff !important;
}

/* KOL设置对话框表单样式 */
.kol-setup-dialog :deep(.el-form-item__label) {
  color: #fff !important;
  font-weight: normal !important;
}

.kol-setup-dialog :deep(.el-input__wrapper) {
  background: #181818 !important;
  border-color: #555 !important;
  box-shadow: 0 0 0 1px #555 inset !important;
}

.kol-setup-dialog :deep(.el-input__inner) {
  background: #181818 !important;
  color: #fff !important;
}

.kol-setup-dialog :deep(.el-input__inner::placeholder) {
  color: #aaa !important;
}

.kol-setup-dialog :deep(.el-textarea__inner) {
  background: #181818 !important;
  color: #fff !important;
  border-color: #555 !important;
}

.kol-setup-dialog :deep(.el-select .el-input__wrapper) {
  background: #181818 !important;
  border-color: #555 !important;
}

.kol-setup-dialog :deep(.el-select .el-input__inner) {
  background: #181818 !important;
  color: #fff !important;
}

/* 标签样式 */
:deep(.el-tag) {
  margin-right: 8px;
  margin-bottom: 8px;
}

:deep(.el-tag--info) {
  background: #2a2b32;
  border-color: #555;
  color: #fff;
}

:deep(.el-tag--success) {
  background: #4CAF50;
  border-color: #4CAF50;
  color: #fff;
}

/* 按钮样式 */
.register-dialog :deep(.el-button) {
  border-color: #555 !important;
  color: #fff !important;
}

.register-dialog :deep(.el-button--primary) {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: #fff !important;
}

.register-dialog :deep(.el-button--success) {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
  color: #fff !important;
}

.kol-setup-dialog :deep(.el-button) {
  border-color: #555 !important;
  color: #fff !important;
}

.kol-setup-dialog :deep(.el-button--primary) {
  background: #409EFF !important;
  border-color: #409EFF !important;
  color: #fff !important;
}

.kol-setup-dialog :deep(.el-button--success) {
  background: #4CAF50 !important;
  border-color: #4CAF50 !important;
  color: #fff !important;
}

/* 下拉框样式 */
.register-dialog :deep(.el-select-dropdown) {
  background: #23242a !important;
  border-color: #555 !important;
}

.register-dialog :deep(.el-option) {
  background: #23242a !important;
  color: #fff !important;
}

.register-dialog :deep(.el-option:hover) {
  background: #2a2b32 !important;
}

.kol-setup-dialog :deep(.el-select-dropdown) {
  background: #23242a !important;
  border-color: #555 !important;
}

.kol-setup-dialog :deep(.el-option) {
  background: #23242a !important;
  color: #fff !important;
}

.kol-setup-dialog :deep(.el-option:hover) {
  background: #2a2b32 !important;
}
</style>
