<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="选择KOL进行分配"
    width="800px"
    :before-close="handleClose"
  >
    <div class="kol-selector-container">
      <!-- 申请列表 -->
      <div v-if="applications.length > 0" class="applications-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3 class="section-title">KOL申请列表</h3>
          <el-tag type="warning" size="small">{{ applications.length }} 个待处理申请</el-tag>
        </div>
        
        <div class="applications-list">
          <el-table 
            :data="applications" 
            style="width: 100%" 
            class="applications-table"
            @row-click="handleRowClick"
            :row-class-name="getRowClassName"
          >
            <el-table-column type="radio" width="50">
              <template #default="scope">
                <el-radio 
                  v-model="selectedKolId" 
                  :label="scope.row.kol_id"
                  @change="handleKolSelect(scope.row.kol_id)"
                >
                  &nbsp;
                </el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="kol_username" label="KOL用户名" width="120" />
            <el-table-column prop="application_reason" label="申请理由" min-width="200">
              <template #default="scope">
                <span v-if="scope.row.application_reason">{{ scope.row.application_reason }}</span>
                <span v-else class="no-reason">无申请理由</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="申请状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getApplicationStatusType(scope.row.status)" size="small" effect="dark">
                  {{ getApplicationStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="申请时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.create_time) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 申请列表为空时的提示 -->
      <div v-else class="no-data-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3 class="section-title">KOL申请列表</h3>
        </div>
        <div class="no-data">
          <el-empty description="暂无待处理的申请" />
        </div>
      </div>

      <!-- 邀请列表 -->
      <div v-if="invitations.length > 0" class="invitations-section">
        <div class="section-header">
          <el-icon class="section-icon"><User /></el-icon>
          <h3 class="section-title">已邀请的KOL列表</h3>
          <el-tag type="success" size="small">{{ invitations.length }} 个已接受邀请</el-tag>
        </div>
        
        <div class="invitations-list">
          <el-table 
            :data="invitations" 
            style="width: 100%" 
            class="invitations-table"
            @row-click="handleRowClick"
            :row-class-name="getRowClassName"
          >
            <el-table-column type="radio" width="50">
              <template #default="scope">
                <el-radio 
                  v-model="selectedKolId" 
                  :label="scope.row.kol_id"
                  @change="handleKolSelect(scope.row.kol_id)"
                >
                  &nbsp;
                </el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="kol_username" label="KOL用户名" width="120" />
            <el-table-column prop="message" label="邀请留言" min-width="200">
              <template #default="scope">
                <span v-if="scope.row.message">{{ scope.row.message }}</span>
                <span v-else class="no-message">无留言</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getInvitationStatusType(scope.row.status)" size="small">
                  {{ getInvitationStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="邀请时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.create_time) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 邀请列表为空时的提示 -->
      <div v-else class="no-data-section">
        <div class="section-header">
          <el-icon class="section-icon"><User /></el-icon>
          <h3 class="section-title">已邀请的KOL列表</h3>
        </div>
        <div class="no-data">
          <el-empty description="暂无已接受的邀请" />
        </div>
      </div>

      <!-- 无申请和邀请的情况 -->
      <div v-if="applications.length === 0 && invitations.length === 0" class="no-data-section">
        <div class="no-data">
          <el-empty description="暂无可分配的KOL，请先邀请KOL或等待KOL申请" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="!canConfirm"
          :loading="loading"
        >
          确认分配 ({{ canConfirm ? '可用' : '禁用' }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Document } from '@element-plus/icons-vue'
import apiService from '@/utils/api'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: [Number, String],
    required: true
  }
})

const emit = defineEmits(['update:visible', 'close', 'kol-selected'])

const applications = ref([])
const invitations = ref([])
const selectedKolId = ref(null)
const manualKolId = ref('')
const loading = ref(false)

// 计算是否可以确认
const canConfirm = computed(() => {
  const hasSelectedKol = selectedKolId.value !== null && selectedKolId.value !== undefined
  const hasManualInput = manualKolId.value && manualKolId.value.trim() !== ''
  console.log('canConfirm debug:', { selectedKolId: selectedKolId.value, manualKolId: manualKolId.value, hasSelectedKol, hasManualInput })
  return hasSelectedKol || hasManualInput
})

// 获取邀请和申请列表
const fetchInvitationsAndApplications = async () => {
  if (!props.taskId) return

  loading.value = true
  try {
    const response = await apiService.getTaskInvitationsAndApplications(props.taskId)
    
    // 过滤申请列表：只显示状态为"待处理"的申请
    const allApplications = response.data.applications || []
    applications.value = allApplications.filter(app => app.status === 'pending')
    
    // 过滤邀请列表：只显示状态为"已接受"的邀请
    const allInvitations = response.data.invitations || []
    invitations.value = allInvitations.filter(inv => inv.status === 'accepted')
    
    console.log('API response:', response)
    console.log('All applications:', allApplications.length)
    console.log('Filtered applications (pending):', applications.value.length)
    console.log('All invitations:', allInvitations.length)
    console.log('Filtered invitations (accepted):', invitations.value.length)
  } catch (error) {
    console.error('获取邀请和申请列表失败:', error)
    ElMessage.error('获取邀请和申请列表失败')
  } finally {
    loading.value = false
  }
}

// 处理KOL选择
const handleKolSelect = (kolId) => {
  console.log('handleKolSelect called with kolId:', kolId)
  selectedKolId.value = kolId
  manualKolId.value = '' // 清空手动输入
  console.log('selectedKolId updated to:', selectedKolId.value)
}

// 处理行点击
const handleRowClick = (row) => {
  handleKolSelect(row.kol_id)
}

// 获取行样式类名
const getRowClassName = ({ row }) => {
  return row.kol_id === selectedKolId.value ? 'selected-row' : ''
}

// 邀请状态标签映射
const getInvitationStatusLabel = (status) => {
  const statusMap = {
    'pending': '待响应',
    'accepted': '已接受',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 邀请状态类型映射
const getInvitationStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'accepted': 'success',
    'rejected': 'danger',
    'expired': 'info'
  }
  return typeMap[status] || 'info'
}

// 申请状态标签映射
const getApplicationStatusLabel = (status) => {
  const statusMap = {
    'pending': '待处理',
    'accepted': '已接受',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 申请状态类型映射
const getApplicationStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'accepted': 'success',
    'rejected': 'danger',
    'expired': 'info'
  }
  return typeMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 确认分配
const handleConfirm = () => {
  console.log('handleConfirm called')
  console.log('selectedKolId:', selectedKolId.value)
  console.log('manualKolId:', manualKolId.value)
  
  const kolId = selectedKolId.value || parseInt(manualKolId.value)
  console.log('final kolId:', kolId)
  
  if (!kolId) {
    ElMessage.warning('请选择一个KOL或输入KOL ID')
    return
  }
  
  console.log('emitting kol-selected with kolId:', kolId)
  emit('kol-selected', kolId)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  selectedKolId.value = null
  manualKolId.value = ''
  emit('update:visible', false)
  emit('close')
}

// 监听visible变化，当显示时获取数据
watch(() => props.visible, (newVal) => {
  if (newVal && props.taskId) {
    fetchInvitationsAndApplications()
  }
})
</script>

<style scoped>
.kol-selector-container {
  color: #fff;
}

.section {
  margin-bottom: 32px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #444;
}

.section-icon {
  color: #409EFF;
  font-size: 18px;
  margin-right: 8px;
}

.section-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  flex: 1;
}

.applications-section,
.invitations-section {
  margin-bottom: 24px;
}

.no-data-section {
  margin-bottom: 24px;
}

.no-data {
  padding: 40px;
  text-align: center;
}

.manual-input {
  margin-top: 16px;
  text-align: center;
}

.selection-hint {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
  padding: 16px;
  background: #1a1a1a;
  border-top: 1px solid #333;
}

.dialog-footer .el-button {
  margin-left: 8px;
  min-width: 80px;
}

.dialog-footer .el-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 表格样式 */
:deep(.applications-table),
:deep(.invitations-table) {
  background: #1a1a1a !important;
  border: 1px solid #333 !important;
  border-radius: 8px;
}

:deep(.applications-table .el-table__cell),
:deep(.invitations-table .el-table__cell) {
  background: #1a1a1a !important;
  color: #fff !important;
  border-color: #333 !important;
}

:deep(.applications-table .el-table th),
:deep(.invitations-table .el-table th) {
  background: #333 !important;
  color: #fff !important;
  border-color: #444 !important;
}

:deep(.applications-table .el-table--striped .el-table__body tr.el-table__row--striped td),
:deep(.invitations-table .el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #222 !important;
}

:deep(.applications-table .el-table--enable-row-hover .el-table__body tr:hover > td),
:deep(.invitations-table .el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: #2a2a2a !important;
}

:deep(.selected-row) {
  background: #409EFF !important;
  color: #fff !important;
}

:deep(.selected-row td) {
  background: #409EFF !important;
  color: #fff !important;
}

.no-message,
.no-reason {
  color: #999;
  font-style: italic;
}

/* 状态标签样式 */
:deep(.applications-table .el-tag),
:deep(.invitations-table .el-tag) {
  font-weight: 500;
  border-radius: 4px;
}

:deep(.applications-table .el-tag--warning),
:deep(.invitations-table .el-tag--warning) {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

:deep(.applications-table .el-tag--success),
:deep(.invitations-table .el-tag--success) {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

:deep(.applications-table .el-tag--danger),
:deep(.invitations-table .el-tag--danger) {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

:deep(.applications-table .el-tag--info),
:deep(.invitations-table .el-tag--info) {
  background-color: #f4f4f5;
  border-color: #909399;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style> 