<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="task" class="task-operation-dialog">
      <!-- 查看详情 -->
      <div v-if="operation === 'view'" class="view-operation">
        <TaskDetailView :task="task" />
      </div>

      <!-- 接受邀请 -->
      <div v-else-if="operation === 'accept'" class="accept-operation">
        <div class="task-summary">
          <h3>任务信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">任务名称:</span>
              <span class="value">{{ task.task_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">基础报酬:</span>
              <span class="value">${{ task.base_reward }}</span>
            </div>
            <div class="info-item">
              <span class="label">截止时间:</span>
              <span class="value">{{ formatDate(task.end_date) }}</span>
            </div>
          </div>
        </div>
        
        <el-alert
          title="确认接受邀请"
          type="success"
          description="接受后任务将分配给您，请按时完成任务要求"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 拒绝邀请 -->
      <div v-else-if="operation === 'reject'" class="reject-operation">
        <div class="task-summary">
          <h3>任务信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">任务名称:</span>
              <span class="value">{{ task.task_name }}</span>
            </div>
          </div>
        </div>
        
        <el-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef" label-position="top">
          <el-form-item label="拒绝理由" prop="reason">
            <el-input
              v-model="rejectForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请填写拒绝理由（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 提交内容 -->
      <div v-else-if="operation === 'submit'" class="submit-operation">
        <div class="task-summary">
          <h3>任务信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">任务名称:</span>
              <span class="value">{{ task.task_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">任务要求:</span>
              <p class="value description">{{ task.description }}</p>
            </div>
          </div>
        </div>
        
        <el-form :model="submitForm" :rules="submitRules" ref="submitFormRef" label-position="top">
          <el-form-item label="提交链接" prop="links">
            <el-input
              v-model="submitForm.links"
              type="textarea"
              :rows="3"
              placeholder="请输入推广内容的链接，多个链接请换行分隔"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="内容说明" prop="content">
            <el-input
              v-model="submitForm.content"
              type="textarea"
              :rows="4"
              placeholder="请简要说明您的推广内容和预期效果"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        
        <el-button 
          v-if="operation === 'accept'" 
          type="success" 
          @click="confirmAccept"
          :loading="submitting"
        >
          确认接受
        </el-button>
        
        <el-button 
          v-else-if="operation === 'reject'" 
          type="danger" 
          @click="confirmReject"
          :loading="submitting"
        >
          确认拒绝
        </el-button>
        
        <el-button 
          v-else-if="operation === 'submit'" 
          type="primary" 
          @click="confirmSubmit"
          :loading="submitting"
        >
          提交内容
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'
import TaskDetailView from './TaskDetailView.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  },
  operation: {
    type: String,
    default: 'view' // view, accept, reject, submit
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const submitting = ref(false)
const rejectFormRef = ref()
const submitFormRef = ref()

const rejectForm = reactive({
  reason: ''
})

const submitForm = reactive({
  links: '',
  content: ''
})

const rejectRules = {
  // 拒绝理由是可选的
}

const submitRules = {
  links: [
    { required: true, message: '请输入推广链接', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请填写内容说明', trigger: 'blur' },
    { min: 10, message: '内容说明至少10字', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    rejectForm.reason = ''
    submitForm.links = ''
    submitForm.content = ''
    
    // 清除验证
    if (rejectFormRef.value) {
      rejectFormRef.value.clearValidate()
    }
    if (submitFormRef.value) {
      submitFormRef.value.clearValidate()
    }
  }
})

// 方法
function getDialogTitle() {
  const titles = {
    'view': '任务详情',
    'accept': '接受邀请',
    'reject': '拒绝邀请',
    'submit': '提交内容'
  }
  return titles[props.operation] || '任务操作'
}

async function confirmAccept() {
  try {
    submitting.value = true
    
    await apiService.respondKolInvitations({
      invitation_id: props.task.invitation_id,
      action: 'accept'
    })
    
    ElMessage.success('邀请已接受')
    emit('success')
    
  } catch (error) {
    console.error('接受邀请失败:', error)
    ElMessage.error(error.response?.data?.detail || '接受邀请失败')
  } finally {
    submitting.value = false
  }
}

async function confirmReject() {
  try {
    submitting.value = true
    
    await apiService.respondKolInvitations({
      invitation_id: props.task.invitation_id,
      action: 'reject',
      reason: rejectForm.reason
    })
    
    ElMessage.success('邀请已拒绝')
    emit('success')
    
  } catch (error) {
    console.error('拒绝邀请失败:', error)
    ElMessage.error(error.response?.data?.detail || '拒绝邀请失败')
  } finally {
    submitting.value = false
  }
}

async function confirmSubmit() {
  if (!submitFormRef.value) return
  
  try {
    const valid = await submitFormRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
          await apiService.submitKolTaskContent({
      task_id: props.task.id,
      published_links: submitForm.links,
      draft_content: submitForm.content
    })
    
    ElMessage.success('内容提交成功')
    emit('success')
    
  } catch (error) {
    console.error('提交内容失败:', error)
    ElMessage.error(error.response?.data?.detail || '提交内容失败')
  } finally {
    submitting.value = false
  }
}

function handleClose() {
  if (submitting.value) return
  emit('update:modelValue', false)
}

// 工具函数
function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.task-operation-dialog {
  color: #fff;
}

.task-summary {
  background: #23242a;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.task-summary h3 {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  border-bottom: 1px solid #555;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.label {
  min-width: 80px;
  font-weight: bold;
  color: #aaa;
  margin-right: 12px;
  flex-shrink: 0;
}

.value {
  color: #fff;
  flex: 1;
}

.description {
  white-space: pre-wrap;
  line-height: 1.5;
  margin: 0;
  grid-column: 1 / -1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog) {
  background: #181818;
  border: 1px solid #555;
}

:deep(.el-dialog__header) {
  background: #23242a;
  border-bottom: 1px solid #555;
  padding: 16px 20px;
}

:deep(.el-dialog__title) {
  color: #fff;
  font-weight: bold;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #aaa;
}

:deep(.el-dialog__body) {
  background: #181818;
  color: #fff;
  padding: 20px;
}

:deep(.el-form-item__label) {
  color: #fff !important;
  font-weight: bold;
}

:deep(.el-textarea__inner) {
  background: #23242a !important;
  border-color: #555 !important;
  color: #fff !important;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409EFF !important;
}

:deep(.el-textarea__inner::placeholder) {
  color: #888 !important;
}

:deep(.el-input__count) {
  background: transparent !important;
  color: #aaa !important;
}

:deep(.el-alert) {
  background: #23242a !important;
  border-color: #555 !important;
  margin-bottom: 20px;
}

:deep(.el-alert__title) {
  color: #fff !important;
}

:deep(.el-alert__content) {
  color: #ccc !important;
}

:deep(.el-alert__icon) {
  color: #67C23A !important;
}

:deep(.el-button) {
  border-color: #555 !important;
}

:deep(.el-button--primary) {
  background: #409EFF !important;
  border-color: #409EFF !important;
}

:deep(.el-button--success) {
  background: #67C23A !important;
  border-color: #67C23A !important;
}

:deep(.el-button--danger) {
  background: #F56C6C !important;
  border-color: #F56C6C !important;
}
</style> 