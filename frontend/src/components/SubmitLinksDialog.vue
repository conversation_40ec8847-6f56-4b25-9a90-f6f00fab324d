<template>
  <el-dialog v-model="visible" title="🔗 提交发布链接" width="600px">
    <div>
      <div style="background: #f8f9fa; padding: 16px; margin-bottom: 20px; border-radius: 6px;">
        <h4 style="margin: 0 0 8px 0;">{{ taskName }}</h4>
        <p style="margin: 0; color: #666;">{{ taskDesc }}</p>
      </div>

      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px;">发布平台:</label>
        <el-select v-model="platform" placeholder="请选择发布平台" style="width: 100%">
          <el-option label="🐦 Twitter" value="twitter" />
        </el-select>
      </div>

      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px;">发布链接:</label>
        <div v-for="(link, index) in links" :key="'input_' + index" style="display: flex; margin-bottom: 8px;">
          <el-input
            v-model="links[index]"
            placeholder="请输入发布内容的链接"
            style="flex: 1; margin-right: 8px;"
          />
          <el-button v-if="links.length > 1" @click="removeLink(index)" size="small">
            删除
          </el-button>
        </div>
        <el-button @click="addLink" size="small" :disabled="links.length >= 5">
          + 添加链接
        </el-button>
      </div>

      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px;">发布时间:</label>
        <el-date-picker
          v-model="publishTime"
          type="datetime"
          placeholder="请选择发布时间"
          style="width: 100%"
        />
      </div>

      <div style="margin-bottom: 20px;">
        <label style="display: block; margin-bottom: 8px;">备注说明:</label>
        <el-input
          v-model="notes"
          type="textarea"
          :rows="3"
          placeholder="可以添加一些关于发布内容的说明（可选）"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit" :loading="loading">
        提交链接
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'

const props = defineProps({
  modelValue: Boolean,
  task: Object
})

const emit = defineEmits(['update:modelValue', 'success'])

// 基础数据
const platform = ref('twitter')
const links = ref([''])
const publishTime = ref('')
const notes = ref('')
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const taskName = computed(() => {
  return props.task?.task_name || '任务名称'
})

const taskDesc = computed(() => {
  return props.task?.description || '任务描述'
})

// 方法
function addLink() {
  if (links.value.length < 5) {
    links.value.push('')
  }
}

function removeLink(index) {
  if (links.value.length > 1) {
    links.value.splice(index, 1)
  }
}

function reset() {
  platform.value = 'twitter'
  links.value = ['']
  publishTime.value = ''
  notes.value = ''
}

function close() {
  visible.value = false
  reset()
}

async function submit() {
  // 验证
  if (!platform.value) {
    ElMessage.warning('请选择发布平台')
    return
  }

  const validLinks = links.value.filter(link => link.trim())
  if (validLinks.length === 0) {
    ElMessage.warning('请至少添加一个发布链接')
    return
  }

  loading.value = true

  try {
    const data = {
      platform: platform.value,
      links: validLinks,
      publish_time: publishTime.value || null,
      notes: notes.value || null
    }

    const taskId = props.task?.id || props.task?.task_id
    await apiService.submitTaskPublishLinks(taskId, data)

    ElMessage.success('发布链接提交成功！')
    emit('success')
    close()

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 监听
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    reset()
  }
})
</script>
