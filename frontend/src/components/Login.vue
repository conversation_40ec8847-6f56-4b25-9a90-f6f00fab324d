<template>
  <div class="login">
    <div class="login-slider">
      <div class="logo">
        <img src="../assets/logo.png" />
        <strong>Inflink</strong>
      </div>
      <div class="desc">
        <h1>Web3营销，从这里开始</h1>
        <h2>精准触达用户，助力品牌腾飞</h2>
        <img src="../assets/kol.png" />
      </div>
    </div>
    <div class="main">
      <div class="login-box">
        <p class="title">登录</p>
        <el-form @submit.prevent="onLogin" :model="loginForm" :rules="loginRules" ref="loginFormRef">
          <el-form-item prop="username">
            <label>用户名</label>
            <el-input v-model="loginForm.username" placeholder="admin" clearable class="login-input" />
          </el-form-item>
          <el-form-item prop="password">
            <label>密码</label>
            <el-input v-model="loginForm.password" placeholder="123456" show-password clearable class="login-input" />
          </el-form-item>
          <el-form-item prop="user_type">
            <label>用户类型</label>
            <el-select v-model="loginForm.user_type" placeholder="请选择用户类型" class="login-input">
              <el-option label="商户" value="merchant" />
              <el-option label="KOL主播" value="kol" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onLogin" class="submit">登录</el-button>
          </el-form-item>
        </el-form>
        <div class="register-link">
          <span>还没有账户？</span>
          <span class="link" @click="openRegister">注册新用户</span>
        </div>
        <Register ref="registerRef" @registration-complete="handleRegistrationComplete" />
        <KOLRegister ref="kolRegisterRef" @registration-complete="handleRegistrationComplete" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import Register from './Register.vue'
import KOLRegister from './KOLRegister.vue'
import { useUserStore } from '@/store/user'
import apiService from '@/utils/api'
import { extractLoginErrorMessage } from '@/utils/errorHandler'

const emit = defineEmits(['login-success'])
const userStore = useUserStore()
const loginForm = ref({ username: '', password: '', user_type: '' })
const loginFormRef = ref(null)
const registerRef = ref()
const kolRegisterRef = ref()

// 登录表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '用户名长度应在1-50个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, max: 100, message: '密码长度应在1-100个字符之间', trigger: 'blur' }
  ],
  user_type: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ]
}

function openRegister() {
  if (loginForm.value.user_type === 'kol') {
    kolRegisterRef.value.open()
  } else {
    registerRef.value.open()
  }
}

function handleRegistrationComplete(userData) {
  // 注册完成后自动登录
  if (userData) {
    // 🔧 现在所有注册都返回包含 Token 的完整登录响应
    console.log('注册完成，自动登录成功')
    userStore.setToken(userData.access_token)
    userStore.setUserInfo(userData.user)
    emit('login-success', userData.user, true)
  } else {
    // 如果没有用户数据，显示成功消息并提示登录
    ElMessage.success('注册完成！请使用您的账号密码登录')
  }
}

async function onLogin() {
  // 验证表单
  try {
    await loginFormRef.value.validate()
  } catch (error) {
    return // 验证失败，不继续执行
  }

  if (loginForm.value.username === 'admin' && loginForm.value.password === '123456') {
    const adminUser = { username: 'admin', id: 0, user_type: 'merchant' }
    // 为admin用户也设置一个假token，避免API调用失败
    userStore.setToken('admin-token')
    userStore.setUserInfo(adminUser)
    emit('login-success', adminUser)
    ElMessage.success('管理员登录成功！欢迎回来')
    return
  }
  try {
    const res = await apiService.login({
      username: loginForm.value.username,
      password: loginForm.value.password,
      user_type: loginForm.value.user_type
    })

    console.log('Login response:', res.data) // 调试日志

    // 确保token存在
    if (!res.data.access_token) {
      throw new Error('服务器未返回访问令牌')
    }

    // 使用用户存储保存用户信息和token
    userStore.setToken(res.data.access_token)
    userStore.setUserInfo(res.data.user)

    console.log('Token saved:', res.data.access_token) // 调试日志

    emit('login-success', res.data.user)
    ElMessage.success('登录成功！欢迎回来')
  } catch (e) {
    // 使用统一的错误处理工具
    ElMessage.error(extractLoginErrorMessage(e))
  }
}
</script>

<style scoped>
.login {
  width: 100vw;
  height: 100vh;
  display: flex;
  background: #fff;
  .login-slider {
    background: #fff;
    width: 640px;
    color: #444;
    display: flex;
    flex-direction: column;
    box-shadow: #ddd 2px 1px 4px;
    background: #f6f9f8;
    .logo {
      padding: 10px;
      display: flex;
      img {
        width: 48px;
        height: 48px;
      }
      strong {
        line-height: 48px;
        padding-left: 12px;
        font-size: 20px;
        color: #409eff;
      }
    }
    .desc {
      text-align: center;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      h1 {
        font-size: 22px;
        line-height: 1.5;
        padding: 0;
        margin: 0;
        font-weight: normal;
      }
      h2 {
        font-size: 16px;
        font-weight: normal;
      }
      img {
        width: 480px;
        margin-top: 20px;
      }
    }
  }
  .main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-size: 24px;
      color: #444;
    }
    .login-box {
      width: 420px;
      label {
        color: #999;
      }
      .login-input, :deep(.el-select__wrapper) {
        height: 42px!important;
        line-height: 42px!important;
      }
      .submit {
        width: 100%;
        height: 42px;
        margin-top: 12px;
      }
      .register-link {
        padding-top: 10px;
        text-align: right;
        font-size: 14px;
        color: #999;
        .link {
          color: #444;
          cursor: pointer;
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
