<template>
  <div class="publish-links-dialog">
    <div v-if="!links || links.length === 0" class="no-links">
      <el-empty description="暂无KOL提交的链接" />
    </div>
    <div v-else class="links-list">
      <div v-for="(link, index) in links" :key="index" class="link-item">
        <div class="link-header">
          <span class="platform">
            <el-icon>
              <component :is="getPlatformIcon(detectPlatformFromUrl(typeof link === 'string' ? link : link.url))" />
            </el-icon>
            {{ getPlatformLabel(detectPlatformFromUrl(typeof link === 'string' ? link : link.url)) }}
          </span>
          <span class="submit-time">
            提交时间: {{ formatDate(typeof link === 'string' ? new Date().toISOString() : link.submit_time) }}
          </span>
        </div>
        <div class="link-content">
          <el-link type="primary" :href="typeof link === 'string' ? link : link.url" target="_blank" class="link-url">
            {{ typeof link === 'string' ? link : link.url }}
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link, Share, VideoCamera, Document, Picture } from '@element-plus/icons-vue'

const props = defineProps({
  links: {
    type: Array,
    required: true,
    default: () => []
  }
})

// 添加调试信息
console.log('PublishLinksDialog props:', props)
console.log('Links data:', props.links)

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleString()
}

const getPlatformIcon = (platform) => {
  const iconMap = {
    'twitter': Share,      // 使用 Share 图标代替 Twitter
    'youtube': VideoCamera, // 使用 VideoCamera 图标代替 Youtube
    'medium': Document,
    'instagram': Picture,   // 使用 Picture 图标代替 Instagram
    'other': Link
  }
  return iconMap[platform?.toLowerCase()] || Link
}

const getPlatformLabel = (platform) => {
  const labelMap = {
    'twitter': 'Twitter/X',
    'youtube': 'YouTube',
    'medium': 'Medium',
    'instagram': 'Instagram',
    'other': '其他平台'
  }
  return labelMap[platform?.toLowerCase()] || '未知平台'
}

// 根据URL自动识别平台
const detectPlatformFromUrl = (url) => {
  console.log('Detecting platform for URL:', url)
  if (!url) return 'other'

  const urlLower = url.toLowerCase()
  console.log('URL lower case:', urlLower)

  if (urlLower.includes('twitter') || urlLower.includes('x.com')) {
    console.log('Detected as Twitter')
    return 'twitter'
  } else if (urlLower.includes('youtube') || urlLower.includes('youtu.be')) {
    console.log('Detected as YouTube')
    return 'youtube'
  } else if (urlLower.includes('medium')) {
    console.log('Detected as Medium')
    return 'medium'
  } else if (urlLower.includes('instagram')) {
    console.log('Detected as Instagram')
    return 'instagram'
  }
  console.log('Detected as Other')
  return 'other'
}
</script>

<style scoped>
.publish-links-dialog {
  padding: 20px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.no-links {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.links-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.link-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 16px;
  background: var(--el-bg-color);
  transition: all 0.3s ease;
}

.link-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.link-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.platform {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.platform .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.submit-time {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.link-content {
  word-break: break-all;
  padding: 8px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.link-url {
  font-size: 14px;
  line-height: 1.4;
}
</style>
