<template>
  <div>
    <ul>
      <li v-for="msg in messages" :key="msg.id" class="message-item">
        <div class="message-content">
          <span :style="{color: msg.is_read ? '#888' : '#409EFF'}">{{ msg.content }}</span>
          <div class="message-time">{{ formatTime(msg.create_time) }}</div>
        </div>
        <el-button v-if="!msg.is_read == 1" size="mini" @click="markRead(msg.id)">标记已读</el-button>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useMessageStore } from '../store/message'

const messageStore = useMessageStore()
const messages = computed(() => messageStore.messages)
const emit = defineEmits(['read'])

const markRead = async (msgId) => {
  await messageStore.markAsRead(msgId)
  // markAsRead 内部已经调用了 fetchUnreadCount()，不需要再通知父组件
  // emit('read') // 移除重复刷新
}

const formatTime = (time) => {
  if (!time) return ''
  
  try {
    const date = new Date(time)
    const now = new Date()
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string:', time)
      return '未知时间'
    }
    
    const diff = now.getTime() - date.getTime()
    
    // 一小时内
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000)
      if (minutes === 0) {
        return '刚刚'
      }
      return `${minutes} 分钟前`
    }
    
    // 24小时内
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000)
      return `${hours} 小时前`
    }
    
    // 7天内
    if (diff < 604800000) {
      const days = Math.floor(diff / 86400000)
      return `${days} 天前`
    }
    
    // 超过7天，显示具体日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.error('Time formatting error:', error, 'for time string:', time)
    return '时间格式错误'
  }
}

// 移除 onMounted 中的数据获取，改为在父组件打开弹窗时获取
// onMounted(() => {
//   messageStore.fetchUnreadCount()
// })
</script>