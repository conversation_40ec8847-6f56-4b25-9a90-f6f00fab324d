<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传官方素材"
    width="600px"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab">
      <!-- 文件上传标签页 -->
      <el-tab-pane label="文件上传" name="upload">
        <div class="upload-section">
          <el-upload
            class="material-uploader"
            :http-request="customUpload"
            :on-progress="handleProgress"
            :before-upload="beforeUpload"
            :file-list="fileList"
            multiple
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持图片(jpg, png, gif)、视频(mp4, mov)等格式，单个文件不超过50MB
              </div>
            </template>
          </el-upload>
        </div>
      </el-tab-pane>

      <!-- 链接输入标签页 -->
      <el-tab-pane label="链接输入" name="link">
        <div class="link-section">
          <el-input
            v-model="linkInput"
            type="textarea"
            :rows="4"
            placeholder="请输入链接，多个链接请用回车分隔"
          />
          <div class="link-tip">
            每行一个链接，支持图片、视频等媒体文件链接
          </div>
          <el-button type="primary" @click="processLinks" style="margin-top: 12px;">
            添加链接
          </el-button>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 已添加的素材预览 -->
    <div class="materials-preview" v-if="materials.length > 0">
      <h4>已添加素材</h4>
      <div class="preview-list">
        <div v-for="(item, index) in materials" :key="index" class="preview-item">
          <!-- 图片预览 -->
          <div v-if="item.type === 'image'" class="preview-image">
            <el-image 
              :src="item.url" 
              :preview-src-list="[item.url]"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>

          <!-- 视频预览 -->
          <div v-else-if="item.type === 'video'" class="preview-video">
            <video 
              :src="item.url" 
              controls 
              preload="metadata"
              class="video-player"
            ></video>
          </div>

          <!-- 链接预览 -->
          <div v-else class="preview-link">
            <el-link :href="item.url" target="_blank" type="primary">
              {{ item.name || item.url }}
            </el-link>
          </div>

          <!-- 删除按钮 -->
          <el-button
            type="danger"
            size="small"
            circle
            class="delete-btn"
            @click="removeMaterial(index)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">
          确认添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture, Delete } from '@element-plus/icons-vue'
import config from '@/config'
import apiService from '@/utils/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'materials-confirmed'])

// 响应式数据
const activeTab = ref('upload')
const materials = ref([])
const fileList = ref([])
const linkInput = ref('')
const submitting = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 自定义上传方法，使用 api.js 的拦截器
const customUpload = async (options) => {
  try {
    const formData = new FormData()
    formData.append('file', options.file)
    
    const response = await apiService.uploadFile(formData)
    console.log('Material upload response:', response.data)
    
    // 直接调用处理函数
    handleUploadSuccess(response.data, options.file)
  } catch (err) {
    console.error('Material upload error:', err)
    handleUploadError(err)
  }
}

// 方法
const handleClose = () => {
  dialogVisible.value = false
  // 清空数据
  materials.value = []
  fileList.value = []
  linkInput.value = ''
  activeTab.value = 'upload'
}

const handleConfirm = () => {
  console.log('handleConfirm called, materials:', materials.value)
  if (materials.value.length === 0) {
    ElMessage.warning('请至少添加一个素材')
    return
  }
  
  emit('materials-confirmed', [...materials.value])
  handleClose()
}

const handleUploadSuccess = (responseData, file) => {
  console.log('handleUploadSuccess called with:', responseData)
  
  if (responseData && responseData.success && responseData.url) {
    // 根据文件类型或URL后缀判断类型
    let type = 'link'
    if (file && file.type) {
      type = file.type.startsWith('image/') ? 'image' : 
             file.type.startsWith('video/') ? 'video' : 'link'
    } else if (responseData.url) {
      type = responseData.url.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image' :
             responseData.url.match(/\.(mp4|mov)$/i) ? 'video' : 'link'
    }
    
    materials.value.push({
      type,
      url: responseData.url,
      name: responseData.original_filename || file?.name || '未命名文件',
      submit_time: new Date().toISOString()
    })
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error('文件上传失败：响应数据格式错误')
  }
}

const handleUploadError = (error) => {
  console.error('上传失败:', error)
  // 处理不同的错误类型
  if (error?.response?.data?.detail) {
    ElMessage.error(`文件上传失败：${error.response.data.detail}`)
  } else if (error?.message) {
    ElMessage.error(`文件上传失败：${error.message}`)
  } else {
    ElMessage.error('文件上传失败，请重试')
  }
}

const handleProgress = (event, file) => {
  console.log('上传进度:', event.percent)
}

const beforeUpload = (file) => {
  const isValidType = file.type.startsWith('image/') || file.type.startsWith('video/')
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isValidType) {
    ElMessage.error('只能上传图片或视频文件!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB!')
    return false
  }
  return true
}

const processLinks = () => {
  console.log('processLinks called, linkInput:', linkInput.value)
  if (!linkInput.value.trim()) {
    ElMessage.warning('请输入链接')
    return
  }

  const links = linkInput.value.split('\n').filter(link => link.trim())
  console.log('Processing links:', links)
  
  links.forEach(link => {
    const type = link.match(/\.(jpg|jpeg|png|gif)$/i) ? 'image' :
                link.match(/\.(mp4|mov)$/i) ? 'video' : 'link'
    
    console.log('Adding link:', { type, url: link.trim() })

    materials.value.push({
      type,
      url: link.trim(),
      submit_time: new Date().toISOString()
    })
  })

  console.log('Materials after adding links:', materials.value)
  linkInput.value = ''
  ElMessage.success('链接添加成功')
}

const removeMaterial = (index) => {
  materials.value.splice(index, 1)
}
</script>

<style scoped>
.material-upload-dialog {
  padding: 20px;
}

.upload-section,
.link-section {
  margin-bottom: 20px;
}

.material-uploader {
  width: 100%;
}

.link-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.materials-preview {
  margin-top: 20px;
}

.materials-preview h4 {
  margin-bottom: 12px;
  color: #333;
}

.preview-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.preview-item {
  position: relative;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100px;
}

.preview-image :deep(.el-image) {
  width: 100%;
  height: 100px;
}

.video-player {
  width: 100%;
  height: 100px;
  object-fit: cover;
}

.preview-link {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

.delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  background: #f0f0f0;
  color: #999;
}

.dialog-footer {
  text-align: right;
}
</style> 