<template>
  <el-dialog
    model-value="true"
    title="输入渠道码"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="channel-code-dialog">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="渠道码" prop="channel_code">
          <el-input
            v-model="form.channel_code"
            placeholder="请输入渠道码"
            clearable
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="可选：添加渠道码相关备注"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  taskId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['close', 'confirm'])

const formRef = ref()
const loading = ref(false)

const form = reactive({
  channel_code: '',
  remark: ''
})

const rules = {
  channel_code: [
    { required: true, message: '请输入渠道码', trigger: 'blur' },
    { min: 1, max: 50, message: '渠道码长度在1到50个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 200, message: '备注长度不能超过200个字符', trigger: 'blur' }
  ]
}

const handleClose = () => {
  emit('close')
}

const handleConfirm = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 触发确认事件，传递渠道码数据
    emit('confirm', {
      taskId: props.taskId,
      channel_code: form.channel_code,
      remark: form.remark
    })
    
    // 重置表单
    form.channel_code = ''
    form.remark = ''
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时重置表单
onMounted(() => {
  form.channel_code = ''
  form.remark = ''
})
</script>

<style scoped>
.channel-code-dialog {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}
</style> 