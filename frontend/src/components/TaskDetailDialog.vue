<template>
  <div class="task-detail-dialog">


    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else>
      <!-- 第一部分：任务基本信息 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3 class="section-title">任务基本信息</h3>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">任务名称:</span>
            <span class="value">{{ taskDetail?.task_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务类型:</span>
            <span class="value">{{ getTaskTypeLabel(taskDetail?.task_type) }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务状态:</span>
            <el-tag :type="getStatusType(taskDetail?.task_status)" size="small">
              {{ getStatusLabel(taskDetail?.task_status) }}
            </el-tag>
          </div>



          <!-- KOL邀请与申请列表区域 -->
          <div class="info-item full-width" style="margin-top: 16px;">
            <span class="label">查看KOL邀请与申请列表:</span>
            <div class="status-actions">
              <el-button type="primary" @click="showInvitationsAndApplications" class="action-btn">
                <el-icon><User /></el-icon>
                &nbsp;查看邀请和申请
              </el-button>
            </div>
          </div>

          <!-- 可执行操作按钮 -->
          <div class="info-item full-width" v-if="availableStatuses.length > 0 && taskDetail?.task_status !== 'completed' && taskDetail?.task_status !== 'cancelled'">
            <span class="label">可执行操作:</span>
            <div class="executable-actions">
              <!-- 审核操作按钮 - 只在特定条件下显示 -->
              <div v-if="shouldShowReviewButtons()" class="status-action-item">
                <div class="review-buttons">
                  <el-button
                    type="success"
                    @click="changeTaskStatus('approved')"
                    :loading="statusLoading"
                    size="default"
                    class="status-action-btn"
                  >
                    审核通过
                  </el-button>
                  <el-button
                    type="danger"
                    @click="showRejectDialog"
                    :loading="statusLoading"
                    size="default"
                    class="status-action-btn"
                  >
                    审核拒绝
                  </el-button>
                </div>
                <div class="status-description">
                  {{ getReviewDescription() }}
                </div>
              </div>
              
              <!-- 运行中状态提示 -->
              <div v-if="taskDetail?.task_status === 'running'" class="running-status-notice">
                <el-alert
                  description="该任务正在运行中，KOL正在发布内容。请耐心等待任务完成。"
                  type="info"
                  :closable="false"
                  show-icon
                  class="running-notice"
                />
              </div>

              <!-- 其他状态显示单个按钮 - 排除审核相关状态 -->
              <div v-for="status in getNonReviewStatuses()" :key="status" class="status-action-item">
                <el-tooltip
                  v-if="status === 'assigned' && !hasInvitationsOrApplications"
                  content="没有邀请KOL或者没有KOL申请该任务"
                  placement="top"
                  effect="dark"
                >
                  <el-button
                    :type="getStatusButtonType(status)"
                    @click="changeTaskStatus(status)"
                    :loading="statusLoading"
                    :disabled="status === 'assigned' && !hasInvitationsOrApplications"
                    size="default"
                    class="status-action-btn"
                  >
                    {{ getStatusDisplayName(status) }}
                  </el-button>
                </el-tooltip>
                <el-button
                  v-else
                  :type="getStatusButtonType(status)"
                  @click="changeTaskStatus(status)"
                  :loading="statusLoading"
                  :disabled="false"
                  size="default"
                  class="status-action-btn"
                >
                  {{ getStatusDisplayName(status) }}
                </el-button>
                <div class="status-description">
                  {{ getStatusDescription(status) }}
                </div>
              </div>
            </div>
          </div>

          <!-- KOL提交的资料 - 非草稿状态且有有效提交数据才显示 -->
          <div class="info-item full-width" v-if="hasValidPublishedLinks(taskDetail) && taskDetail?.task_status !== 'draft'">
            <span class="label">KOL提交的资料:</span>
            <div class="kol-submitted-content">
              <div class="submitted-header">
                <span class="submitted-title">🔗 {{ getPlatformLabel(getPublishedPlatform(taskDetail.published_links)) }}</span>
                <span class="submit-time">📅 提交时间：{{ getPublishedTime(taskDetail.published_links) }}</span>
              </div>
              <div class="submitted-links-box">
                <div v-for="(link, index) in getPublishedLinks(taskDetail.published_links)" :key="index" class="submitted-link-item">
                  <span class="link-icon">{{ getPlatformIcon(getPublishedPlatform(taskDetail.published_links)) }}</span>
                  <a :href="link" target="_blank" class="link-url">{{ link }}</a>
                </div>
              </div>
              <div v-if="getPublishedNotes(taskDetail.published_links)" class="submitted-notes">
                <div class="notes-title">💡 提交说明:</div>
                <div class="notes-content">{{ getPublishedNotes(taskDetail.published_links) }}</div>
              </div>
            </div>
          </div>
          <div class="info-item">
            <span class="label">开始日期:</span>
            <span class="value">{{ formatDate(taskDetail?.start_date) }}</span>
          </div>
          <div class="info-item">
            <span class="label">结束日期:</span>
            <span class="value">{{ formatDate(taskDetail?.end_date) }}</span>
          </div>
          <div class="info-item">
            <span class="label">奖励类型:</span>
            <span class="value">{{ getRewardTypeLabel(taskDetail?.reward_type) }}</span>
          </div>

          <!-- 品牌推广模式显示基础奖励 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding'">
            <span class="label">基础奖励:</span>
            <span class="value reward">${{ taskDetail?.base_reward || 0 }}</span>
          </div>

          <!-- 带单返佣模式显示返佣比例 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'commission'">
            <span class="label">返佣比例:</span>
            <span class="value reward">{{ taskDetail?.commission_rate || 0 }}%</span>
          </div>

          <!-- 品牌推广+按转化付费模式显示基础奖励和按转化付费FTT奖励 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding_plus_conversion'">
            <span class="label">基础奖励:</span>
            <span class="value reward">${{ taskDetail?.base_reward || 0 }}</span>
          </div>
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding_plus_conversion'">
            <span class="label">按转化付费FTT:</span>
            <span class="value reward">${{ taskDetail?.performance_rate || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">渠道码:</span>
            <span class="value">{{ taskDetail?.channel_code || '-' }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">任务描述:</span>
            <div class="value description">{{ taskDetail?.description || '-' }}</div>
          </div>

          <!-- 草稿内容展示 -->
          <div class="info-item full-width" v-if="shouldShowDraftContent(taskDetail)">
            <span class="label">草稿内容:</span>
            <div class="draft-content-section">
              <!-- 草稿内容 -->
              <div class="draft-text-box">
                {{ getDraftContentPreview(taskDetail.draft_content, 300) }}
              </div>

              <!-- 创作说明 -->
              <div v-if="getDraftCreationNotes(taskDetail.draft_content)" class="creation-notes-box">
                <div class="notes-title">💡 创作说明:</div>
                <div class="notes-content">{{ getDraftCreationNotes(taskDetail.draft_content) }}</div>
              </div>

              <!-- 素材展示 -->
              <div v-if="getDraftMaterials(taskDetail.draft_content).length > 0" class="materials-box">
                <div class="materials-title">📎 创作素材:</div>
                <div class="materials-preview">
                  <div v-for="(material, index) in getDraftMaterials(taskDetail.draft_content)" :key="index" class="material-item">

                    <!-- 图片预览 -->
                    <div v-if="material.type === 'image'" class="material-image">
                      <el-image
                        :src="material.url"
                        :preview-src-list="[material.url]"
                        fit="cover"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <div>图片加载失败</div>
                          </div>
                        </template>
                      </el-image>
                    </div>

                    <!-- 视频预览 -->
                    <div v-else-if="material.type === 'video'" class="material-video">
                      <video
                        :src="material.url"
                        controls
                        preload="metadata"
                        class="video-player"
                      ></video>
                    </div>

                    <!-- 链接预览 -->
                    <div v-else class="material-link">
                      <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                        <span class="material-icon">{{ getMaterialIcon(material.type) }}</span>
                        {{ material.name || material.url }}
                      </el-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="info-item full-width" v-if="taskDetail?.official_materials">
            <span class="label">官方素材:</span>
            <div class="materials-preview">
              <div v-for="(material, index) in taskDetail.official_materials" :key="index" class="material-item">
                <!-- 图片预览 -->
                <div v-if="material.type === 'image'" class="material-image">
                  <el-image
                    :src="material.url"
                    :preview-src-list="[material.url]"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="material.type === 'video'" class="material-video">
                  <video
                    :src="material.url"
                    controls
                    preload="metadata"
                    class="video-player"
                  ></video>
                </div>

                <!-- 链接预览 -->
                <div v-else class="material-link">
                  <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                    {{ material.name || material.url }}
                  </el-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二部分：统计信息 (data_source为api) -->
      <div class="detail-section"  v-if="taskDetail?.task_status !== 'draft'">
        <div class="section-header">
          <el-icon class="section-icon"><DataLine /></el-icon>
          <h3 class="section-title">统计信息</h3>

        </div>

        <div v-if="platformPosts.length === 0" class="no-data">
          <el-empty description="暂无平台帖子统计数据" />
        </div>
        <div v-else class="stats-grid">
          <div class="stats-card card1">
            <div class="stats-label">总浏览量</div>
            <div class="stats-value view">{{ totalPlatformStats.view_count }}</div>
          </div>
          <div class="stats-card card2">
            <div class="stats-label">总点赞数</div>
            <div class="stats-value like">{{ totalPlatformStats.like_count }}</div>
          </div>
          <div class="stats-card card3">
            <div class="stats-label">总转发数</div>
            <div class="stats-value retweet">{{ totalPlatformStats.retweet_count }}</div>
          </div>
          <div class="stats-card card4">
            <div class="stats-label">总评论数</div>
            <div class="stats-value reply">{{ totalPlatformStats.reply_count }}</div>
          </div>
          <div class="stats-card card5">
            <div class="stats-label">总引用数</div>
            <div class="stats-value quote">{{ totalPlatformStats.quote_count }}</div>
          </div>
          <div class="stats-card card6">
            <div class="stats-label">总收藏数</div>
            <div class="stats-value bookmark">{{ totalPlatformStats.bookmark_count }}</div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div v-if="platformPosts.length > 0" class="stats-table">
          <h4>帖子详细数据</h4>
          <el-table :data="platformPosts" class="stats-data-table" border>
            <el-table-column prop="post_id" label="帖子ID" width="120" />
            <el-table-column prop="post_url" label="帖子链接" min-width="200">
              <template #default="scope">
                <el-link :href="scope.row.post_url" target="_blank" type="primary">
                  {{ scope.row.post_id }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="post_date" label="发布时间" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.post_date) }}
              </template>
            </el-table-column>
            <el-table-column prop="metrics.view_count" label="浏览量" width="100" />
            <el-table-column prop="metrics.like_count" label="点赞数" width="100" />
            <el-table-column prop="metrics.retweet_count" label="转发数" width="100" />
            <el-table-column prop="metrics.quote_count" label="引用数" width="100" />
            <el-table-column prop="metrics.reply_count" label="评论数" width="100" />
            <el-table-column prop="metrics.bookmark_count" label="收藏数" width="100" />
            <el-table-column prop="last_updated" label="最后更新" width="150">
              <template #default="scope">
                {{ formatDateTime(scope.row.last_updated) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

       <!-- 第三部分：渠道统计信息 (按channel_code查询) -->
       <div class="detail-section"  v-if="taskDetail?.task_status !== 'draft'">
         <div class="section-header">
           <el-icon class="section-icon"><Link /></el-icon>
          <h3 class="section-title">渠道统计信息</h3>
        </div>
        <div v-if="channelStats.length === 0" class="no-data">
          <el-empty description="暂无渠道数据" />
        </div>
        <div v-else class="channel-stats">
          <div class="channel-summary">
            <h4>渠道汇总 ({{ taskDetail?.channel_code || '-' }})</h4>
            <div class="stats-grid">
              <div class="stats-card">
                <div class="stats-label">渠道总点击</div>
                <div class="stats-value click">{{ totalChannelStats.total_clicks || 0 }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总注册</div>
                <div class="stats-value register">{{ totalChannelStats.total_registers || 0 }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总FTT</div>
                <div class="stats-value ftt">{{ formatMoney(totalChannelStats.total_ftt || 0) }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总入金</div>
                <div class="stats-value deposit">${{ formatMoney(totalChannelStats.total_deposit || 0) }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总带单</div>
                <div class="stats-value order">${{ formatMoney(totalChannelStats.total_order || 0) }}</div>
              </div>
            </div>
          </div>

          <!-- 渠道详细数据表格 -->
          <div class="stats-table">
            <h4>渠道详细数据</h4>
            <el-table :data="channelStats" style="width: 100%" class="stats-data-table">
              <el-table-column prop="stat_date" label="日期" width="120">
                <template #default="scope">
                  {{ formatDate(scope.row.stat_date) }}
                </template>
              </el-table-column>
              <el-table-column prop="daily_clicks" label="点击量" width="100" />
              <el-table-column prop="daily_registers" label="注册量" width="100" />
              <el-table-column prop="daily_ftt" label="FTT值" width="120">
                <template #default="scope">
                  {{ formatMoney(scope.row.daily_ftt) }}
                </template>
              </el-table-column>
              <el-table-column prop="daily_deposit" label="入金金额" width="120">
                <template #default="scope">
                  ${{ formatMoney(scope.row.daily_deposit) }}
                </template>
              </el-table-column>
              <el-table-column prop="daily_order" label="带单金额" width="120">
                <template #default="scope">
                  ${{ formatMoney(scope.row.daily_order) }}
                </template>
              </el-table-column>
              <el-table-column prop="data_source" label="数据来源" width="100" />
              <el-table-column prop="channel_code" label="渠道码" width="120" />
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态流转对话框 -->
    <el-drawer v-model="statusFlowVisible" title="任务状态管理" size="40%" :before-close="() => statusFlowVisible = false">
      <TaskStatusFlow v-if="taskDetail" :task-id="taskDetail.id" :current-status="taskDetail.task_status"
        @status-changed="handleStatusChanged" />
    </el-drawer>

    <!-- 在组件底部添加PublishLinksDialog -->
    <el-dialog
      v-model="publishLinksVisible"
      title="KOL提交链接"
      width="600px"
      :before-close="() => publishLinksVisible = false"
    >
      <PublishLinksDialog :links="taskDetail?.published_links || []" />
    </el-dialog>

    <!-- 邀请和申请对话框 -->
    <TaskInvitationsDialog
      v-model:visible="invitationsAndApplicationsVisible"
      :task-id="taskDetail?.id"
      :show-actions="true"
      @close="() => invitationsAndApplicationsVisible = false"
    />

    <!-- 审核拒绝对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核拒绝"
      width="500px"
      :before-close="() => rejectDialogVisible = false"
    >
      <div class="reject-dialog-content">
        <p class="reject-tip">请输入拒绝理由：</p>
        <el-input
          v-model="rejectReason"
          type="textarea"
          :rows="4"
          placeholder="请输入详细的拒绝理由..."
          maxlength="500"
          show-word-limit
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="statusLoading">
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watchEffect, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus'
import { Document, DataLine, Link, Setting, Close, Picture, User } from '@element-plus/icons-vue'
import apiService from '@/utils/api'
import TaskStatusFlow from './TaskStatusFlow.vue'
import PublishLinksDialog from './PublishLinksDialog.vue'
import TaskInvitationsDialog from './TaskInvitationsDialog.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

// 立即执行的调试信息
console.log('🎯 TaskDetailDialog script setup executed!')
console.log('Props in script setup:', props)

// 格式化金额函数
const formatMoney = (val) => {
  if (val === null || val === undefined || isNaN(val)) {
    return '0.00'
  }
  return Number(val).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 格式化日期函数
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  
  try {
    const date = new Date(dateStr)
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string:', dateStr)
      return '未知日期'
    }
    
    // 使用中文格式显示日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    console.error('Date formatting error:', error, 'for date string:', dateStr)
    return '日期格式错误'
  }
}

const emit = defineEmits(['close', 'status-changed'])

const loading = ref(false)
const taskDetail = ref(null)
const apiStats = ref([])
const channelStats = ref([])

// 新增：平台帖子统计相关变量
const platformPosts = ref([])

// 状态管理相关状态
const statusFlowVisible = ref(false)

// 添加新的响应式变量
const publishLinksVisible = ref(false)
const invitationsAndApplicationsVisible = ref(false)

// 可执行操作相关状态
const availableStatuses = ref([])
const statusLoading = ref(false)
const hasInvitationsOrApplications = ref(false)
const rejectDialogVisible = ref(false)
const rejectReason = ref('')

// 计算总计数据（API数据）
const totalApiStats = computed(() => {
  if (!Array.isArray(apiStats.value) || apiStats.value.length === 0) {
    return { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 }
  }

  return apiStats.value.reduce((total, item) => {
    return {
      click_count: total.click_count + (item.daily_clicks || 0),
      register_count: total.register_count + (item.daily_registers || 0),
      ftt: total.ftt + (parseFloat(item.daily_ftt) || 0),
      deposit_amount: total.deposit_amount + (parseFloat(item.daily_deposit) || 0)
    }
  }, { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 })
})

// 计算总计数据（渠道数据）
const totalChannelStats = ref({
  total_clicks: 0,
  total_registers: 0,
  total_ftt: 0,
  total_deposit: 0,
  total_order: 0
})

// 新增：计算平台帖子统计信息
const totalPlatformStats = computed(() => {
  if (!Array.isArray(platformPosts.value) || platformPosts.value.length === 0) {
    return {
      view_count: 0,
      like_count: 0,
      retweet_count: 0,
      quote_count: 0,
      reply_count: 0,
      bookmark_count: 0
    }
  }

  console.log('=== 计算总计调试信息 ===')
  console.log('平台帖子数量:', platformPosts.value.length)

  const total = platformPosts.value.reduce((total, post, index) => {
    const metrics = post.metrics || {}
    console.log(`帖子 ${index + 1}:`, {
      post_id: post.post_id,
      metrics: metrics,
      view_count: metrics.view_count || 0,
      like_count: metrics.like_count || 0,
      retweet_count: metrics.retweet_count || 0,
      quote_count: metrics.quote_count || 0,
      reply_count: metrics.reply_count || 0,
      bookmark_count: metrics.bookmark_count || 0
    })

    return {
      view_count: total.view_count + (metrics.view_count || 0),
      like_count: total.like_count + (metrics.like_count || 0),
      retweet_count: total.retweet_count + (metrics.retweet_count || 0),
      quote_count: total.quote_count + (metrics.quote_count || 0),
      reply_count: total.reply_count + (metrics.reply_count || 0),
      bookmark_count: total.bookmark_count + (metrics.bookmark_count || 0)
    }
  }, {
    view_count: 0,
    like_count: 0,
    retweet_count: 0,
    quote_count: 0,
    reply_count: 0,
    bookmark_count: 0
  })

  console.log('计算得到的总计:', total)
  console.log('=== 计算总计调试信息结束 ===')

  return total
})

// 获取任务详情数据
const fetchTaskDetail = async () => {
  loading.value = true
  try {
    console.log('=== fetchTaskDetail Debug ===')
    console.log('Props task:', props.task)
    console.log('Task ID:', props.task.id)
    console.log('Task name:', props.task.task_name)
    console.log('Reward type:', props.task.reward_type)
    console.log('Task ID type:', typeof props.task.id)
    console.log('Task ID value:', props.task.id)

    // 验证taskId是否有效
    if (!props.task.id || props.task.id === undefined || props.task.id === null) {
      console.error('❌ Task ID is invalid:', props.task.id)
      throw new Error('Invalid task ID')
    }

    console.log('✅ Task ID is valid, calling API...')

    // 从API获取完整的任务详情
    const taskDetailResponse = await apiService.getTaskDetail(props.task.id)
    console.log('Task Detail Response:', taskDetailResponse)

    // 确保我们使用response.data
    const taskData = taskDetailResponse.data
    console.log('Task Data before processing:', taskData)
    console.log('Published Links before processing:', taskData.published_links)

    // 🔧 修复：处理published_links字段，正确处理空字符串和有效JSON字符串
    if (taskData.published_links !== null && taskData.published_links !== undefined) {
      try {
        if (typeof taskData.published_links === 'string') {
          // 如果是空字符串，设置为空数组
          if (taskData.published_links.trim() === '') {
            taskData.published_links = []
          } else {
            // 尝试解析JSON字符串
            taskData.published_links = JSON.parse(taskData.published_links)
          }
        }
        console.log('Published Links after parsing:', taskData.published_links)
      } catch (e) {
        console.error('解析published_links失败:', e)
        console.error('原始数据:', taskData.published_links)
        taskData.published_links = []
      }
    } else {
      taskData.published_links = []
    }

    // 处理official_materials字段，确保它是一个数组
    if (taskData.official_materials) {
      try {
        taskData.official_materials = typeof taskData.official_materials === 'string'
          ? JSON.parse(taskData.official_materials)
          : taskData.official_materials
        console.log('Official Materials after parsing:', taskData.official_materials)
      } catch (e) {
        console.error('解析official_materials失败:', e)
        taskData.official_materials = []
      }
    } else {
      taskData.official_materials = []
    }

    taskDetail.value = taskData
    console.log('Final task detail:', taskDetail.value)

    console.log('Task detail after API call:', taskDetail.value)
    console.log('=== End fetchTaskDetail Debug ===')

    // 获取API统计数据 (data_source为api)
    const apiStatsResponse = await apiService.getTaskApiStats(props.task.id)
    console.log('API Stats Response:', apiStatsResponse.data)
    // 后端返回的是包含daily_stats的对象，需要提取数组
    apiStats.value = apiStatsResponse.data?.daily_stats || []
    console.log('Processed API Stats:', apiStats.value)

    // 获取渠道统计数据 (按task_id, data_source=api, channel_code查询)
    if (taskDetail.value.channel_code) {
      const channelStatsResponse = await apiService.getTaskChannelStats(props.task.id, {
        params: { channel_code: taskDetail.value.channel_code }
      })
      console.log('Channel Stats Response:', channelStatsResponse.data)

      // 设置总计数据
      if (channelStatsResponse.data?.total_stats) {
        totalChannelStats.value = channelStatsResponse.data.total_stats
      }

      // 后端返回的是包含channel_data的对象，需要提取对应渠道的数据
      const channelData = channelStatsResponse.data?.channel_data || {}
      channelStats.value = channelData[taskDetail.value.channel_code] || []
      console.log('Processed Channel Stats:', channelStats.value)
      console.log('Total Channel Stats:', totalChannelStats.value)
    }

    // 获取平台帖子统计数据 (基于published_links_metrics)
    if (taskData.published_links_metrics) {
      try {
        const metricsData = typeof taskData.published_links_metrics === 'string'
          ? JSON.parse(taskData.published_links_metrics)
          : taskData.published_links_metrics
        console.log('Published Links Metrics after parsing:', metricsData)

        // 提取posts数组
        if (metricsData && metricsData.posts && Array.isArray(metricsData.posts)) {
          platformPosts.value = metricsData.posts
          console.log('成功提取posts数组，数量:', metricsData.posts.length)
          console.log('Posts数据:', metricsData.posts)
        } else {
          platformPosts.value = []
          console.log('未找到有效的posts数组')
        }
      } catch (e) {
        console.error('解析published_links_metrics失败:', e)
        platformPosts.value = []
      }
    } else {
      platformPosts.value = []
      console.log('taskData.published_links_metrics 为空或不存在')
    }

    // 添加调试信息来诊断问题
    console.log('=== 调试信息 ===')
    console.log('任务ID:', taskData.id)
    console.log('任务状态:', taskData.task_status)
    console.log('published_links_metrics 是否存在:', !!taskData.published_links_metrics)
    console.log('published_links_metrics 内容:', taskData.published_links_metrics)
    console.log('published_links 是否存在:', !!taskData.published_links)
    console.log('published_links 内容:', taskData.published_links)
    console.log('平台帖子数量:', platformPosts.value.length)

    // 检查数据流程
    if (taskData.published_links) {
      try {
        const publishedLinksData = typeof taskData.published_links === 'string'
          ? JSON.parse(taskData.published_links)
          : taskData.published_links
        console.log('published_links 解析后:', publishedLinksData)
        console.log('是否有links数组:', !!publishedLinksData.links)
        console.log('links数组长度:', publishedLinksData.links?.length || 0)
      } catch (e) {
        console.error('解析published_links失败:', e)
      }
    }

    console.log('=== 调试信息结束 ===')

  } catch (error) {
    console.error('获取任务详情失败:', error)
    // ElMessage.error('获取任务详情失败')
    // 使用传入的基本数据作为备用
    taskDetail.value = { ...props.task }
    apiStats.value = []
    channelStats.value = []
    platformPosts.value = []
  } finally {
    loading.value = false
  }
  
  // 获取可用状态列表
  if (props.task?.id) {
    try {
      await fetchAvailableStatuses()
      await checkInvitationsAndApplications()
    } catch (error) {
      console.error('获取可用状态失败:', error)
    }
  }
}

// 任务类型标签映射
const getTaskTypeLabel = (type) => {
  const typeMap = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA活动'
  }
  return typeMap[type] || type || '-'
}

// 状态标签映射
const getStatusLabel = (status) => {
  const statusMap = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '已分配',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝',
    'running': '运行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status || '-'
}

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'published': 'warning',
    'assigned': 'primary',
    'unapproved': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'running': 'success',
    'completed': 'success',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// 奖励类型标签映射
const getRewardTypeLabel = (type) => {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌推广+按转化付费'
  }
  return typeMap[type] || type || '-'
}

// URL处理函数
const ensureFullUrl = (url) => {
  if (!url) return url
  // 如果URL不是以http://或https://开头，则添加https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }
  return url
}

// 状态管理相关函数
const showStatusFlow = () => {
  console.log('=== showStatusFlow Debug ===')
  console.log('Task detail:', taskDetail.value)
  console.log('Task ID:', taskDetail.value?.id)
  console.log('Task status:', taskDetail.value?.task_status)

  statusFlowVisible.value = true
  console.log('=== End showStatusFlow Debug ===')
}

// 关闭任务
const cancelTask = async () => {
  try {
    console.log('=== 关闭任务调试信息 ===')
    console.log('任务信息:', taskDetail.value)

    await ElMessageBox.confirm(
      '确定要关闭这个任务吗？此操作不可恢复。',
      '确认关闭',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await apiService.changeTaskStatus({
      task_id: taskDetail.value.id,
      new_status: 'cancelled',
      type: 1 // 默认为商户类型
    })

    console.log('关闭任务响应:', response)
    ElMessage.success(response.data.message || '任务已关闭')

    // 更新本地任务状态
    if (taskDetail.value) {
      taskDetail.value.task_status = 'cancelled'
    }

    // 通知父组件状态已更改
    emit('status-changed', {
      taskId: taskDetail.value.id,
      oldStatus: 'previous',
      newStatus: 'cancelled'
    })

  } catch (error) {
    if (error !== 'cancel') {
      console.error('关闭任务失败:', error)
      ElMessage.error(error.response?.data?.detail || '关闭任务失败')
    }
  }
}

// 状态变更回调
const handleStatusChanged = ({ taskId, oldStatus, newStatus }) => {
  console.log('Status changed:', { taskId, oldStatus, newStatus })

  // 更新本地任务状态
  if (taskDetail.value && taskDetail.value.id === taskId) {
    taskDetail.value.task_status = newStatus
    console.log('Updated task status to:', newStatus)
  }

  // 关闭状态流转对话框
  statusFlowVisible.value = false

  // 通知父组件状态已更改
  emit('status-changed', { taskId, oldStatus, newStatus })
}

// 添加新的方法
const showPublishLinks = () => {
  publishLinksVisible.value = true
}

const showInvitationsAndApplications = () => {
  invitationsAndApplicationsVisible.value = true
}

// 草稿内容相关辅助函数
function shouldShowDraftContent(task) {
  const validStatuses = ['unapproved', 'approved', 'running', 'completed']
  return validStatuses.includes(task?.task_status) && task?.draft_content
}

function getDraftContentPreview(draftContent, maxLength = 100) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.content) {
      return truncateText(draftData.content, maxLength)
    }
  } catch (error) {
    // 解析失败，当作纯文本处理
  }

  return truncateText(draftContent, maxLength)
}

function getDraftCreationNotes(draftContent) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.creation_notes) {
      return draftData.creation_notes
    }
  } catch (error) {
    // 解析失败，返回空
  }

  return ''
}

function getDraftMaterials(draftContent) {
  if (!draftContent) return []

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.materials) {
      return draftData.materials || []
    }
  } catch (error) {
    console.error('解析草稿素材失败:', error)
  }

  return []
}

function getMaterialIcon(materialType) {
  const iconMap = {
    'image': '🖼️',
    'video': '📹',
    'link': '🔗',
    'file': '📄'
  }
  return iconMap[materialType] || '📎'
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}



// 🆕 检查是否有有效的提交链接数据
function hasValidPublishedLinks(taskDetail) {
  if (!taskDetail?.published_links) return false

  try {
    // 如果是字符串，尝试解析
    let publishedData = taskDetail.published_links
    if (typeof publishedData === 'string') {
      // 空字符串或只包含空白字符
      if (!publishedData.trim()) return false

      // 尝试解析 JSON
      publishedData = JSON.parse(publishedData)
    }

    // 检查是否是有效的数组格式
    if (Array.isArray(publishedData)) {
      return publishedData.length > 0 && publishedData[0]?.links?.length > 0
    }

    // 检查是否是有效的对象格式
    if (publishedData && typeof publishedData === 'object') {
      return publishedData.links && publishedData.links.length > 0
    }

    return false
  } catch (error) {
    console.warn('检查 published_links 有效性失败:', error)
    return false
  }
}

function getPublishedLinks(publishedLinksData) {
  if (!publishedLinksData) return []

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    // 🔧 修复：处理数组格式的数据，取第一个提交记录
    if (Array.isArray(publishedData) && publishedData.length > 0) {
      const firstSubmission = publishedData[0]
      return firstSubmission.links || []
    }

    // 兼容旧格式：直接是对象的情况
    return publishedData.links || []
  } catch (error) {
    console.warn('解析 published_links 失败:', error)
    return []
  }
}

function getPublishedPlatform(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    // 🔧 修复：处理数组格式的数据，取第一个提交记录
    if (Array.isArray(publishedData) && publishedData.length > 0) {
      const firstSubmission = publishedData[0]
      return firstSubmission.platform || ''
    }

    // 兼容旧格式：直接是对象的情况
    return publishedData.platform || ''
  } catch (error) {
    return ''
  }
}

function getPublishedTime(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    let publishTime = ''

    // 🔧 修复：处理数组格式的数据，取第一个提交记录
    if (Array.isArray(publishedData) && publishedData.length > 0) {
      const firstSubmission = publishedData[0]
      publishTime = firstSubmission.publish_time || firstSubmission.submit_time
    } else {
      // 兼容旧格式：直接是对象的情况
      publishTime = publishedData.publish_time || publishedData.submit_time
    }

    if (!publishTime) {
      return '未知时间'
    }

    // 尝试解析ISO格式的日期字符串
    const date = new Date(publishTime)
    if (isNaN(date.getTime())) {
      console.warn('Invalid date in published_links:', publishTime)
      return '未知时间'
    }

    return formatDateTime(publishTime)
  } catch (error) {
    console.warn('Error parsing published_links time:', error)
    return '未知时间'
  }
}

function getPublishedNotes(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    // 🔧 修复：处理数组格式的数据，取第一个提交记录
    if (Array.isArray(publishedData) && publishedData.length > 0) {
      const firstSubmission = publishedData[0]
      return firstSubmission.notes || ''
    }

    // 兼容旧格式：直接是对象的情况
    return publishedData.notes || ''
  } catch (error) {
    return ''
  }
}

function getPlatformIcon(platform) {
  const iconMap = {
    'twitter': '🐦',
    'youtube': '📹',
    'medium': '📝',
    'instagram': '📷',
    'other': '🔗'
  }
  return iconMap[platform?.toLowerCase()] || '🔗'
}

function getPlatformLabel(platform) {
  const labelMap = {
    'twitter': 'Twitter/X',
    'youtube': 'YouTube',
    'medium': 'Medium',
    'instagram': 'Instagram',
    'other': '其他平台'
  }
  return labelMap[platform?.toLowerCase()] || '未知平台'
}

// 在现有的辅助函数后面添加formatDateTime函数
function formatDateTime(dateStr) {
  if (!dateStr) return ''
  
  try {
    const date = new Date(dateStr)
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string:', dateStr)
      return '未知时间'
    }
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.error('DateTime formatting error:', error, 'for date string:', dateStr)
    return '时间格式错误'
  }
}

// 格式化日期（只显示日期，不显示时间）
// 注意：formatDate函数已在上面定义，这里删除重复定义

// 监听props变化，确保组件挂载时执行
watchEffect(() => {
  if (props.task) {
    console.log('🎯 TaskDetailDialog component mounted or props changed!')
    console.log('Props received:', props)
    console.log('Task object:', props.task)
    fetchTaskDetail()
  }
})

// 保留onMounted作为备用
onMounted(() => {
  console.log('🎯 TaskDetailDialog onMounted called!')
  if (props.task) {
    console.log('Task exists in onMounted, calling fetchTaskDetail')
    fetchTaskDetail()
  } else {
    console.log('No task in onMounted, waiting for props...')
  }
})

// 状态管理相关函数
const fetchAvailableStatuses = async () => {
  try {
    console.log('Fetching available statuses for task:', props.task.id)
    const response = await apiService.getBitTaskStatusFlow(props.task.id)
    console.log('Status flow response:', response.data)
    
    availableStatuses.value = response.data.available_statuses || []
    console.log('Available statuses:', availableStatuses.value)
  } catch (error) {
    console.error('获取可用状态失败:', error)
    availableStatuses.value = []
  }
}

// 检查是否有邀请或申请
const checkInvitationsAndApplications = async () => {
  try {
    if (!props.task?.id) return
    
    const response = await apiService.getTaskInvitationsAndApplications(props.task.id)
    console.log('Invitations and applications response:', response.data)
    
    // 检查是否有邀请或申请数据
    const hasData = response.data && (
      (response.data.invitations && response.data.invitations.length > 0) ||
      (response.data.applications && response.data.applications.length > 0)
    )
    
    hasInvitationsOrApplications.value = hasData
    console.log('Has invitations or applications:', hasInvitationsOrApplications.value)
    console.log('Available statuses:', availableStatuses.value)
    console.log('Is assigned status available:', availableStatuses.value.includes('assigned'))
    console.log('Should show tooltip for assigned:', availableStatuses.value.includes('assigned') && !hasInvitationsOrApplications.value)
  } catch (error) {
    console.error('检查邀请和申请失败:', error)
    hasInvitationsOrApplications.value = false
  }
}

const getStatusDisplayName = (status) => {
  const statusNames = {
    'draft': '草稿',
    'published': '发布',
    'assigned': '分配',
    'unapproved': '审核通过',
    'approved': '审核通过',
    'rejected': '审核拒绝待修改',
    'running': '运行中',
    'completed': '完成',
    'cancelled': '关闭'
  }
  return statusNames[status] || status
}

const getStatusButtonType = (status) => {
  const buttonTypes = {
    'draft': 'primary',
    'published': 'success',
    'assigned': 'primary',
    'unapproved': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'running': 'success',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return buttonTypes[status] || 'default'
}

const getStatusDescription = (status) => {
  const descriptions = {
    'draft': '任务创建后的初始状态，可以修改任务内容和要求',
    'published': '任务已发布到需求池，等待KOL申请',
    'assigned': 'KOL已接受任务，可以提交资料进行审核',
    'unapproved': 'KOL已提交内容，等待品牌方审核',
    'approved': '内容审核通过，任务已准备就绪',
    'rejected': '内容需要修改，KOL需要重新制作',
    'running': '任务正在运行中，KOL正在发布内容',
    'completed': '任务已完成，所有工作都已结束',
    'cancelled': '如果点击关闭，则该任务不再执行'
  }
  return descriptions[status] || '未知状态'
}

// 判断是否为审核相关状态
const isReviewStatus = (status) => {
  return status === 'approved' || status === 'rejected'
}

// 判断是否应该显示审核按钮
const shouldShowReviewButtons = () => {
  const currentStatus = taskDetail.value?.task_status
  const hasPublishedLinks = taskDetail.value?.published_links
  
  // 确保availableStatuses.value存在
  if (!availableStatuses.value || !Array.isArray(availableStatuses.value)) {
    return false
  }
  
  // 当任务状态为已分配且有KOL提交的资料时，显示审核按钮
  if (currentStatus === 'assigned' && hasPublishedLinks && (availableStatuses.value.includes('approved') || availableStatuses.value.includes('rejected'))) {
    return true
  }
  
  // 当任务状态为待审核时，显示审核按钮
  if (currentStatus === 'unapproved' && (availableStatuses.value.includes('approved') || availableStatuses.value.includes('rejected'))) {
    return true
  }
  
  // 当任务状态为运行中或审核通过时，不显示审核按钮
  if (currentStatus === 'running' || currentStatus === 'approved') {
    return false
  }
  
  return false
}

// 获取审核按钮的描述
const getReviewDescription = () => {
  const currentStatus = taskDetail.value?.task_status
  
  if (currentStatus === 'assigned') {
    return 'KOL已提交资料，请进行审核'
  } else if (currentStatus === 'unapproved') {
    return 'KOL已提交内容，等待品牌方审核'
  }
  
  return '请进行审核'
}

// 获取非审核相关的状态列表
const getNonReviewStatuses = () => {
  const currentStatus = taskDetail.value?.task_status
  
  // 确保availableStatuses.value存在
  if (!availableStatuses.value || !Array.isArray(availableStatuses.value)) {
    return []
  }
  
  // 状态和按钮的映射关系
  const statusButtonMap = {
    'rejected': ['cancelled'], // 审核拒绝状态只显示取消按钮
    'approved': ['running'], // 审核通过状态只显示运行中按钮
    'running': [] // 运行中状态不显示任何按钮
  }
  
  // 如果当前状态在映射关系中，使用映射的按钮
  if (statusButtonMap[currentStatus]) {
    return availableStatuses.value.filter(status => 
      statusButtonMap[currentStatus].includes(status)
    ).sort((a, b) => {
      // 按照映射顺序排序
      const order = statusButtonMap[currentStatus]
      return order.indexOf(a) - order.indexOf(b)
    })
  }
  
  // 如果当前状态是已分配或待审核，并且显示了审核按钮，则不显示其他非审核按钮
  if ((currentStatus === 'assigned' || currentStatus === 'unapproved') && shouldShowReviewButtons()) {
    return []
  }
  
  // 过滤可用的状态，只显示关闭按钮在特定状态下
  const filteredStatuses = availableStatuses.value.filter(status => {
    // 如果是关闭按钮，只在特定状态下显示
    if (status === 'cancelled') {
      return ['draft', 'published', 'assigned'].includes(currentStatus)
    }
    // 其他状态正常显示，但排除审核相关状态
    return !isReviewStatus(status)
  })
  
  return filteredStatuses
}

const changeTaskStatus = async (newStatus) => {
  try {
    statusLoading.value = true

    // 如果是分配操作，先弹出邀请和申请列表
    if (newStatus === 'assigned') {
      invitationsAndApplicationsVisible.value = true
      return
    }

    // 如果是审核相关操作，检查当前状态
    if (newStatus === 'approved' || newStatus === 'rejected') {
      if (taskDetail.value?.task_status !== 'unapproved' && taskDetail.value?.task_status !== 'assigned') {
        ElMessage.error('当前任务状态不允许进行审核操作')
        return
      }
      
      // 如果是从已分配状态进行审核，需要确保有KOL提交的资料
      if (taskDetail.value?.task_status === 'assigned' && !taskDetail.value?.published_links) {
        ElMessage.error('KOL尚未提交资料，无法进行审核')
        return
      }
    }

    // 如果是审核通过，需要确认
    if (newStatus === 'approved') {
      await ElMessageBox.confirm(
        '确定要通过这个任务吗？通过后任务将进入运行中状态。',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    const response = await apiService.changeTaskStatus({
      task_id: props.task.id,
      new_status: newStatus,
      type: 1, // Bit操作
    })

    ElMessage.success(response.data.message || '状态更新成功')

    // 如果是审核通过，自动流转到运行中状态
    if (newStatus === 'approved') {
      try {
        const runningResponse = await apiService.changeTaskStatus({
          task_id: props.task.id,
          new_status: 'running',
          type: 1, // Bit操作
        })

        ElMessage.success('任务已进入运行中状态')
      } catch (runningError) {
        console.error('流转到运行中状态失败:', runningError)
        ElMessage.warning('审核通过成功，但流转到运行中状态失败')
      }
    }

    // 刷新任务详情
    await fetchTaskDetail()
    
    // 刷新可用状态和邀请申请状态
    await fetchAvailableStatuses()
    await checkInvitationsAndApplications()

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.task.id,
      oldStatus: taskDetail.value?.task_status,
      newStatus: newStatus === 'approved' ? 'running' : newStatus
    })

  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作
      console.error('更新状态失败:', error)
      ElMessage.error(error.response?.data?.detail || '更新状态失败')
    }
  } finally {
    statusLoading.value = false
  }
}

// 显示拒绝对话框
const showRejectDialog = () => {
  rejectReason.value = ''
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入拒绝理由')
    return
  }

  // 检查当前任务状态是否允许审核
  if (taskDetail.value?.task_status !== 'unapproved' && taskDetail.value?.task_status !== 'assigned') {
    ElMessage.error('当前任务状态不允许进行审核操作')
    return
  }
  
  // 如果是从已分配状态进行审核，需要确保有KOL提交的资料
  if (taskDetail.value?.task_status === 'assigned' && !taskDetail.value?.published_links) {
    ElMessage.error('KOL尚未提交资料，无法进行审核')
    return
  }

  try {
    statusLoading.value = true

    const response = await apiService.changeTaskStatus({
      task_id: props.task.id,
      new_status: 'rejected',
      type: 1, // Bit操作
      feedback: rejectReason.value.trim()
    })

    ElMessage.success('审核拒绝成功')

    // 关闭对话框
    rejectDialogVisible.value = false
    rejectReason.value = ''

    // 刷新任务详情
    await fetchTaskDetail()
    
    // 刷新可用状态和邀请申请状态
    await fetchAvailableStatuses()
    await checkInvitationsAndApplications()

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.task.id,
      oldStatus: taskDetail.value?.task_status,
      newStatus: 'rejected'
    })

  } catch (error) {
    console.error('审核拒绝失败:', error)
    ElMessage.error(error.response?.data?.detail || '审核拒绝失败')
  } finally {
    statusLoading.value = false
  }
}

// 临时加载测试数据
const loadTestData = () => {
  console.log('加载测试数据...')
  // 模拟API调用获取数据
  const mockPlatformPosts = [
    {
      post_id: '1234567890',
      post_url: 'https://twitter.com/user/status/1234567890123456789',
      post_date: '2023-10-27T10:00:00Z',
      metrics: {
        view_count: 1200,
        like_count: 150,
        retweet_count: 50,
        quote_count: 10,
        reply_count: 20,
        bookmark_count: 50
      },
      last_updated: '2023-10-27T10:05:00Z'
    },
    {
      post_id: '9876543210',
      post_url: 'https://medium.com/@user/article/1234567890',
      post_date: '2023-10-27T11:00:00Z',
      metrics: {
        view_count: 800,
        like_count: 80,
        retweet_count: 20,
        quote_count: 5,
        reply_count: 10,
        bookmark_count: 20
      },
      last_updated: '2023-10-27T11:05:00Z'
    },
    {
      post_id: '1122334455',
      post_url: 'https://youtube.com/watch?v=abc123',
      post_date: '2023-10-27T12:00:00Z',
      metrics: {
        view_count: 2000,
        like_count: 250,
        retweet_count: 100,
        quote_count: 20,
        reply_count: 50,
        bookmark_count: 100
      },
      last_updated: '2023-10-27T12:05:00Z'
    }
  ]

  // 先清空数据，然后重新设置
  platformPosts.value = []
  // 使用nextTick确保DOM更新后再设置数据
  nextTick(() => {
    platformPosts.value = mockPlatformPosts
    console.log('测试数据设置完成，平台帖子数量:', platformPosts.value.length)
    console.log('总计数据:', totalPlatformStats.value)
  })

  const mockChannelStats = [
    {
      stat_date: '2023-10-27',
      daily_clicks: 100,
      daily_registers: 15,
      daily_ftt: 1000,
      daily_deposit: 500,
      data_source: 'api'
    },
    {
      stat_date: '2023-10-28',
      daily_clicks: 120,
      daily_registers: 20,
      daily_ftt: 1200,
      daily_deposit: 600,
      data_source: 'api'
    },
    {
      stat_date: '2023-10-29',
      daily_clicks: 110,
      daily_registers: 18,
      daily_ftt: 1100,
      daily_deposit: 550,
      data_source: 'api'
    }
  ]
  channelStats.value = mockChannelStats

  console.log('测试数据加载成功！')
}

</script>

<style scoped>
.task-detail-dialog {
  .el-empty {
    height: 150px;
  }
}

.loading {
  padding: 40px;
}

.detail-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f6f9f8;
  border: 1px solid #efefef;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #efefef;
}

.section-icon {
  color: #409EFF;
  font-size: 20px;
  margin-right: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 10px;
  background: #fff;
  border: 1px solid #efefef;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.label {
  color: #888;
  font-weight: 500;
  min-width: 80px;
  margin-right: 10px;
}

.value {
  flex: 1;
}

.value.reward {
  color: #67C23A;
  font-weight: bold;
}

.value.description,
.value.materials {
  margin-top: 8px;
  line-height: 1.6;
  color: #ddd;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  padding: 20px;
  text-align: center;
  color: #fff;
  background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

  &.card2 {
    background: linear-gradient(to bottom right, #e74888, #bc53a1);
  }

  &.card3 {
    background: linear-gradient(to bottom right, #825dbf, #5345b4);
  }

  &.card4 {
    background: linear-gradient(to bottom right, #fbb728, #f68254);
  }
}

.stats-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
}


/* 状态操作按钮样式 */
.status-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

/* 可执行操作按钮样式 */
.executable-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.status-action-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  min-width: 120px;
}

.status-action-btn {
  min-width: 100px;
}

.status-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.review-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.reject-dialog-content {
  padding: 20px 0;
}

.reject-tip {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.status-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  max-width: 200px;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .status-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .executable-actions {
    flex-direction: column;
    gap: 12px;
  }

  .status-action-item {
    width: 100%;
    align-items: center;
  }

  .status-action-btn {
    width: 100%;
    justify-content: center;
  }

  .status-description {
    text-align: center;
    max-width: none;
  }
}

.no-data {
  padding: 40px;
  text-align: center;
}

.stats-table {
  margin-top: 24px;
}

.stats-table h4 {
  margin-bottom: 16px;
  font-size: 16px;
}

.channel-summary {
  margin-bottom: 24px;
}

.channel-summary h4 {
  color: #409EFF;
  margin-bottom: 16px;
  font-size: 16px;
}

/* 新增：平台帖子统计信息样式 */
.platform-stats {
  margin-top: 16px;
}

.platform-summary {
  margin-bottom: 24px;
}

.platform-summary h4 {
  color: #409EFF;
  margin-bottom: 16px;
  font-size: 16px;
}

/* 平台帖子统计卡片样式 */
.stats-card .stats-value.view {
  color: #409EFF;
}

.stats-card .stats-value.like {
  color: #67C23A;
}

.stats-card .stats-value.retweet {
  color: #E6A23C;
}

.stats-card .stats-value.reply {
  color: #F56C6C;
}

.stats-card .stats-value.quote {
  color: #9C27B0;
}

.stats-card .stats-value.bookmark {
  color: #FF9800;
}

/* 原有的统计卡片样式保持不变 */
.stats-card .stats-value.click {
  color: #409EFF;
}

.stats-card .stats-value.register {
  color: #67C23A;
}

.stats-card .stats-value.ftt {
  color: #E6A23C;
}

.stats-card .stats-value.deposit {
  color: #F56C6C;
}

.stats-card .stats-value.order {
  color: #909399;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.material-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.material-image .el-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.material-video .video-player {
  width: 100%;
  max-height: 150px;
}

.material-link {
  padding: 16px;
  word-break: break-all;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

/* 草稿内容样式 - 与原页面保持统一 */
.draft-content-section {
  margin-top: 8px;
  border: 1px solid #efefef;
  border-radius: 6px;
  background: #f6f9f8;
  padding: 16px;
}

.draft-text-box {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-line;
}

.creation-notes-box {
  margin-bottom: 12px;
}

.notes-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.notes-content {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
  color: #555;
  line-height: 1.5;
  font-size: 13px;
}

.materials-box {
  margin-bottom: 12px;
}

.materials-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.materials-preview .material-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.material-image .el-image {
  width: 100%;
  height: 120px;
}

.material-video .video-player {
  width: 100%;
  max-height: 120px;
}

.material-link {
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  word-break: break-all;
}

.material-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background: #f8f9fa;
  color: #6c757d;
}



/* KOL提交资料样式 */
.kol-submitted-content {
  margin-top: 8px;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
  background: #f0f8ff;
  padding: 16px;
}

.submitted-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e1f5fe;
}

.submitted-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 14px;
}

.submit-time {
  font-size: 12px;
  color: #666;
}

.submitted-links-box {
  background: #fff;
  border: 1px solid #e3f2fd;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
}

.submitted-link-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f5f9ff;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.submitted-link-item:last-child {
  margin-bottom: 0;
}

.submitted-link-item .link-url {
  color: #1976d2;
  text-decoration: none;
  word-break: break-all;
  flex: 1;
}

.submitted-link-item .link-url:hover {
  text-decoration: underline;
  color: #1565c0;
}

.submitted-notes {
  background: #fff;
  border: 1px solid #e3f2fd;
  border-radius: 4px;
  padding: 12px;
}

.submitted-notes .notes-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 13px;
  margin-bottom: 8px;
}

.submitted-notes .notes-content {
  color: #555;
  line-height: 1.5;
  font-size: 13px;
  background: #f8fbff;
  border-radius: 4px;
  padding: 8px;
}

.link-url {
  color: #409EFF;
  text-decoration: none;
  word-break: break-all;
  flex: 1;
}

.link-url:hover {
  text-decoration: underline;
}

/* 运行中状态提示样式 */
.running-status-notice {
  margin-bottom: 20px;
}

.running-notice {
  border-radius: 8px;
  border: 1px solid #e1f5fe;
  background: #f0f8ff;
}

.running-notice .el-alert__title {
  font-weight: bold;
  color: #1976d2;
}

.running-notice .el-alert__description {
  color: #555;
  line-height: 1.5;
}
</style>
