<template>
  <el-drawer v-model="visible" title="💰 收益详情" width="700px" :before-close="handleClose">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <div v-else-if="earningsData && earningsData.length > 0" class="earnings-content">
      <div v-for="(settlement, index) in earningsData" :key="index" class="settlement-item">
        <!-- 基本信息 -->
        <div class="settlement-header">
          <div class="info-row">
            <span class="label">任务名称：</span>
            <span class="value">{{ task?.task_name }}</span>
          </div>
          <div class="info-row">
            <span class="label">结算月份：</span>
            <span class="value">{{ settlement.settlement_month }}</span>
          </div>
          <div class="info-row">
            <span class="label">结算日期：</span>
            <span class="value">{{ formatDate(settlement.settlement_date) }}</span>
          </div>
        </div>

        <!-- 收益明细 -->
        <div class="earnings-section">
          <h3>💰 收益明细</h3>
          <div class="earnings-details">
            <div class="detail-group">
              <h4>基础奖励：</h4>
              <div class="detail-item">
                <span class="detail-label">推广单价：</span>
                <span class="detail-value">${{ formatNumber(settlement.base_reward) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">推广次数：</span>
                <span class="detail-value">{{ settlement.marketing_count || 1 }} 次</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">基础费用：</span>
                <span class="detail-value">${{ formatNumber(settlement.base_total || settlement.base_reward) }}</span>
              </div>
            </div>

            <div class="detail-group">
              <h4>效果奖励：</h4>
              <div class="detail-item">
                <span class="detail-label">效果单价：</span>
                <span class="detail-value">${{ formatNumber(settlement.performance_rate) }} </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">效果数量：</span>
                <span class="detail-value">{{ formatNumber(settlement.performance_value) }} </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">效果佣金：</span>
                <span class="detail-value">${{ formatNumber(settlement.performance_total) }}</span>
              </div>
            </div>

            <div class="detail-group">
              <h4>佣金奖励：</h4>
              <div class="detail-item">
                <span class="detail-label">带单金额：</span>
                <span class="detail-value">${{ formatNumber(settlement.commission_value) }} </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">抽佣比例：</span>
                <span class="detail-value">{{ formatNumber(settlement.commission_rate) }} </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">带单佣金：</span>
                <span class="detail-value">${{ formatNumber(settlement.commission_total) }}</span>
              </div>
            </div>

            <div class="detail-group total-group">
              <h4>收益汇总：</h4>
              <div class="detail-item">
                <span class="detail-label">基础费用：</span>
                <span class="detail-value">${{ formatNumber(settlement.base_total || settlement.base_reward) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">效果佣金：</span>
                <span class="detail-value">${{ formatNumber(settlement.performance_total) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">带单佣金：</span>
                <span class="detail-value">${{ formatNumber(settlement.commission_total) }}</span>
              </div>
              <div class="detail-item total-item">
                <span class="detail-label">费用总计：</span>
                <span class="detail-value total-value">${{ formatNumber(settlement.total_fee) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 暂时不要，后面在加上 -->
        <!-- 支付信息 -->
        <!-- <div class="payment-section">
          <h3>💳 支付信息</h3>
          <div class="payment-details">
            <div class="payment-item">
              <span class="payment-label">支付方式：</span>
              <span class="payment-value">{{ settlement.payment_method || '加密钱包' }}</span>
            </div>
            <div class="payment-item">
              <span class="payment-label">收款地址：</span>
              <span class="payment-value">{{ settlement.wallet_address || 'N/A' }}</span>
            </div>
            <div class="payment-item">
              <span class="payment-label">支付网络：</span>
              <span class="payment-value">{{ settlement.payment_network || 'Ethereum' }}</span>
            </div>
            <div class="payment-item">
              <span class="payment-label">支付时间：</span>
              <span class="payment-value">{{ formatDateTime(settlement.paid_time) }}</span>
            </div>
            <div class="payment-item">
              <span class="payment-label">交易哈希：</span>
              <span class="payment-value">{{ settlement.transaction_hash || 'N/A' }}</span>
            </div>
            <div class="payment-item">
              <span class="payment-label">支付状态：</span>
              <span class="payment-value" :class="getStatusClass(settlement.status)">
                {{ getStatusText(settlement.status) }}
              </span>
            </div>
          </div> -->
        <!-- </div> -->
      </div>
    </div>

    <div v-else class="no-earnings">
      <el-empty description="暂无收益数据" />
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'

// Props
const props = defineProps({
  modelValue: Boolean,
  task: Object
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const earningsData = ref(null)

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.task) {
    fetchTaskEarnings()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
async function fetchTaskEarnings() {
  if (!props.task?.id) {
    console.warn('任务ID不存在:', props.task)
    return
  }

  console.log('开始获取任务收益数据，任务ID:', props.task.id)
  loading.value = true
  try {
    const response = await apiService.getKolTaskEarnings(props.task.id)
    
    // 后端返回的数据结构是 { data: [...], total: ..., summary: ... }
    earningsData.value = response.data.data || response.data
  } catch (error) {
    ElMessage.error('获取收益数据失败')
  } finally {
    loading.value = false
  }
}

function handleClose() {
  visible.value = false
  earningsData.value = null
}

// 工具方法
function formatNumber(num) {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString()
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString()
}

function getPerformanceUnit() {
  const taskType = props.task?.task_type
  const unitMap = {
    'tweet': '点击',
    'video': '观看',
    'article': '阅读',
    'live': '观看',
    'ama': '参与者'
  }
  return unitMap[taskType] || '次'
}

function getStatusText(status) {
  const statusMap = {
    'paid': '✅ 已支付',
    'pending': '🟡 待支付',
    'processing': '🔄 处理中',
    'failed': '❌ 支付失败'
  }
  return statusMap[status] || status
}

function getStatusClass(status) {
  return `status-${status}`
}
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.settlement-item {
  margin-bottom: 24px;
}

.settlement-header {
  margin-bottom: 20px;
  padding: 12px;
  background: #f6f9f8;
  border: 1px solid #efefef;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  min-width: 80px;
}

.value {
  font-weight: normal;
}

.earnings-section, .payment-section {
  margin-bottom: 24px;
}

.earnings-section h3, .payment-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
}

.earnings-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-group {
  padding: 16px;
  background: #f6f9f8;
  border-radius: 8px;
  border: 1px solid #efefef;
}

.detail-group h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #409eff;
  font-size: 14px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #666;
}

.detail-value {
  font-weight: 500;
}

.total-group {
  border-left: 4px solid #67c23a;
}

.total-item {
  border-top: 1px solid #eee;
  padding-top: 8px;
  margin-top: 8px;
}

.total-value {
  color: #67c23a;
  font-weight: 600;
  font-size: 16px;
}

.payment-details {
  padding: 16px;
  background: #f6f9f8;
  border: 1px solid #efefef;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.payment-item:last-child {
  margin-bottom: 0;
}

.payment-label {
  min-width: 80px;
}

.payment-value {
  word-break: break-all;
}

.status-paid {
  color: #67c23a;
}

.status-pending {
  color: #e6a23c;
}

.status-processing {
  color: #409eff;
}

.status-failed {
  color: #f56c6c;
}

.no-earnings {
  text-align: center;
  padding: 40px;
}

</style>
