<template>
  <div>
    <span>当前状态：{{ task.task_status }}</span>
    <div style="margin-top: 8px;">
      <button
        v-for="s in nextStatuses[task.task_status] || []"
        :key="s"
        @click="changeStatus(s)"
        style="margin-right: 8px;"
      >
        流转到「{{ s }}」
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

import apiService from '@/utils/api'

const props = defineProps({
  task: Object,
  operatorId: Number,
  onStatusChanged: Function
})

const nextStatuses = {
  "待接受": ["已接受"],
  "已接受": ["待审核"],
  "待审核": ["进行中"],
  "进行中": ["待验收"],
  "待验收": ["已验收（待结算）"],
  "已验收（待结算）": ["已结算"],
  "已结算": []
}

const changeStatus = async (newStatus) => {
      await apiService.changeTaskStatus({
    task_id: props.task.id,
    new_status: newStatus,
    operator_id: props.operatorId
  })
  props.onStatusChanged && props.onStatusChanged()
}
</script>