<template>
  <el-drawer
    v-model="dialogVisible"
    title="申请任务"
    size="50%"
    :before-close="handleClose"
  >
    <div v-if="task" class="apply-dialog">
      <!-- 任务信息 -->
      <div class="task-info-section">
        <h3>任务信息</h3>
        <div class="task-summary">
          <div class="info-row">
            <span class="label">任务名称:</span>
            <span class="value">{{ task?.task_name || '' }}</span>
          </div>
          <div class="info-row">
            <span class="label">任务类型:</span>
            <span class="value">{{ getTaskTypeLabel(task?.task_type) }}</span>
          </div>
          <div class="info-row">
            <span class="label">基础报酬:</span>
            <span class="value">${{ task?.base_reward || 0 }}</span>
          </div>
          <div class="info-row" v-if="task?.performance_rate">
            <span class="label">效果奖励:</span>
            <span class="value">${{ task.performance_rate }}/注册</span>
          </div>
          <div class="info-row">
            <span class="label">截止时间:</span>
            <span class="value">{{ formatDate(task?.end_date) }}</span>
          </div>
          <div class="info-row" v-if="task?.description">
            <span class="label">任务要求:</span>
            <p class="value description">{{ task.description }}</p>
          </div>
        </div>
      </div>

      <!-- 申请表单 -->
      <div class="apply-form-section">
        <h3>申请理由</h3>
        <div class="simple-form">
          <div class="form-item">
            <label class="form-label">请详细说明您的申请理由和相关经验（至少10字）</label>
            <el-input
              v-model="applicationReason"
              type="textarea"
              :rows="6"
              placeholder="请包含以下内容：
1. 您为什么适合这个任务？
2. 您在相关领域的经验和成功案例
3. 您计划如何完成这个任务？
4. 您能为项目方带来什么价值？
示例：我在DeFi领域有2年的推广经验，曾为10+项目制作推广内容，平均每次推广能带来500+的用户注册。我的Twitter有10K+关注者，主要是对DeFi项目感兴趣的用户。我计划通过制作详细的项目分析线程来完成这个任务，预计能带来良好的推广效果..."
              maxlength="500"
              show-word-limit
            />
          </div>
        </div>
      </div>

      <!-- 提示信息 -->
      <div class="tips-section">
        <el-alert
          title="申请提示"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="tips-content">
            <p>1. 申请理由需要至少10字，详细说明您的经验和计划</p>
            <p>2. 项目方会在7天内处理您的申请</p>
            <p>3. 申请被拒绝后可以重新申请</p>
            <p>4. 一个任务同时只能申请一次</p>
          </div>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="submitApplication"
          :loading="submitting"
        >
          {{ submitting ? '提交中...' : '提交申请' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const submitting = ref(false)
const applicationReason = ref('')

// 使task在模板中可访问
const task = computed(() => props.task)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 重置表单
    applicationReason.value = ''
    // 调试：检查task对象结构
    console.log('TaskApplyDialog opened with task:', task.value)
    if (task.value && typeof task.value !== 'object') {
      console.error('Task is not an object:', typeof task.value, task.value)
    }
  }
})

// 方法
async function submitApplication() {
  if (submitting.value) return

  try {
    // 验证任务信息
    if (!task.value?.id) {
      ElMessage.error('任务信息无效')
      return
    }

    // 验证申请理由
    const reason = applicationReason.value.trim()
    if (!reason) {
      ElMessage.error('请填写申请理由')
      return
    }

    if (reason.length < 10) {
      ElMessage.error('申请理由至少需要10字')
      return
    }

    if (reason.length > 500) {
      ElMessage.error('申请理由不能超过500字')
      return
    }

    submitting.value = true

          await apiService.applyKolTaskById(task.value.id, {
      application_reason: reason
    })

    ElMessage.success('申请提交成功')
    emit('success')

  } catch (error) {
    console.error('提交申请失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('提交申请失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

function handleClose() {
  if (submitting.value) return
  emit('update:modelValue', false)
}

// 工具函数
function getTaskTypeLabel(type) {
  if (!type) return ''
  const labels = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA'
  }
  return labels[type] || type
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch (error) {
    console.error('Date formatting error:', error, dateStr)
    return ''
  }
}
</script>

<style scoped>

.task-info-section, .apply-form-section, .tips-section {
  margin-bottom: 24px;
}

.task-info-section h3, .apply-form-section h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  border-bottom: 1px solid #efefef;
  padding-bottom: 8px;
}

.task-summary {
  background: #f6f9f8;
  border: 1px solid #efefef;
  padding: 12px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  min-width: 80px;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.value {
  flex: 1;
}

.description {
  white-space: pre-wrap;
  line-height: 1.5;
  margin: 0;
}

.tips-section {
  margin-top: 20px;
}

.tips-content p {
  margin: 4px 0;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.simple-form {
  margin-top: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  color: #fff;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
}

</style>
