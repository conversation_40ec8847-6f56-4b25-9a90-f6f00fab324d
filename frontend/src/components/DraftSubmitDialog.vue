<template>
  <el-drawer v-model="visible" title="📝 提交内容草稿" size="50%" :before-close="handleClose">
    <div v-if="task" class="draft-form">
      <!-- 任务信息 -->
      <div class="task-info">
        <div class="info-row">
          <span class="label">任务名称：</span>
          <span class="value">{{ task.task_name }}</span>
        </div>
        <div class="info-row">
          <span class="label">任务类型：</span>
          <span class="value">{{ getTaskTypeIcon(task.task_type) }} {{ getTaskTypeLabel(task.task_type) }}</span>
        </div>
      </div>

      <!-- 表单 -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="草稿内容" prop="draft_content">
          <el-input v-model="formData.draft_content" type="textarea" :rows="8" placeholder="请输入推广内容草稿..."
            show-word-limit maxlength="2000" />
        </el-form-item>

        <el-form-item label="创作说明" prop="creation_notes">
          <el-input v-model="formData.creation_notes" type="textarea" :rows="4" placeholder="请说明创作思路和亮点..."
            show-word-limit maxlength="500" />
        </el-form-item>

        <el-form-item label="创作素材" prop="materials" class="form-item">
          <div class="materials-section">
            <el-button type="primary" @click="showMaterialUpload = true" class="upload-btn">
              <el-icon><Upload /></el-icon>
              上传素材
            </el-button>
            <div class="materials-preview" v-if="materials.length > 0">
              <div v-for="(material, index) in materials" :key="index" class="material-item">
                <!-- 图片预览 -->
                <div v-if="material.type === 'image'" class="material-image">
                  <el-image
                    :src="material.url"
                    :preview-src-list="[material.url]"
                    fit="cover"
                    class="preview-image"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div class="material-info">
                    <span class="material-name">{{ material.name }}</span>
                    <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="material.type === 'video'" class="material-video">
                  <video :src="material.url" class="preview-video" controls></video>
                  <div class="material-info">
                    <span class="material-name">{{ material.name }}</span>
                    <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>

                <!-- 链接预览 -->
                <div v-else class="material-link">
                  <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                    {{ material.name || material.url }}
                  </el-link>
                  <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitting" @click="handleSubmit">提交</el-button>
    </template>

    <!-- 素材上传对话框 -->
    <MaterialUploadDialog
      v-model:visible="showMaterialUpload"
      @materials-confirmed="handleMaterialsConfirmed"
    />
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Picture, Delete } from '@element-plus/icons-vue'
import apiService from '@/utils/api'
import MaterialUploadDialog from './MaterialUploadDialog.vue'

// Props
const props = defineProps({
  modelValue: Boolean,
  task: Object
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const visible = ref(false)
const submitting = ref(false)
const formRef = ref()
const showMaterialUpload = ref(false)
const materials = ref([])

const formData = reactive({
  draft_content: '',
  creation_notes: '',
  materials: []
})

const formRules = {
  draft_content: [
    { required: true, message: '请输入草稿内容', trigger: 'blur' },
    { min: 10, message: '草稿内容至少10个字符', trigger: 'blur' }
  ]
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.task) {
    // 如果是重新提交草稿，预填充之前的内容
    if (props.task.draft_content) {
      try {
        // 尝试解析JSON格式的草稿内容
        const draftData = JSON.parse(props.task.draft_content)
        if (typeof draftData === 'object' && draftData.content) {
          // 新格式：包含素材的JSON数据
          formData.draft_content = draftData.content || ''
          formData.creation_notes = draftData.creation_notes || ''
          materials.value = draftData.materials || []
          formData.materials = materials.value
        } else {
          // 旧格式：纯文本内容
          formData.draft_content = props.task.draft_content
        }
      } catch (error) {
        // 解析失败，当作纯文本处理
        formData.draft_content = props.task.draft_content
      }
    }
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 方法
function resetForm() {
  formData.draft_content = ''
  formData.creation_notes = ''
  formData.materials = []
  materials.value = []
  formRef.value?.clearValidate()
}

async function handleSubmit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch {
    return
  }

  submitting.value = true
  try {
    // 使用task_id或id，确保API调用正确
    const taskId = props.task.task_id || props.task.id
    await apiService.submitKolTaskDraft(taskId, {
      draft_content: formData.draft_content,
      creation_notes: formData.creation_notes,
      materials: materials.value
    })

    ElMessage.success('草稿提交成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交草稿失败:', error)
    ElMessage.error(error.response?.data?.detail || '提交草稿失败')
  } finally {
    submitting.value = false
  }
}

function handleClose() {
  visible.value = false
}

// 工具方法
function getTaskTypeIcon(taskType) {
  const iconMap = {
    'post': '📝',
    'video': '📹',
    'article': '📰',
    'live_stream': '📺',
    'ama_activity': '🎤'
  }
  return iconMap[taskType] || '📋'
}

function getTaskTypeLabel(taskType) {
  const labelMap = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA活动'
  }
  return labelMap[taskType] || taskType
}

// 素材处理方法
function handleMaterialsConfirmed(newMaterials) {
  console.log('handleMaterialsConfirmed called, newMaterials:', newMaterials)
  materials.value = newMaterials
  formData.materials = newMaterials
  console.log('materials.value after update:', materials.value)
  showMaterialUpload.value = false
}

function removeMaterial(index) {
  materials.value.splice(index, 1)
  formData.materials = materials.value
}

function ensureFullUrl(url) {
  if (!url) return ''
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  return `https://${url}`
}
</script>

<style scoped>
.task-info {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 8px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  min-width: 80px;
}

/* 素材上传相关样式 */
.materials-section {
  width: 100%;
}

.upload-btn {
  margin-bottom: 16px;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.material-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.material-image,
.material-video {
  position: relative;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.preview-image :deep(.el-image) {
  width: 100%;
  height: 120px;
}

.material-info {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.material-name {
  font-size: 12px;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.remove-btn {
  padding: 4px;
  min-height: auto;
}

.material-link {
  padding: 16px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 120px;
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  background: #f0f0f0;
  color: #999;
}
</style>
