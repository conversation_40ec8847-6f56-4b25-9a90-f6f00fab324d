<template>
  <div class="review-container">
    <div class="title">公司资质认证审核</div>
    <div class="review-card">
      <el-table :data="certificateList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="company_name" label="公司名称" width="200" />
        <el-table-column prop="certificate_type" label="证书类型" width="150">
          <template #default="scope">
            <el-tag v-if="scope.row.certificate_type === 'business_license'" type="success">营业执照</el-tag>
            <el-tag v-else-if="scope.row.certificate_type === 'org_code'" type="warning">组织机构代码证</el-tag>
            <el-tag v-else-if="scope.row.certificate_type === 'tax_registration'" type="info">税务登记证</el-tag>
            <el-tag v-else type="default">其他证书</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="verify_status" label="状态" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.verify_status === 'pending'" type="warning">待审核</el-tag>
            <el-tag v-else-if="scope.row.verify_status === 'approved'" type="success">已通过</el-tag>
            <el-tag v-else-if="scope.row.verify_status === 'rejected'" type="danger">已拒绝</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="提交时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="证书图片" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewCertificate(scope.row)">
              查看证书
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              v-if="scope.row.verify_status === 'pending'"
              type="success" 
              size="small" 
              @click="reviewCertificate(scope.row, 'approved')"
            >
              通过
            </el-button>
            <el-button 
              v-if="scope.row.verify_status === 'pending'"
              type="danger" 
              size="small" 
              @click="reviewCertificate(scope.row, 'rejected')"
            >
              拒绝
            </el-button>
            <span v-else>{{ scope.row.verify_remark || '已处理' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 审核对话框 -->
    <el-dialog v-model="reviewDialogVisible" title="审核公司资质认证" width="500px">
      <el-form :model="reviewForm" label-width="100px">
        <el-form-item label="公司名称">
          <span>{{ reviewForm.company_name }}</span>
        </el-form-item>
        <el-form-item label="证书类型">
          <span>{{ getCertificateTypeName(reviewForm.certificate_type) }}</span>
        </el-form-item>
        <el-form-item label="证书图片">
          <img 
            :src="reviewForm.certificate_url" 
            alt="证书图片" 
            style="max-width: 100%; max-height: 300px; cursor: pointer;"
            @click="openPreview(reviewForm.certificate_url)"
          />
        </el-form-item>
        <el-form-item label="审核备注" v-if="reviewForm.verify_status === 'rejected'">
          <el-input 
            v-model="reviewForm.verify_remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReview" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览遮罩 -->
    <div v-if="previewVisible" class="image-overlay" @click.self="closePreview">
      <img :src="previewUrl" alt="预览" class="image-overlay__img" />
      <span class="image-overlay__close" @click="closePreview">×</span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import apiService from '@/utils/api'

const certificateList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const reviewDialogVisible = ref(false)
const submitting = ref(false)
const reviewForm = reactive({
  id: null,
  company_name: '',
  certificate_type: '',
  certificate_url: '',
  verify_status: '',
  verify_remark: ''
})

const previewVisible = ref(false)
const previewUrl = ref('')

// 获取证书列表
async function fetchCertificateList() {
  try {
    loading.value = true
    const res = await apiService.getCompanyCertificateList({
      page: currentPage.value,
      page_size: pageSize.value
    })
    
    certificateList.value = res.data.items || []
    total.value = res.data.total || 0
  } catch (e) {
    ElMessage.error(e?.response?.data?.detail || '获取证书列表失败')
    console.error('fetchCertificateList error:', e)
  } finally {
    loading.value = false
  }
}

// 查看证书
function viewCertificate(row) {
  reviewForm.id = row.id
  reviewForm.company_name = row.company_name
  reviewForm.certificate_type = row.certificate_type
  reviewForm.certificate_url = row.certificate_url
  reviewForm.verify_status = ''
  reviewForm.verify_remark = ''
  reviewDialogVisible.value = true
}

// 审核证书
function reviewCertificate(row, status) {
  reviewForm.id = row.id
  reviewForm.company_name = row.company_name
  reviewForm.certificate_type = row.certificate_type
  reviewForm.certificate_url = row.certificate_url
  reviewForm.verify_status = status
  reviewForm.verify_remark = ''
  reviewDialogVisible.value = true
}

// 确认审核
async function confirmReview() {
  try {
    if (reviewForm.verify_status === 'rejected' && !reviewForm.verify_remark.trim()) {
      ElMessage.warning('拒绝时请输入拒绝原因')
      return
    }

    submitting.value = true
    await apiService.reviewCompanyCertificate({
      id: reviewForm.id,
      verify_status: reviewForm.verify_status,
      verify_remark: reviewForm.verify_remark
    })
    
    ElMessage.success('审核完成')
    reviewDialogVisible.value = false
    await fetchCertificateList()
  } catch (e) {
    ElMessage.error(e?.response?.data?.detail || '审核失败')
  } finally {
    submitting.value = false
  }
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val
  currentPage.value = 1
  fetchCertificateList()
}

function handleCurrentChange(val) {
  currentPage.value = val
  fetchCertificateList()
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN')
}

// 获取证书类型名称
function getCertificateTypeName(type) {
  const typeMap = {
    'business_license': '营业执照',
    'org_code': '组织机构代码证',
    'tax_registration': '税务登记证',
    'other': '其他证书'
  }
  return typeMap[type] || type
}

// 图片预览
function openPreview(url) {
  previewUrl.value = url
  previewVisible.value = true
}

function closePreview() {
  previewVisible.value = false
  previewUrl.value = ''
}

onMounted(fetchCertificateList)
</script>

<style scoped>
.review-container {
  padding: 20px;
}

.title {
  color: #444;
  font-size: 24px;
  margin-bottom: 20px;
}

.review-card {
  background: #fff;
  border: 1px solid #efefef;
  padding: 20px;
  border-radius: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay__img {
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.image-overlay__close {
  position: absolute;
  top: 40px;
  right: 60px;
  font-size: 40px;
  color: white;
  cursor: pointer;
  font-weight: bold;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  user-select: none;
  transition: color 0.2s;
}

.image-overlay__close:hover {
  color: #409eff;
}
</style> 