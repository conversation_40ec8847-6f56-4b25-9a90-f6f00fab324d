<template>
    <div>
      <div class="title">用户信息</div>
      <el-form :model="form" label-width="80px" class="card">
        <el-form-item label="用户名">
          <el-input v-model="form.username" disabled />
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="form.nickname" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="form.email" disabled />
        </el-form-item>
        <el-form-item label="钱包地址">
          <el-input v-model="form.wallet_address" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="save">保存修改</el-button>
        </el-form-item>
      </el-form>
    </div>

  </template>

  <script setup>
  import { ElMessage, ElMessageBox, ElSkeleton } from 'element-plus'
  import { ref, onMounted } from 'vue'
  import { useUserStore } from '@/store/user'
  import apiService from '@/utils/api'
  import TaskStatusFlow from '@/components/TaskStatusFlow.vue'
  import { Document, Setting, Close, View, ArrowDown, ArrowRight } from '@element-plus/icons-vue'

  const userStore = useUserStore()
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  const form = ref({
    id: userInfo.value.id, // 确保带上 id
    username: userInfo.value.username || '',
    nickname: userInfo.value.nickname || '',
    phone: userInfo.value.phone || '',
    email: userInfo.value.email || '',
    wallet_address: userInfo.value.wallet_address || ''
  })


  async function save() {
    try {
      // 🔧 调试信息：检查 Token 状态
      console.log('=== 用户资料更新调试信息 ===')
      console.log('当前用户信息:', userInfo.value)
      console.log('当前 Token:', userStore.token)
      console.log('localStorage Token:', localStorage.getItem('token'))
      console.log('表单数据:', form.value)

      await apiService.updateProfile(form.value)
      Object.assign(userInfo.value, form.value)
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      ElMessage.success('保存成功')
    } catch (error) {
      console.error('保存失败:', error)
      console.error('错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers
      })

      // 根据错误类型提供更详细的错误信息
      if (error.response?.status === 401) {
        ElMessage.error('认证失败：请重新登录')
      } else {
        ElMessage.error(error.response?.data?.detail || '保存失败')
      }
    }
  }
</script>
<style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }
  .card {
    margin-top: 32px;
    padding: 20px;
    background: #fff;
    border: #efefef 1px solid;
    .el-input {
      max-width: 360px;
    }
  }
</style>
