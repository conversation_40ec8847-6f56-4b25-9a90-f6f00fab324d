<template>
  <div>
    <div class="title">自动结算系统</div>
    <div class="c-title">查看并处理KOL的佣金结算</div>
    <div class="main">
      <div class="module-title">
        <span class="name">结算单</span>
      </div>
      <el-table :data="settlements" border>
        <el-table-column prop="kol_name" label="KOL名称" />
        <el-table-column prop="task_name" label="任务名称" />
        <el-table-column prop="base_total" label="基础费用">
          <template #default="scope">￥{{ formatMoney(scope.row.base_total) }}</template>
        </el-table-column>
        <el-table-column prop="performance_total" label="效果佣金">
          <template #default="scope">￥{{ formatMoney(scope.row.performance_total) }}</template>
        </el-table-column>
        <el-table-column prop="commission_total" label="带单佣金">
          <template #default="scope">￥{{ formatMoney(scope.row.commission_total) }}</template>
        </el-table-column>
        <el-table-column prop="total_fee" label="总计">
          <template #default="scope">￥{{ formatMoney(scope.row.total_fee) }}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'paid' ? 'success' : (scope.row.status === 'failed' ? 'danger' : 'warning')" effect="dark">
              {{ scope.row.status === 'paid' ? '已支付' : scope.row.status === 'pending' ? '待支付' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlement_date" label="结算日期">
          <template #default="scope">{{ scope.row.settlement_date || '-' }}</template>
        </el-table-column>
        <el-table-column prop="settlement_month" label="结算月份" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="showDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 详情弹窗 -->
    <el-drawer v-model="detailDialogVisible" :title="`结算详情 - ${currentDetail?.task_name || ''}`" size="40%"
      :before-close="handleDetailClose">
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-section">
          <h4>📊 基础信息</h4>
          <div class="detail-item">
            <span class="label">KOL名称：</span>
            <span class="value">{{ currentDetail.kol_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">任务名称：</span>
            <span class="value">{{ currentDetail.task_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结算月份：</span>
            <span class="value">{{ currentDetail.settlement_month }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结算日期：</span>
            <span class="value">{{ currentDetail.settlement_date || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDate(currentDetail.created_at) }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>💰 费用明细</h4>
          <div class="detail-item">
            <span class="label">营销次数：</span>
            <span class="value">{{ currentDetail.marketing_count }} 次</span>
          </div>
          <div class="detail-item">
            <span class="label">营销单价：</span>
            <span class="value">¥{{ formatMoney(currentDetail.base_reward) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">基础费用：</span>
            <span class="value">¥{{ formatMoney(currentDetail.base_total) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果值：</span>
            <span class="value">{{ currentDetail.performance_value }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果单价：</span>
            <span class="value">¥{{ formatMoney(currentDetail.performance_rate) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果佣金：</span>
            <span class="value">¥{{ formatMoney(currentDetail.performance_total) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">带单金额：</span>
            <span class="value">¥{{ formatMoney(currentDetail.commission_value) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">抽佣比例：</span>
            <span class="value">{{ currentDetail.commission_rate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">带单佣金：</span>
            <span class="value">¥{{ formatMoney(currentDetail.commission_total) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">费用总计：</span>
            <span class="value" style="font-weight: bold; color: #67c23a;">¥{{ formatMoney(currentDetail.total_fee) }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>💳 支付信息</h4>
          <div class="detail-item">
            <span class="label">支付方式：</span>
            <span class="value">{{ currentDetail.payment_method === 'crypto_wallet' ? '加密钱包' : '银行转账' }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.payment_network">
            <span class="label">支付网络：</span>
            <span class="value">{{ currentDetail.payment_network }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.wallet_address">
            <span class="label">钱包地址：</span>
            <span class="value" style="word-break: break-all;">{{ currentDetail.wallet_address }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.tx_hash">
            <span class="label">交易哈希：</span>
            <span class="value" style="word-break: break-all;">
              <a :href="`https://etherscan.io/tx/${currentDetail.tx_hash}`" target="_blank" style="color:#409EFF;">
                {{ formatTxHash(currentDetail.tx_hash) }}
              </a>
            </span>
          </div>
          <div class="detail-item" v-if="currentDetail.paid_time">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatDate(currentDetail.paid_time) }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.payment_note">
            <span class="label">支付备注：</span>
            <span class="value">{{ currentDetail.payment_note }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import apiService from '@/utils/api'

const settlements = ref([])
const currentDetail = ref(null)
const detailDialogVisible = ref(false)

function formatMoney(val) {
  if (val === null || val === undefined || isNaN(val)) {
    return '0.00'
  }
  return Number(val).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

function formatDate(dateStr, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`
  } else if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  return date.toLocaleString('zh-CN')
}

function formatTxHash(hash) {
  if (!hash) return '-'
  return hash.length > 10 ? `${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}` : hash
}

function showDetail(row) {
  currentDetail.value = row
  detailDialogVisible.value = true
}

function handleDetailClose() {
  detailDialogVisible.value = false
  currentDetail.value = null
}

async function fetchData() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('💰 User not logged in, skipping settlement fetch')
    settlements.value = []
    return
  }

  try {
    const res = await apiService.getUserSettlements()
    settlements.value = Array.isArray(res.data) ? res.data : [res.data]
  } catch (e) {
    console.error('获取结算数据失败:', e)
    settlements.value = []
  }
}

onMounted(fetchData)
</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
}

.c-title {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
}

.main {
  background: #fff;
  margin-top: 32px;
  padding: 20px;
}

.main .module-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}

.main .module-title .name {
  font-size: 16px;
}

.detail-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-section h4 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-item .label {
  font-size: 15px;
  color: #aaa;
  font-weight: bold;
}

.detail-item .value {
  font-size: 15px;
  font-weight: normal;
  word-break: break-all;
}

.detail-item .value a {
  color: #409EFF;
  text-decoration: none;
}

.detail-item .value a:hover {
  text-decoration: underline;
}
</style>
