<template>
  <div>
    <div style="margin-bottom: 20px;">
      <el-button @click="goBack" style="color: #409EFF;">
        <el-icon><ArrowLeft /></el-icon>
        返回任务看板
      </el-button>
    </div>

    <!-- 任务基本信息 -->
    <el-card class="task-card">
      <template #header>
        <div class="card-header">任务基本信息</div>
      </template>
      <div class="task-info">
        <div class="info-row">
          <span class="info-label">任务标题:</span>
          <span class="info-value">{{ task.task_name || 'DeFi项目Twitter推广' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">项目方:</span>
          <span class="info-value">{{ task.creator_name || 'DeFiProtocol' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">发布时间:</span>
          <span class="info-value">{{ formatDateTime(task.create_time) || '2024-01-15 14:30' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">截止时间:</span>
          <span class="info-value">{{ formatDateTime(task.end_date) || '2024-02-15 23:59' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">任务状态:</span>
          <el-tag :type="getStatusType(task.task_status)">{{ getStatusText(task.task_status) }}</el-tag>
        </div>
        <div class="info-row">
          <span class="info-label">基础报酬:</span>
          <span class="info-value">${{ task.base_reward || '200' }} USDT</span>
        </div>
        <div class="info-row">
          <span class="info-label">效果奖励:</span>
          <span class="info-value">每个有效注册额外${{ task.performance_rate || '5' }} USDT</span>
        </div>
        <div class="info-row">
          <span class="info-label">预计总收入:</span>
          <span class="info-value">${{ task.base_reward || '200' }}-${{ (task.base_reward || 200) + (task.performance_rate || 5) * 60 }} USDT</span>
        </div>
        <div class="info-row">
          <span class="info-label">目标KOL数量:</span>
          <span class="info-value">{{ task.target_kol_count || '5' }}人</span>
        </div>
      </div>
    </el-card>

    <!-- 任务要求详情 -->
    <el-card class="task-card">
      <template #header>
        <div class="card-header">任务要求详情</div>
      </template>
      <div class="task-requirements">
        <div class="info-row">
          <span class="info-label">任务类型:</span>
          <span class="info-value">{{ getTaskTypeText(task.task_type) || '推文' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">专业标签:</span>
          <span class="info-value">DeFi, 去中心化金融, 流动性挖矿</span>
        </div>

        <div class="requirements-section">
          <div class="section-title">具体要求:</div>
          <div class="requirements-content">
            {{ task.description || `1. 发布一条关于我们DeFi协议的推文
2. 必须包含指定话题标签: #DeFiProtocol #DeFi
3. 必须包含官方链接: https://defiprotocol.com
4. 推文字数不少于100字
5. 需要体现个人观点和专业分析
6. 发布后24小时内提交推文链接` }}
          </div>
        </div>

        <div class="requirements-section">
          <div class="section-title">禁止内容:</div>
          <div class="requirements-content">
            • 不得包含其他项目的推广信息<br>
            • 不得使用负面或争议性语言<br>
            • 不得抄袭其他KOL的内容
          </div>
        </div>

        <div class="requirements-section" v-if="task.official_materials">
          <div class="section-title">官方素材:</div>
          <div class="requirements-content">
            <div v-for="(material, index) in parseMaterials(task.official_materials)" :key="index">
              • <a :href="material.url" target="_blank" style="color: #409EFF;">{{ material.name }}</a>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 草稿内容 (仅当任务已分配时显示) -->
    <el-card class="task-card" v-if="task.task_status === 'assigned' && task.draft_content">
      <template #header>
        <div class="card-header">草稿内容</div>
      </template>
      <div class="draft-content">
        <div class="section-title">📝 待发布内容:</div>
        <div class="draft-text">
          {{ task.draft_content }}
        </div>

        <div class="section-title" style="margin-top: 20px;">📄 创作说明:</div>
        <div class="draft-description">
          重点突出了15%的高收益率和革命性技术，同时强调了安全性和个人分析的专业性。
        </div>

        <div class="info-row" style="margin-top: 20px;">
          <span class="info-label">📅 草稿提交时间:</span>
          <span class="info-value">{{ formatDateTime(task.draft_submit_time) || '2024-01-21 16:20' }}</span>
        </div>
      </div>
    </el-card>

    <!-- 项目方信息 -->
    <el-card class="task-card">
      <template #header>
        <div class="card-header">项目方信息</div>
      </template>
      <div class="project-info">
        <div class="info-row">
          <span class="info-label">公司名称:</span>
          <span class="info-value">{{ task.creator_name || 'DeFiProtocol' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">联系人:</span>
          <span class="info-value">Alice Chen</span>
        </div>
        <div class="info-row">
          <span class="info-label">历史合作:</span>
          <span class="info-value">已完成32个任务</span>
        </div>
        <div class="info-row">
          <span class="info-label">结算记录:</span>
          <span class="info-value">100%按时结算</span>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="goBack">取消</el-button>
      <el-button
        v-if="task.task_status === 'published'"
        type="primary"
        @click="applyTask"
        :loading="applying"
      >
        立即申请
      </el-button>
      <el-button
        v-if="task.task_status === 'assigned'"
        type="primary"
        @click="showSubmitDialog"
      >
        提交内容
      </el-button>
    </div>

    <!-- 内容提交对话框 -->
    <el-dialog v-model="submitDialogVisible" title="提交内容" width="600px" class="submit-dialog">
      <el-form :model="submitForm" label-width="120px">
        <el-form-item label="内容链接" required>
          <el-input
            v-model="submitForm.contentUrl"
            placeholder="请输入已发布内容的链接（如Twitter链接、YouTube链接等）"
          />
        </el-form-item>

        <el-form-item label="内容描述" required>
          <el-input
            v-model="submitForm.contentDescription"
            type="textarea"
            :rows="4"
            placeholder="请描述您创作的内容，包括主要亮点和推广策略"
          />
        </el-form-item>

        <el-form-item label="推广链接">
          <el-input
            v-model="submitForm.promotionLinks"
            type="textarea"
            :rows="2"
            placeholder="如有特定的推广链接或追踪链接，请在此填写"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="submitDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitContent" :loading="submitting">提交内容</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

const task = ref({})
const applying = ref(false)
const submitting = ref(false)
const submitDialogVisible = ref(false)

const submitForm = ref({
  contentUrl: '',
  contentDescription: '',
  promotionLinks: ''
})

// 获取任务详情
async function fetchTaskDetail() {
  try {
    const taskId = route.params.id
    const response = await apiService.getMarketingTasks({ id: taskId })
    task.value = response.data
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
    // 使用模拟数据
    task.value = {
      id: 1,
      task_name: 'DeFi项目Twitter推广',
      creator_name: 'DeFiProtocol',
      task_status: 'published',
      task_type: 'post',
      base_reward: 200,
      performance_rate: 5,
      target_kol_count: 5,
      description: '发布一条关于我们DeFi协议的推文，必须包含指定话题标签和链接',
      create_time: '2024-01-15T14:30:00Z',
      end_date: '2024-02-15T23:59:00Z'
    }
  }
}

// 申请任务
async function applyTask() {
  applying.value = true
  try {
    await apiService.applyKolTask({
      task_id: task.value.id
    })
    ElMessage.success('任务申请成功！')
    task.value.task_status = 'assigned'
  } catch (error) {
    console.error('申请任务失败:', error)
    ElMessage.error(error.response?.data?.detail || '申请任务失败')
  } finally {
    applying.value = false
  }
}

// 显示提交对话框
function showSubmitDialog() {
  submitForm.value = {
    contentUrl: '',
    contentDescription: '',
    promotionLinks: ''
  }
  submitDialogVisible.value = true
}

// 提交内容
async function submitContent() {
  if (!submitForm.value.contentUrl.trim() || !submitForm.value.contentDescription.trim()) {
    ElMessage.warning('请填写内容链接和内容描述')
    return
  }

  submitting.value = true
  try {
    await apiService.submitKolTask({
      task_id: task.value.id,
      content_url: submitForm.value.contentUrl,
      content_description: submitForm.value.contentDescription,
      promotion_links: submitForm.value.promotionLinks
    })
    ElMessage.success('内容提交成功！')
    submitDialogVisible.value = false
    task.value.task_status = 'unapproved'
  } catch (error) {
    console.error('提交内容失败:', error)
    ElMessage.error(error.response?.data?.detail || '提交内容失败')
  } finally {
    submitting.value = false
  }
}

// 返回任务看板
function goBack() {
  router.push('/kol-task')
}

// 工具函数
function getStatusType(status) {
  const statusMap = {
    'published': 'success',
    'assigned': 'warning',
    'unapproved': 'info',
    'approved': 'success',
    'rejected': 'danger',
    'completed': 'success',
    'cancelled': 'info'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    'published': '🟢已发布',
    'assigned': '🟡已分配',
    'unapproved': '🔵待审核',
    'approved': '🟢审核通过',
    'rejected': '🔴审核拒绝',
    'completed': '✅已完成',
    'cancelled': '❌已关闭'
  }
  return statusMap[status] || status
}

function getTaskTypeText(type) {
  const typeMap = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA活动'
  }
  return typeMap[type] || type
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

function parseMaterials(materials) {
  if (!materials) return []
  try {
    return JSON.parse(materials)
  } catch {
    // 如果不是JSON格式，按行分割
    return materials.split('\n').map((line, index) => ({
      name: `素材${index + 1}`,
      url: line.trim()
    })).filter(item => item.url)
  }
}

onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped>
.task-card {
  border: 1px solid #efefef;
  margin-bottom: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
}

.info-label {
  min-width: 120px;
  color: #aaa;
  font-weight: 500;
}

.info-value {
  flex: 1;
}

.requirements-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.requirements-content {
  background: #f6f9f8;
  border: 1px solid #efefef;
  border-radius: 6px;
  padding: 12px;
  line-height: 1.6;
  white-space: pre-line;
}

.draft-text {
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 6px;
  padding: 15px;
  margin: 8px 0;
  color: #fff;
  line-height: 1.5;
}

.draft-description {
  border: 1px solid #efefef;
  border-radius: 6px;
  padding: 12px;
  line-height: 1.5;
}

.action-buttons {
  text-align: right;
  margin-top: 20px;
}
</style>
