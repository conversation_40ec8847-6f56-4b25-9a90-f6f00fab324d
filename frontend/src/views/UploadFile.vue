<template>
  <div>
    <div class="title">文件上传</div>
    <el-card style="border:none; max-width:400px; width:100%; margin-top: 32px;">
      <el-upload
        class="upload-demo"
        drag
        :http-request="customUpload"
        :show-file-list="false"
        :before-upload="beforeUpload"
        style="width:100%"
      >
        <i class="el-icon-upload" style="font-size:48px;color:#409EFF" />
        <div class="el-upload__text" style="color:#bbb;">将文件拖到此处，或 <em>点击上传</em></div>
      </el-upload>
      <el-alert v-if="url" :title="'上传成功，文件URL：' + url" type="success" show-icon style="margin-top:20px;word-break:break-all;" />
      <el-alert v-if="error" :title="error" type="error" show-icon style="margin-top:20px;" />
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import apiService from '@/utils/api'
const url = ref('')
const error = ref('')

// 自定义上传方法，使用 api.js 的拦截器
async function customUpload(options) {
  try {
    const formData = new FormData()
    formData.append('file', options.file)

    const response = await apiService.uploadFile(formData)
    console.log('Upload response:', response.data)

    // 直接调用 handleSuccess，传递正确的数据
    handleSuccess(response.data)
  } catch (err) {
    console.error('Upload error:', err)
    handleError(err)
  }
}

function handleSuccess(res) {
  if (res && res.url) {
    url.value = res.url
    error.value = ''
  } else {
    error.value = '上传成功但未获取到文件URL'
  }
}
function handleError(err) {
  url.value = ''
  // 处理不同的错误类型
  if (err?.response?.data?.detail) {
    error.value = err.response.data.detail
  } else if (err?.message) {
    error.value = err.message
  } else {
    error.value = '上传失败'
  }
}
function beforeUpload(file) {
  url.value = ''
  error.value = ''
  return true
}
</script>

<style scoped>
.title {
    color: #444;
    font-size: 24px;
  }
  .upload-demo {
    width: 100%;
    margin-bottom: 20px;
  }
</style>
