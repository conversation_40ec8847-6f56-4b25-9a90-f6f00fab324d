<template>
  <div class="verify-container">
    <div class="title">实名认证</div>
    <div class="c-titlr">请填写真实身份信息，完成实名认证</div>
    <div class="verify-card">
      <!-- 状态提示 -->
      <el-alert
        v-if="statusInfo.verify_status === 'approved'"
        title="认证已通过"
        type="success"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />
      <el-alert
        v-else-if="statusInfo.verify_status === 'pending'"
        title="认证信息已提交，正在审核中"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />
      <el-alert
        v-else-if="statusInfo.verify_status === 'rejected'"
        :title="'认证未通过' + (statusInfo.verify_remark ? '：' + statusInfo.verify_remark : '')"
        type="error"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />

      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="120px"
        class="verify-form"
        :disabled="statusInfo.verify_status === 'approved'"
      >
        <el-form-item label="姓名" prop="real_name" required>
          <el-input
            v-model="form.real_name"
            placeholder="请输入姓名,必填"
            maxlength="20"
            show-word-limit
            style="width: 300px;"
          />
        </el-form-item>

        <el-form-item label="证件类型" prop="id_type">
          <el-select v-model="form.id_type" placeholder="请选择证件类型" style="width: 300px;">
            <el-option label="居民身份证" value="id_card" />
            <!-- <el-option label="护照" value="passport" /> -->
            <!-- <el-option label="军官证" value="military_id" /> -->
          </el-select>
        </el-form-item>

        <el-form-item label="身份证号码" prop="id_number" required>
          <el-input
            v-model="form.id_number"
            placeholder="请输入身份证号码,必填"
            style="width: 300px;"
          />
        </el-form-item>

        <el-form-item label="证件有效期" prop="id_validity" required>
          <div class="validity-container">
            <el-date-picker
              v-model="form.id_validity"
              type="date"
              placeholder="请选择证件有效期,必填"
              style="width: 200px;"
              :disabled="form.is_long_term"
            />
            <el-checkbox
              v-model="form.is_long_term"
              style="margin-left: 16px;"
            >
              长期有效
            </el-checkbox>
          </div>
          <div class="form-hint">
            &nbsp;请与资质照片上的有效期保持一致,若为长期有效证件请勾选"长期有效"
          </div>
        </el-form-item>

        <el-form-item label="身份证照片" prop="id_card_front" required>
          <div class="upload-container">
            <div class="upload-area">
              <div class="upload-item">
                <div class="upload-label">正面照片</div>
                <div
                  class="upload-box"
                  @click="frontList.length === 0 && handleUploadClick('front')"
                >
                  <template v-if="frontList.length === 0">
                    <div class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">点击上传</div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="image-preview">
                      <img
                        :src="frontList[0].url || frontList[0].thumbUrl"
                        class="preview-img"
                        @click.stop="openPreview(frontList[0].url || frontList[0].thumbUrl)"
                      />
                      <div class="image-actions">
                        <el-icon class="action-icon" @click.stop="openPreview(frontList[0].url || frontList[0].thumbUrl)">
                          <View />
                        </el-icon>
                        <div class="action-divider"></div>
                        <el-icon class="action-icon" @click.stop="removeFront">
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                </div>
              </div>

              <div class="upload-item">
                <div class="upload-label">反面照片</div>
                <div
                  class="upload-box"
                  @click="backList.length === 0 && handleUploadClick('back')"
                >
                  <template v-if="backList.length === 0">
                    <div class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">点击上传</div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="image-preview">
                      <img
                        :src="backList[0].url || backList[0].thumbUrl"
                        class="preview-img"
                        @click.stop="openPreview(backList[0].url || backList[0].thumbUrl)"
                      />
                      <div class="image-actions">
                        <el-icon class="action-icon" @click.stop="openPreview(backList[0].url || backList[0].thumbUrl)">
                          <View />
                        </el-icon>
                        <div class="action-divider"></div>
                        <el-icon class="action-icon" @click.stop="removeBack">
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div class="upload-hint">
              请上传身份证号清晰可见的身份证正反面照片,必填。支持jpg、jpeg、gif、png图片文件,单个图片小于5M
            </div>
          </div>
        </el-form-item>

        <el-form-item v-if="statusInfo.verify_status !== 'approved'">
          <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="submitVerify" :loading="submitting">
              提交
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <!-- 已上传图片展示 -->
      <div v-if="statusInfo.verify_image_url" class="uploaded-images">
        <div class="section-title">已上传认证截图：</div>
        <img
          :src="statusInfo.verify_image_url"
          alt="认证截图"
          class="uploaded-img"
          @click="openPreview(statusInfo.verify_image_url)"
        />
      </div>
    </div>

    <!-- 隐藏的文件上传输入 -->
    <input
      ref="fileInputFront"
      type="file"
      accept="image/*"
      style="display: none;"
      @change="handleFileSelect($event, 'front')"
    />
    <input
      ref="fileInputBack"
      type="file"
      accept="image/*"
      style="display: none;"
      @change="handleFileSelect($event, 'back')"
    />

    <!-- 图片预览遮罩 -->
    <div v-if="previewVisible" class="image-overlay" @click.self="closePreview">
      <img :src="previewUrl" alt="预览" class="image-overlay__img" />
      <span class="image-overlay__close" @click="closePreview">×</span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, View, Delete } from '@element-plus/icons-vue'

import apiService from '@/utils/api'

const frontList = ref([])
const backList = ref([])
const form = reactive({
  real_name: '',
  id_type: 'id_card',
  id_number: '',
  id_validity: '',
  is_long_term: false,
  id_card_front: null,
  id_card_back: null
})

const formRef = ref()
const fileInputFront = ref()
const fileInputBack = ref()
const statusInfo = reactive({
  verify_status: '',
  verify_image_url: '',
  verify_remark: ''
})
const isSubmitted = ref(false)
const submitting = ref(false)
const previewVisible = ref(false)
const previewUrl = ref('')

const rules = {
  real_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  id_type: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  id_number: [
    { required: true, message: '请输入身份证号码', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
  ],
  id_validity: [
    { required: true, message: '请选择证件有效期', trigger: 'change' }
  ],
  id_card_front: [
    { required: true, message: '请上传身份证正面照片', trigger: 'change' }
  ],
  id_card_back: [
    { required: true, message: '请上传身份证反面照片', trigger: 'change' }
  ]
}

// 获取认证状态
async function fetchStatus() {
  try {
    const res = await apiService.getVerifyStatus()
    Object.assign(statusInfo, res.data)

    // 只有已通过认证时才禁用表单
    if (res.data.verify_status === 'approved') {
      isSubmitted.value = true
    } else {
      isSubmitted.value = false
    }

    if (res.data.real_name) form.real_name = res.data.real_name
    if (res.data.id_number) form.id_number = res.data.id_number
    if (res.data.id_type) form.id_type = res.data.id_type
    if (res.data.id_validity) form.id_validity = res.data.id_validity
    if (res.data.is_long_term) form.is_long_term = res.data.is_long_term

    // 回显正面照片
    if (res.data.id_card_front_url) {
      frontList.value = [{
        name: '身份证正面',
        url: res.data.id_card_front_url
      }]
      form.id_card_front = null
    } else {
      frontList.value = []
      form.id_card_front = null
    }

    // 回显反面照片
    if (res.data.id_card_back_url) {
      backList.value = [{
        name: '身份证反面',
        url: res.data.id_card_back_url
      }]
      form.id_card_back = null
    } else {
      backList.value = []
      form.id_card_back = null
    }
  } catch (e) {
    ElMessage.error(e?.response?.data?.detail || '获取认证状态失败')
    console.error('fetchStatus error:', e)
  }
}

onMounted(fetchStatus)

function handleUploadClick(type) {
  if (type === 'front') {
    fileInputFront.value.click()
  } else {
    fileInputBack.value.click()
  }
}

function handleFileSelect(event, type) {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型和大小
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 jpg、jpeg、gif、png 格式的图片')
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 5MB')
    return
  }

  const fileObj = {
    name: file.name,
    url: URL.createObjectURL(file),
    raw: file,
    status: 'finished',
    uid: Date.now() + Math.random()
  }

  if (type === 'front') {
    form.id_card_front = file
    frontList.value = [fileObj]
  } else {
    form.id_card_back = file
    backList.value = [fileObj]
  }

  // 清空 input 值，允许重复选择同一文件
  event.target.value = ''
}

async function submitVerify() {
  try {
    await formRef.value.validate()

    // 验证有效期
    if (!form.is_long_term && !form.id_validity) {
      ElMessage.error('请选择证件有效期或勾选长期有效')
      return
    }

    submitting.value = true
    const fd = new FormData()
    fd.append('real_name', form.real_name)
    fd.append('id_type', form.id_type)
    fd.append('id_number', form.id_number)
    fd.append('id_validity', form.is_long_term ? '长期有效' : form.id_validity)
    fd.append('is_long_term', form.is_long_term)
    fd.append('id_card_front', form.id_card_front)
    fd.append('id_card_back', form.id_card_back)

    await apiService.submitVerify(fd)
    ElMessage.success('提交成功，等待审核')
    await fetchStatus()
  } catch (e) {
    if (e.name === 'ValidationError') {
      ElMessage.error('请完善表单信息')
    } else {
      ElMessage.error(e?.response?.data?.detail || '提交失败')
    }
  } finally {
    submitting.value = false
  }
}

function handleCancel() {
  // 可以跳转到其他页面或重置表单
  ElMessage.info('已取消')
}

function removeFront() {
  if (frontList.value.length > 0) {
    URL.revokeObjectURL(frontList.value[0].url)
  }
  frontList.value = []
  form.id_card_front = null
}

function removeBack() {
  if (backList.value.length > 0) {
    URL.revokeObjectURL(backList.value[0].url)
  }
  backList.value = []
  form.id_card_back = null
}

function openPreview(url) {
  previewUrl.value = url
  previewVisible.value = true
}

function closePreview() {
  previewVisible.value = false
  previewUrl.value = ''
}
</script>

<style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }

  .c-title {
    color: #999;
    font-size: 14px;
    padding: 8px 0;
  }

.verify-card {
  background: #fff;
  border: 1px solid #efefef;
  margin-top: 32px;
  padding: 20px;
}

.verify-form {
  margin-bottom: 32px;
}

.validity-container {
  display: flex;
  align-items: center;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.upload-container {
  width: 100%;
}

.upload-area {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.upload-label {
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-box {
  width: 320px;
  height: 200px;
  border: 1px dashed #efefef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  background: #f6f9f8;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #bbb;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #409eff;
}

.upload-text {
  font-size: 14px;
  color: #bbb;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.action-icon {
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-icon:hover {
  background: rgba(64, 158, 255, 0.3);
}

.action-divider {
  width: 2px;
  height: 48px;
  background-color: #555;
  margin: 0 8px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.form-actions .el-button {
  min-width: 120px;
}

.uploaded-images {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #efefef;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.uploaded-img {
  max-width: 300px;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay__img {
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.image-overlay__close {
  position: absolute;
  top: 40px;
  right: 60px;
  font-size: 40px;
  color: white;
  cursor: pointer;
  font-weight: bold;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  user-select: none;
  transition: color 0.2s;
}

.image-overlay__close:hover {
  color: #409eff;
}
</style>
