<template>
  <div class="task-market">
    <div class="title">任务广场 🛒</div>
    <div class="c-title">发现适合您的推广任务，主动申请参与</div>

    <!-- 筛选区域 -->
    <div class="filters-section">
      <div class="filter-row">
        <span class="filter-label">任务类型:</span>
        <el-button
          v-for="type in taskTypes"
          :key="type.value"
          :type="typeFilter === type.value ? 'primary' : 'default'"
          @click="setTypeFilter(type.value)"
          class="filter-btn"
        >
          {{ type.label }}
        </el-button>
      </div>

      <div class="sort-row">
        <span class="sort-label">排序:</span>
        <el-select v-model="sortBy" @change="onSortChange" style="width: 150px;">
          <el-option label="最新发布" value="create_time" />
          <el-option label="报酬金额" value="base_reward" />
          <el-option label="截止时间" value="end_date" />
        </el-select>
      </div>

      <div class="search-row">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索任务..."
          @change="onSearchChange"
        >
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div 
        class="stat-card card1" 
        :class="{ active: cardFilter === 'available' }"
        @click="setCardFilter('available')"
      >
        <div class="card-title">可申请任务</div>
        <div class="card-count">{{ availableTasksCount }}</div>
      </div>
      <div 
        class="stat-card card2" 
        :class="{ active: cardFilter === 'applications' }"
        @click="setCardFilter('applications')"
      >
        <div class="card-title">我的申请</div>
        <div class="card-count">{{ myApplicationsCount }}</div>
      </div>
      <div 
        class="stat-card card3" 
        :class="{ active: cardFilter === 'invitations' }"
        @click="setCardFilter('invitations')"
      >
        <div class="card-title">收到邀请</div>
        <div class="card-count">{{ pendingInvitationsCount }}</div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-section">
      <div v-if="loading" class="loading">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="safeTasks.length === 0" class="no-tasks">
        <el-empty description="暂无可申请的任务" />
      </div>

      <div v-else class="task-list">
        <div
          v-for="(task, index) in safeTasks"
          :key="task.id || `task-${index}`"
          class="task-card"
        >
          <div class="task-header">
            <div class="task-title">
              <span class="task-icon">📝</span>
              <span>{{ task.task_name || '未知任务' }}</span>
            </div>
            <div class="application-status" v-if="task.id && getApplicationStatus(task.id)">
              <el-tag :type="getApplicationStatusType(getApplicationStatus(task.id))" size="small">
                {{ getApplicationStatusLabel(getApplicationStatus(task.id)) }}
              </el-tag>
            </div>
          </div>

          <div class="task-content">
            <div class="task-info">
              <!-- 奖励类型 -->
              <div class="info-item">
                <span class="icon">🏆</span>
                <span>奖励类型:
                  <el-tag size="small" :type="getRewardTypeColor(getInferredRewardType(task))" effect="plain" class="reward-type-tag">
                    {{ getRewardTypeDisplayName(getInferredRewardType(task)) }}
                  </el-tag>
                </span>
              </div>

              <!-- 基础奖励 - 品牌推广和品牌+转化模式显示 -->
              <div class="info-item" v-if="getInferredRewardType(task) !== 'commission'">
                <span class="icon">💰</span>
                <span>基础奖励:
                  <span class="reward-amount base">
                    <span class="currency">$</span>
                    <span class="amount">{{ task.base_reward || 0 }}</span>
                  </span>
                </span>
              </div>

              <!-- 按转化付费FTT - 品牌+转化模式显示 -->
              <div class="info-item" v-if="getInferredRewardType(task) === 'branding_plus_conversion'">
                <span class="icon">🎯</span>
                <span>按转化付费FTT:
                  <span class="reward-amount performance">
                    <span class="currency">$</span>
                    <span class="amount">{{ task.performance_rate || 0 }}</span>
                    <span class="unit">/{{ getPerformanceUnit(task.task_type) }}</span>
                  </span>
                </span>
              </div>

              <!-- 佣金比例 - 带单返佣模式显示 -->
              <div class="info-item" v-if="getInferredRewardType(task) === 'commission'">
                <span class="icon">💸</span>
                <span>佣金比例:
                  <span class="reward-amount commission">
                    <span class="amount">{{ task.commission_rate || 0 }}</span>
                    <span class="unit">%</span>
                  </span>
                </span>
              </div>

              <div class="info-item">
                <span class="icon">⏰</span>
                <span>截止时间: {{ formatDate(task.end_date) }}</span>
              </div>
              <div class="info-item">
                <span class="icon">🏷️</span>
                <span>任务类型: {{ getTaskTypeLabel(task.task_type) }}</span>
              </div>
              <div class="info-item" v-if="task.description">
                <span class="icon">📝</span>
                <span>要求: {{ task.description }}</span>
              </div>
            </div>
          </div>
          <div class="task-actions">
            <el-button @click="viewTaskDetail(task)">
              查看详情
            </el-button>

            <!-- 任务操作按钮 -->
            <el-tag v-if="hasInvitation(task.id)" 
              :type="getInvitationStatusType(getInvitationStatus(task.id))" 
              size="large"
            >
              {{ getInvitationStatusText(getInvitationStatus(task.id)) }}
            </el-tag>
            
            <el-tag v-else-if="getApplicationStatus(task.id)" 
              :type="getApplicationStatusType(getApplicationStatus(task.id))" 
              size="large"
            >
              {{ getApplicationStatusLabel(getApplicationStatus(task.id)) }}
            </el-tag>
            
            <el-button 
              v-else
              type="primary" 
              @click="applyForTask(task)"
              :disabled="task.task_status !== 'published'"
            >
              立即申请
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-if="totalTasks > pageSize"
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalTasks"
        layout="total, prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 申请弹窗 -->
    <TaskApplyDialog
      v-if="selectedTask"
      v-model="applyDialogVisible"
      :task="selectedTask"
      @success="handleApplySuccess"
    />

    <!-- 任务详情弹窗 -->
    <el-drawer
      v-model="taskDetailVisible"
      title="任务详情"
      size="50%"
      :before-close="() => taskDetailVisible = false"
      class="task-detail-dialog"
    >
      <div v-if="safeSelectedTask" class="task-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">📋 基本信息</h3>
          <div class="info-grid">
            <div class="info-row">
              <span class="info-label">任务名称:</span>
              <span class="info-value">{{ safeSelectedTask.task_name || '' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">任务类型:</span>
              <span class="info-value">{{ getTaskTypeLabel(safeSelectedTask.task_type) }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">奖励类型:</span>
              <span class="info-value">
                <el-tag size="small" :type="getRewardTypeColor(getInferredRewardType(safeSelectedTask))" effect="plain">
                  {{ getRewardTypeDisplayName(getInferredRewardType(safeSelectedTask)) }}
                </el-tag>
              </span>
            </div>
            <!-- 基础奖励 - 品牌推广和品牌+转化模式显示 -->
            <div class="info-row" v-if="getInferredRewardType(safeSelectedTask) !== 'commission'">
              <span class="info-label">基础报酬:</span>
              <span class="info-value reward-amount">${{ safeSelectedTask.base_reward || 0 }}</span>
            </div>
            <!-- 效果奖励 - 品牌+转化模式显示 -->
            <div class="info-row" v-if="getInferredRewardType(safeSelectedTask) === 'branding_plus_conversion'">
              <span class="info-label">效果奖励:</span>
              <span class="info-value reward-amount">${{ safeSelectedTask.performance_rate || 0 }}/注册</span>
            </div>
            <!-- 佣金比例 - 带单返佣模式显示 -->
            <div class="info-row" v-if="getInferredRewardType(safeSelectedTask) === 'commission'">
              <span class="info-label">佣金比例:</span>
              <span class="info-value reward-amount">{{ safeSelectedTask.commission_rate || 0 }}%</span>
            </div>
            <div class="info-row">
              <span class="info-label">开始时间:</span>
              <span class="info-value">{{ formatDate(safeSelectedTask.start_date) || '立即开始' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">截止时间:</span>
              <span class="info-value">{{ formatDate(safeSelectedTask.end_date) }}</span>
            </div>
            <!-- 🔧 修复：优先显示用户与任务的关系状态 -->
            <div class="info-row" v-if="safeSelectedTask.id && getApplicationStatus(safeSelectedTask.id)">
              <span class="info-label">我的状态:</span>
              <el-tag :type="getApplicationStatusType(getApplicationStatus(safeSelectedTask.id))" size="small">
                {{ getApplicationStatusLabel(getApplicationStatus(safeSelectedTask.id)) }}
              </el-tag>
            </div>
            <div class="info-row" v-else-if="safeSelectedTask.id && hasInvitation(safeSelectedTask.id)">
              <span class="info-label">邀请状态:</span>
              <el-tag :type="getInvitationStatusType(getInvitationStatus(safeSelectedTask.id))" size="small">
                {{ getInvitationStatusText(getInvitationStatus(safeSelectedTask.id)) }}
              </el-tag>
            </div>
            <div class="info-row">
              <span class="info-label">任务状态:</span>
              <el-tag type="info" size="small">{{ getTaskStatusLabel(safeSelectedTask.task_status) }}</el-tag>
            </div>
            <div class="info-row" v-if="safeSelectedTask.target_kol_count">
              <span class="info-label">目标KOL数:</span>
              <span class="info-value">{{ safeSelectedTask.target_kol_count }}人</span>
            </div>
          </div>
        </div>

        <!-- 任务要求 -->
        <div class="detail-section" v-if="safeSelectedTask.description">
          <h3 class="section-title">📝 任务要求</h3>
          <div class="content-box">
            <p class="task-description">{{ safeSelectedTask.description }}</p>
          </div>
        </div>

        <!-- 官方素材 -->
        <div class="detail-section" v-if="safeSelectedTask.official_materials">
          <h3 class="section-title">🎨 官方素材</h3>
          <div class="content-box">
            <div class="materials-preview">
              <div
                  v-for="(material, index) in parseMaterials(safeSelectedTask.official_materials)"
                  :key="`material-${index}`"
                  class="material-item"
              >
                <!-- 图片预览 -->
                <div v-if="material.type === 'image'" class="material-image">
                  <el-image
                      :src="material.url"
                      :preview-src-list="[material.url]"
                      fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon>
                          <Picture/>
                        </el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="material.type === 'video'" class="material-video">
                  <video
                      :src="material.url"
                      controls
                      preload="metadata"
                      class="video-player"
                  ></video>
                </div>

                <!-- 链接预览 -->
                <div v-else class="material-link">
                  <el-link :href="material.url || material" target="_blank" type="primary">
                    <el-icon>
                      <Link/>
                    </el-icon>
                    素材链接
                  </el-link>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'
import { Search, Link } from '@element-plus/icons-vue'
import TaskApplyDialog from '@/components/TaskApplyDialog.vue'
import { useUserStore } from '@/store/user'

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 数据状态
const tasks = ref([])
const myApplications = ref([])
const invitationsData = ref([])  // 完整的邀请数据
const pendingInvitations = ref(0)  // 保留兼容性
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)

// 筛选状态
const typeFilter = ref('')
const sortBy = ref('create_time')
const searchKeyword = ref('')
const cardFilter = ref('') // 新增：卡片筛选状态 'available' | 'applications' | 'invitations' | ''

// 设置卡片筛选
function setCardFilter(filterType) {
  cardFilter.value = filterType === cardFilter.value ? '' : filterType
  console.log('设置卡片筛选:', cardFilter.value)
}

// 弹窗状态
const applyDialogVisible = ref(false)
const taskDetailVisible = ref(false)
const selectedTask = ref(null)

// 任务类型选项
const taskTypes = [
  { label: '全部', value: '' },
  { label: '推文', value: 'post' },
  { label: '视频', value: 'video' },
  { label: '文章', value: 'article' },
  { label: '直播', value: 'live_stream' },
  { label: 'AMA', value: 'ama_activity' }
]

// 计算属性 - 交叉匹配筛选逻辑
const filteredMyApplications = computed(() => {
  const currentUserId = userInfo.value?.id
  if (!currentUserId) return []

  // 获取所有已发布的任务（数据源A）
  const publishedTasks = tasks.value || []

  // 获取我申请的任务（数据源B）
  const myApps = myApplications.value || []

  // 交叉匹配：A和B的任务ID一样 AND B的kol_id等于当前用户ID
  let matched = []
  publishedTasks.forEach(publishedTask => {
    const matchedApp = myApps.find(app =>
      app.task_id === publishedTask.id && app.kol_id === currentUserId
    )
    if (matchedApp) {
      // 使用已发布任务的数据，确保是最新状态
      matched.push({
        ...publishedTask,
        application_status: matchedApp.status,  // 保留申请状态
        application_id: matchedApp.id           // 保留申请ID
      })
    }
  })

  // 按任务类型筛选
  if (typeFilter.value) {
    matched = matched.filter(task => task.task_type === typeFilter.value)
  }

  // 按关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    matched = matched.filter(task =>
      (task.task_name && task.task_name.toLowerCase().includes(keyword)) ||
      (task.description && task.description.toLowerCase().includes(keyword))
    )
  }

  return matched
})

const filteredInvitations = computed(() => {
  const currentUserId = userInfo.value?.id
  if (!currentUserId) return []

  // 获取所有已发布的任务（数据源A）
  const publishedTasks = tasks.value || []

  // 获取邀请我的任务（数据源C）
  const invitations = invitationsData.value || []

  // 交叉匹配：A和C的任务ID一样 AND C的kol_id等于当前用户ID AND C的状态为pending
  let matched = []
  publishedTasks.forEach(publishedTask => {
    const matchedInv = invitations.find(inv =>
      inv.task_id === publishedTask.id &&
      inv.kol_id === currentUserId &&
      inv.status === 'pending'
    )
    if (matchedInv) {
      // 使用已发布任务的数据，确保是最新状态
      matched.push({
        ...publishedTask,
        invitation_status: matchedInv.status,  // 保留邀请状态
        invitation_id: matchedInv.id           // 保留邀请ID
      })
    }
  })

  // 按任务类型筛选
  if (typeFilter.value) {
    matched = matched.filter(task => task.task_type === typeFilter.value)
  }

  // 按关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    matched = matched.filter(task =>
      (task.task_name && task.task_name.toLowerCase().includes(keyword)) ||
      (task.description && task.description.toLowerCase().includes(keyword))
    )
  }

  return matched
})

// 可申请任务：所有已发布任务 - 我申请的任务 - 邀请我的任务
const filteredAvailableTasks = computed(() => {
  const currentUserId = userInfo.value?.id
  if (!currentUserId) return []

  // 获取所有已发布的任务
  let availableTasks = tasks.value || []

  // 获取我申请过的任务ID列表
  const myApplicationTaskIds = (myApplications.value || [])
    .filter(app => app.kol_id === currentUserId)
    .map(app => app.task_id)

  // 获取邀请我的任务ID列表
  const invitationTaskIds = (invitationsData.value || [])
    .filter(inv => inv.kol_id === currentUserId)
    .map(inv => inv.task_id)

  // 排除我申请的和邀请我的任务
  availableTasks = availableTasks.filter(task =>
    !myApplicationTaskIds.includes(task.id) &&
    !invitationTaskIds.includes(task.id)
  )

  // 按任务类型筛选
  if (typeFilter.value) {
    availableTasks = availableTasks.filter(task => task.task_type === typeFilter.value)
  }

  // 按关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    availableTasks = availableTasks.filter(task =>
      (task.task_name && task.task_name.toLowerCase().includes(keyword)) ||
      (task.description && task.description.toLowerCase().includes(keyword))
    )
  }

  return availableTasks
})

const myApplicationsCount = computed(() => filteredMyApplications.value.length)
const pendingInvitationsCount = computed(() => filteredInvitations.value.length)
const availableTasksCount = computed(() => filteredAvailableTasks.value.length)

// 安全的任务数据访问
const safeTasks = computed(() => {
  let tasksToShow = []
  
  // 根据卡片筛选决定显示哪些任务
  switch (cardFilter.value) {
    case 'available':
      tasksToShow = filteredAvailableTasks.value
      break
    case 'applications':
      tasksToShow = filteredMyApplications.value
      break
    case 'invitations':
      tasksToShow = filteredInvitations.value
      break
    default:
      // 默认显示所有已发布的任务
      tasksToShow = tasks.value.filter(task => task && typeof task === 'object' && task.id)
  }
  
  return tasksToShow
})

const safeSelectedTask = computed(() => {
  return selectedTask.value && typeof selectedTask.value === 'object' ? selectedTask.value : null
})

// 方法
async function fetchTasks() {
  loading.value = true
  console.log('开始获取任务数据...')
  try {
    console.log('发送API请求...')
    const [tasksResponse, applicationsResponse, invitationsResponse] = await Promise.all([
      apiService.getPublishedKolTasks({
        page: currentPage.value,
        size: pageSize.value,
        task_type: typeFilter.value || undefined,
        sort: sortBy.value,
        keyword: searchKeyword.value || undefined
      }),
      apiService.getKolMyApplications(),
      apiService.getKolInvitations()
    ])

    console.log('API响应:', {
      tasksResponse: tasksResponse.data,
      applicationsResponse: applicationsResponse.data,
      invitationsResponse: invitationsResponse.data
    })

    if (tasksResponse.data) {
      const taskData = tasksResponse.data.data || []
      console.log('处理任务数据:', taskData)
      console.log('任务数据长度:', taskData.length)

      // 验证每个task对象的结构
      taskData.forEach((task, index) => {
        if (!task || typeof task !== 'object') {
          console.error(`Task at index ${index} is not a valid object:`, task)
        } else if (!task.id) {
          console.error(`Task at index ${index} missing id:`, task)
        } else {
          console.log(`Task ${index}:`, {
            id: task.id,
            name: task.task_name,
            status: task.task_status,
            type: task.task_type
          })
        }
      })

      tasks.value = taskData
      totalTasks.value = tasksResponse.data.total || 0
      console.log('设置任务数据完成:', {
        tasksCount: tasks.value.length,
        totalTasks: totalTasks.value
      })
    } else {
      console.error('tasksResponse.data 为空:', tasksResponse)
    }

    myApplications.value = applicationsResponse.data || []
    invitationsData.value = invitationsResponse.data || []  // 保存完整邀请数据
    pendingInvitations.value = (invitationsResponse.data || []).filter(inv => inv.status === 'pending').length  // 保留兼容性

  } catch (error) {
    console.error('获取数据失败:', error)
    console.error('错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    })
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
    console.log('fetchTasks 完成')
  }
}

function getApplicationStatus(taskId) {
  const application = myApplications.value.find(app => app.task_id === taskId)
  return application ? application.status : null
}

function getApplicationStatusType(status) {
  const types = {
    'pending': 'warning',
    'accepted': 'success',
    'rejected': 'danger'
  }
  return types[status] || 'info'
}

function getApplicationStatusLabel(status) {
  const labels = {
    'pending': '申请中',
    'accepted': '已接受',
    'rejected': '已拒绝'
  }
  return labels[status] || status
}

function setTypeFilter(type) {
  typeFilter.value = type
  currentPage.value = 1
  fetchTasks()
}

function onSortChange() {
  currentPage.value = 1
  fetchTasks()
}

function onSearchChange() {
  currentPage.value = 1
  fetchTasks()
}

function handlePageChange(page) {
  currentPage.value = page
  fetchTasks()
}

function applyForTask(task) {
  // 验证task对象
  if (!task || typeof task !== 'object') {
    console.error('Invalid task object passed to applyForTask:', task)
    ElMessage.error('任务信息无效，请刷新页面重试')
    return
  }

  if (!task.id) {
    console.error('Task missing required id property:', task)
    ElMessage.error('任务ID无效，请刷新页面重试')
    return
  }

  console.log('Opening apply dialog for task:', task)
  selectedTask.value = task
  applyDialogVisible.value = true
}

function viewTaskDetail(task) {
  selectedTask.value = task
  taskDetailVisible.value = true
}

function handleApplySuccess() {
  applyDialogVisible.value = false
  fetchTasks() // 刷新数据
}



function getTaskStatusLabel(status) {
  const labels = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '已分配',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝',
    'running': '运行中',  // 🔧 修复：添加缺失的运行中状态
    'completed': '已完成',
    'cancelled': '已关闭'
  }
  return labels[status] || status
}

function parseMaterials(materials) {
  if (!materials) return []

  // 如果已经是数组格式，直接返回
  if (Array.isArray(materials)) {
    return materials
  }

  // 如果是字符串，尝试解析为JSON
  if (typeof materials === 'string') {
    try {
      const parsed = JSON.parse(materials)
      if (Array.isArray(parsed)) {
        return parsed
      }
    } catch (error) {
      // JSON解析失败，按逗号分隔处理（向后兼容）
      return materials.split(',')
        .map(link => link.trim())
        .filter(link => link && link.length > 0)
        .map(link => ({ url: link, type: 'link' })) // 转换为统一格式
    }
  }

  return []
}

// 工具函数
function getTaskTypeLabel(type) {
  const labels = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA'
  }
  return labels[type] || type
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 奖励类型相关函数
function getInferredRewardType(task) {
  // 如果有明确的 reward_type 字段，直接使用
  if (task.reward_type) {
    return task.reward_type
  }

  // 根据任务数据推断奖励类型
  if (task.commission_rate && task.commission_rate > 0) {
    return 'commission'  // 带单返佣
  } else if (task.performance_rate && task.performance_rate > 0) {
    return 'branding_plus_conversion'  // 品牌+转化
  } else {
    return 'branding'  // 品牌推广
  }
}

function getRewardTypeDisplayName(type) {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌+转化'
  }
  return typeMap[type] || '品牌推广'
}

function getRewardTypeColor(type) {
  const colorMap = {
    'branding': 'info',
    'commission': 'success',
    'branding_plus_conversion': 'warning'
  }
  return colorMap[type] || 'info'
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function getPerformanceUnit(taskType) {
  const unitMap = {
    'post': '个',
    'video': '个',
    'article': '个',
    'live_stream': '场',
    'ama_activity': '场'
  }
  return unitMap[taskType] || '个'
}

function hasInvitation(taskId) {
  // 检查邀请我的任务列表中是否有该任务
  const invitation = invitationsData.value.find(inv => inv.task_id === taskId)
  if (invitation) {
    console.log(`任务 ${taskId} 的邀请状态:`, invitation.status)
    return true
  }
  return false
}

function getInvitationStatus(taskId) {
  const invitation = invitationsData.value.find(inv => inv.task_id === taskId)
  return invitation ? invitation.status : null
}

function getInvitationStatusText(status) {
  const statusMap = {
    'pending': '邀请中',
    'accepted': '已接受',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

function getInvitationStatusType(status) {
  const typeMap = {
    'pending': 'warning',
    'accepted': 'success', 
    'rejected': 'danger',
    'expired': 'info'
  }
  return typeMap[status] || 'info'
}

onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
}

.c-title {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
}

.filters-section {
  margin-top: 32px;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 320px;
  &>div {
    margin-right: 20px;
  }
  .search-row {
    position: absolute;
    right: 0;
  }
}


.filter-row:last-child, .sort-row:last-child, .search-row:last-child {
  margin-bottom: 0;
}

.filter-label, .sort-label {
  margin-right: 12px;
  font-weight: bold;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-card {
  margin-top: 32px;
  padding: 20px 0;
  text-align: center;
  color: #fff;
  background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.stat-card.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  pointer-events: none;
}

.stat-card.card2 {
  background: linear-gradient(to bottom right, #e74888, #bc53a1);
}

.stat-card.card3 {
  background: linear-gradient(to bottom right, #825dbf, #5345b4);
}

.stat-card.card4 {
  background: linear-gradient(to bottom right, #fbb728, #f68254);
}
  .card-title {
    font-size: 14px;
    margin-bottom: 8px;
  }
  .card-count {
    font-size: 24px;
    font-weight: bold;
  }


.tasks-section {
  margin-top: 32px;
  display: flex;
}

.task-list {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-row-gap: 20px;
  grid-column-gap: 20px;
}

.task-card {
  background: #fff;
  border: 1px solid #efefef;
  padding: 20px;
  transition: all 0.3s;
  box-shadow: #eee 2px 1px 2px;
}

.task-card:hover {
  border-color: #ddd;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.task-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.task-icon {
  margin-right: 8px;
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.task-info {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item .icon {
  margin-right: 8px;
  width: 20px;
}

.task-actions {
  display: flex;
  gap: 10px;
  padding-top: 8px;
}

.no-tasks {
  text-align: center;
  padding: 40px;
}

.loading {
  padding: 20px;
}

.task-detail-content {
  padding: 20px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #efefef;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-label {
  min-width: 100px;
  font-weight: 500;
  margin-right: 12px;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
}

/* 奖励类型和金额样式 */
.reward-type-tag {
  font-size: 11px;
  font-weight: 500;
  border-radius: 3px;
  padding: 1px 6px;
  margin-left: 4px;
}

.reward-amount {
  display: inline-flex;
  align-items: baseline;
  gap: 1px;
  font-weight: 600;
  margin-left: 4px;
}

.reward-amount.base .currency,
.reward-amount.base .amount {
  color: #67C23A;
}

.reward-amount.performance .currency,
.reward-amount.performance .amount {
  color: #E6A23C;
}

.reward-amount.commission .amount {
  color: #409EFF;
}

.reward-amount .currency {
  font-size: 11px;
}

.reward-amount .amount {
  font-size: 13px;
  font-weight: bold;
}

.reward-amount .unit {
  font-size: 10px;
  color: #999;
  margin-left: 1px;
}

.content-box {
  background: #f6f9f8;
  border: 1px solid #efefef;
  padding: 12px;
}

.task-description {
  white-space: pre-wrap;
  line-height: 1.6;
  margin: 0;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.material-item {
  border: 1px solid #efefef;
  border-radius: 8px;
  overflow: hidden;
}

.material-image .el-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.material-video .video-player {
  width: 100%;
  max-height: 150px;
}

.material-link {
  padding: 16px;
  word-break: break-all;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  background: #f5f5f5;
  color: #999;
}

.status-tag {
  font-size: 14px !important;
  padding: 8px 16px !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

</style>
