<template>
  <div class="certificate-container">
    <div class="title">公司资质认证</div>
    <div class="c-title">请上传公司相关证书，完成资质认证</div>
    <div class="certificate-card">
      <!-- 状态提示 -->
      <el-alert
        v-if="statusInfo.verify_status === 'approved'"
        title="认证已通过"
        type="success"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />
      <el-alert
        v-else-if="statusInfo.verify_status === 'pending'"
        title="认证信息已提交，正在审核中"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />
      <el-alert
        v-else-if="statusInfo.verify_status === 'rejected'"
        :title="'认证未通过' + (statusInfo.verify_remark ? '：' + statusInfo.verify_remark : '')"
        type="error"
        show-icon
        :closable="false"
        style="margin-bottom: 24px;"
      />

      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="120px"
        class="certificate-form"
        :disabled="statusInfo.verify_status === 'approved'"
      >
        <el-form-item label="公司名称" prop="company_name" required>
          <el-input
            v-model="form.company_name"
            placeholder="请输入公司名称,必填"
            maxlength="255"
            show-word-limit
            style="width: 400px;"
          />
        </el-form-item>

        <el-form-item label="证书类型" prop="certificate_type" required>
          <el-select v-model="form.certificate_type" placeholder="请选择证书类型" style="width: 400px;">
            <el-option label="公司资质证书" value="business_license" />
            <!-- <el-option label="组织机构代码证" value="org_code" />
            <el-option label="税务登记证" value="tax_registration" />
            <el-option label="其他证书" value="other" /> -->
          </el-select>
        </el-form-item>

        <el-form-item label="证书图片" prop="certificate_file" required>
          <div class="upload-container">
            <div class="upload-area">
              <div
                class="upload-box"
                @click="certificateList.length === 0 && handleUploadClick()"
              >
                <template v-if="certificateList.length === 0">
                  <div class="upload-placeholder">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text">点击上传证书图片</div>
                  </div>
                </template>
                <template v-else>
                  <div class="image-preview">
                    <img
                      :src="certificateList[0].url || certificateList[0].thumbUrl"
                      class="preview-img"
                      @click.stop="openPreview(certificateList[0].url || certificateList[0].thumbUrl)"
                    />
                    <div class="image-actions">
                      <el-icon class="action-icon" @click.stop="openPreview(certificateList[0].url || certificateList[0].thumbUrl)">
                        <View />
                      </el-icon>
                      <div class="action-divider"></div>
                      <el-icon class="action-icon" @click.stop="removeCertificate">
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <div class="upload-hint">
              请上传清晰的公司证书图片,必填。支持jpg、jpeg、gif、png图片文件,单个图片小于5M
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="form-actions">
            <el-button 
              :type="cancelButtonType" 
              @click="handleCancel"
            >
              {{ cancelButtonText }}
            </el-button>
            <el-button 
              v-if="showSubmitButton"
              type="primary" 
              @click="submitCertificate" 
              :loading="submitting"
            >
              {{ submitButtonText }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 隐藏的文件上传输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none;"
      @change="handleFileSelect"
    />

    <!-- 图片预览遮罩 -->
    <div v-if="previewVisible" class="image-overlay" @click.self="closePreview">
      <img :src="previewUrl" alt="预览" class="image-overlay__img" />
      <span class="image-overlay__close" @click="closePreview">×</span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View, Delete } from '@element-plus/icons-vue'

import apiService from '@/utils/api'

const certificateList = ref([])
const form = reactive({
  company_name: '',
  certificate_type: '',
  certificate_file: null
})

const formRef = ref()
const fileInput = ref()
const statusInfo = reactive({
  verify_status: '',
  certificate_url: '',
  verify_remark: '',
  company_name: '',
  certificate_type: ''
})
const isSubmitted = ref(false)
const submitting = ref(false)
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算取消按钮的文本
const cancelButtonText = computed(() => {
  switch (statusInfo.verify_status) {
    case 'pending':
      return '撤回申请'
    case 'rejected':
      return '重置'
    case 'approved':
      return '返回工作台'
    default:
      return '取消'
  }
})

// 计算取消按钮的类型
const cancelButtonType = computed(() => {
  switch (statusInfo.verify_status) {
    case 'pending':
      return 'warning'
    case 'rejected':
      return 'default'
    case 'approved':
      return 'success'
    default:
      return 'default'
  }
})

// 计算提交按钮的显示逻辑
const showSubmitButton = computed(() => {
  return !statusInfo.verify_status || statusInfo.verify_status === 'rejected'
})

// 计算提交按钮的文本
const submitButtonText = computed(() => {
  return statusInfo.verify_status === 'rejected' ? '重新提交' : '提交'
})

const rules = {
  company_name: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 2, max: 255, message: '公司名称长度在 2 到 255 个字符', trigger: 'blur' }
  ],
  certificate_type: [
    { required: true, message: '请选择证书类型', trigger: 'change' }
  ],
  certificate_file: [
    { required: true, message: '请上传证书图片', trigger: 'change' }
  ]
}

// 获取认证状态
async function fetchStatus() {
  try {
    const res = await apiService.getCompanyCertificateStatus()
    if (res.data) {
      Object.assign(statusInfo, res.data)

      // 只有已通过认证时才禁用表单
      if (res.data.verify_status === 'approved') {
        isSubmitted.value = true
      } else {
        isSubmitted.value = false
      }

      if (res.data.company_name) form.company_name = res.data.company_name
      if (res.data.certificate_type) form.certificate_type = res.data.certificate_type

      // 回显证书图片
      if (res.data.certificate_url) {
        certificateList.value = [{
          name: '公司证书',
          url: res.data.certificate_url
        }]
        form.certificate_file = null
      } else {
        certificateList.value = []
        form.certificate_file = null
      }
    }
  } catch (e) {
    // 如果没有认证记录，这是正常的，不需要显示错误
    if (e?.response?.status !== 404) {
      ElMessage.error(e?.response?.data?.detail || '获取认证状态失败')
      console.error('fetchStatus error:', e)
    }
  }
}

onMounted(fetchStatus)

function handleUploadClick() {
  fileInput.value.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型和大小
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 jpg、jpeg、gif、png 格式的图片')
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 5MB')
    return
  }

  const fileObj = {
    name: file.name,
    url: URL.createObjectURL(file),
    raw: file,
    status: 'finished',
    uid: Date.now() + Math.random()
  }

  form.certificate_file = file
  certificateList.value = [fileObj]

  // 清空 input 值，允许重复选择同一文件
  event.target.value = ''
}

async function submitCertificate() {
  try {
    await formRef.value.validate()

    submitting.value = true
    const fd = new FormData()
    fd.append('company_name', form.company_name)
    fd.append('certificate_type', form.certificate_type)
    fd.append('certificate_file', form.certificate_file)

    await apiService.verifyCompanyCertificate(fd)
    ElMessage.success('提交成功，等待审核')
    await fetchStatus()
  } catch (e) {
    if (e.name === 'ValidationError') {
      ElMessage.error('请完善表单信息')
    } else {
      ElMessage.error(e?.response?.data?.detail || '提交失败')
    }
  } finally {
    submitting.value = false
  }
}

function handleCancel() {
  // 根据认证状态执行不同的取消操作
  if (statusInfo.verify_status === 'pending') {
    // 待审核状态：撤回申请
    ElMessageBox.confirm(
      '确定要撤回认证申请吗？撤回后可以重新提交',
      '撤回申请',
      {
        confirmButtonText: '确定撤回',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(async () => {
      try {
        await apiService.withdrawCompanyCertificate()
        ElMessage.success('申请已撤回')
        await fetchStatus()
        // 刷新页面以确保状态完全更新
        window.location.reload()
      } catch (e) {
        ElMessage.error(e?.response?.data?.detail || '撤回失败')
      }
    }).catch(() => {
      // 用户取消操作
    })
  } else if (statusInfo.verify_status === 'rejected') {
    // 被拒绝状态：重置表单，允许重新提交
    ElMessageBox.confirm(
      '确定要重置表单吗？已填写的内容将丢失',
      '重置表单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      resetForm()
    }).catch(() => {
      // 用户取消操作
    })
  } else if (statusInfo.verify_status === 'approved') {
    // 已通过状态：返回工作台
    ElMessageBox.confirm(
      '认证已通过，确定要返回工作台吗？',
      '返回工作台',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success',
      }
    ).then(() => {
      // 这里可以跳转到工作台或关闭页面
      ElMessage.info('已返回工作台')
    }).catch(() => {
      // 用户取消操作
    })
  } else {
    // 未认证状态：重置表单
    ElMessageBox.confirm(
      '确定要取消认证吗？已填写的内容将丢失',
      '取消认证',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning',
      }
    ).then(() => {
      resetForm()
    }).catch(() => {
      // 用户取消操作，继续编辑
    })
  }
}

// 重置表单函数
function resetForm() {
  // 重置表单数据
  form.company_name = ''
  form.certificate_type = ''
  form.certificate_file = null
  
  // 清除文件列表
  if (certificateList.value.length > 0) {
    URL.revokeObjectURL(certificateList.value[0].url)
  }
  certificateList.value = []
  
  // 重置表单验证状态
  formRef.value?.resetFields()
  
  ElMessage.success('表单已重置')
}

function removeCertificate() {
  if (certificateList.value.length > 0) {
    URL.revokeObjectURL(certificateList.value[0].url)
  }
  certificateList.value = []
  form.certificate_file = null
}

function openPreview(url) {
  previewUrl.value = url
  previewVisible.value = true
}

function closePreview() {
  previewVisible.value = false
  previewUrl.value = ''
}
</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
}

.c-title {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
}

.certificate-card {
  background: #fff;
  border: 1px solid #efefef;
  margin-top: 32px;
  padding: 20px;
}

.certificate-form {
  margin-bottom: 32px;
}

.upload-container {
  width: 100%;
}

.upload-area {
  margin-bottom: 12px;
}

.upload-box {
  width: 400px;
  height: 250px;
  border: 1px dashed #efefef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  background: #f6f9f8;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #bbb;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #409eff;
}

.upload-text {
  font-size: 14px;
  color: #bbb;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.action-icon {
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-icon:hover {
  background: rgba(64, 158, 255, 0.3);
}

.action-divider {
  width: 2px;
  height: 48px;
  background-color: #555;
  margin: 0 8px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.form-actions .el-button {
  min-width: 120px;
}

.uploaded-images {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #efefef;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.uploaded-img {
  max-width: 400px;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-overlay__img {
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.image-overlay__close {
  position: absolute;
  top: 40px;
  right: 60px;
  font-size: 40px;
  color: white;
  cursor: pointer;
  font-weight: bold;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  user-select: none;
  transition: color 0.2s;
}

.image-overlay__close:hover {
  color: #409eff;
}
</style> 