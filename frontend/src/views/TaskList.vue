<template>
  <div>
    <h2>任务列表</h2>
    <div v-for="task in tasks" :key="task.id" style="border:1px solid #eee;padding:12px;margin-bottom:12px;">
      <div>任务名：{{ task.task_name }}</div>
      <TaskStatusChanger
        :task="task"
        :operatorId="userId"
        :onStatusChanged="fetchTasks"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

import TaskStatusChanger from '../components/TaskStatusChanger.vue'
import apiService from '@/utils/api'

const userId = Number(localStorage.getItem('userId'))
const tasks = ref([])

const fetchTasks = async () => {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('📋 User not logged in, skipping task list fetch')
    tasks.value = []
    return
  }

  try {
    // 假设后端有 /api/tasks?user_id=xxx
    const res = await apiService.getMarketingTasks({ user_id: userId })
    tasks.value = res.data
  } catch (error) {
    console.error('获取任务列表失败:', error)
    tasks.value = []
  }
}

onMounted(fetchTasks)
</script>