<template>
  <div>
    <div class="title">发现、筛选并邀请最适合您营销活动的KOL</div>
    <div class="form">
      <el-input v-model="search" placeholder="输入KOL名称..." style="width:240px;" clearable />
      <el-select v-model="platformFilter" placeholder="所有平台" style="width:140px;">
        <el-option label="所有平台" value="" />
        <el-option v-for="item in platformOptions" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="tagFilter" placeholder="所有领域" style="width:140px;">
        <el-option label="所有领域" value="" />
        <el-option v-for="item in tagOptions" :key="item" :label="item" :value="item" />
      </el-select>
    </div>
    <div class="cards">
      <div
        v-for="kol in filteredKOLs"
        :key="kol.id"
        class="card"
        style=""
      >
        <div class="baseinfo">
          <div :style="avatarStyle(kol.avatar_color)" class="avatar">
            {{ kol.initial || kol.platform_username.charAt(0) }}
          </div>
          <div>
            <div class="name">{{ kol.platform_username }}</div>
            <div class="platform">{{ kol.platform }} | {{ kol.platform_name || kol.platform_username }}</div>
            <!-- 添加系统用户名显示 -->
            <div class="system-username" v-if="kol.username">
              <span class="label">系统用户名:</span>
              <span class="value">{{ kol.username }}</span>
            </div>
          </div>
        </div>
        <div class="tags">
          <el-tag
            v-for="tag in kol.tag_name ? kol.tag_name.split(',') : []"
            :key="tag"
            size="small"
          >{{ tag }}</el-tag>
        </div>
        <div class="fans">
          <div class="count">粉丝数<br><span>{{ formatNumber(kol.followers_count) }}</span></div>
          <!-- 暂时不要，后面在加上 -->
          <!-- <div class="status">合作状态<br>
            <el-tag
              :type="cooperationTagType(kol.cooperation_status)"
              effect="dark"
            >{{ kol.cooperation_status || '未知状态' }}</el-tag>
          </div> -->
        </div>
        <div class="btns">
          <el-button size="small" @click="viewKOLDetail(kol)">查看资料</el-button>
          <el-button
            size="small"
            :type="getInviteButtonType(kol)"
            :disabled="getInviteButtonDisabled(kol)"
            @click="openInviteDialog(kol)"
          >
            {{ getInviteButtonText(kol) }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 邀请对话框 -->
    <el-dialog
      v-model="inviteDialogVisible"
      title="邀请KOL合作"
      width="500px"
      :before-close="handleInviteDialogClose"
    >
      <div v-if="selectedKOL">
        <div style="margin-bottom:16px;">
          <div style="font-weight:bold; margin-bottom:8px;">KOL信息</div>
          <div style="background:#f5f5f5; padding:12px; border-radius:8px; line-height: 2;">
            <div><strong>平台用户名：</strong>{{ selectedKOL.platform_username }}</div>
            <div v-if="selectedKOL.username"><strong>系统用户名：</strong>{{ selectedKOL.username }}</div>
            <div><strong>平台：</strong>{{ selectedKOL.platform }}</div>
            <div><strong>粉丝数：</strong>{{ formatNumber(selectedKOL.followers_count) }}</div>
            <div><strong>标签：</strong>{{ selectedKOL.tag_name }}</div>
          </div>
        </div>

        <div style="margin-bottom:16px;">
          <div style="font-weight:bold; margin-bottom:8px;">选择任务</div>
          <el-select v-model="selectedTaskId" placeholder="请选择要邀请的任务" style="width:100%;">
            <el-option
              v-for="task in filteredAvailableTasks"
              :key="task.id"
              :label="`${task.task_name} (${task.task_type}) - $${task.base_reward}`"
              :value="task.id"
            >
              <span style="float: left">{{ task.task_name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">${{ task.base_reward }}</span>
            </el-option>
          </el-select>
          <div v-if="selectedTaskId" style="margin-top:8px; padding:8px; background:#f5f5f5; border-radius:4px; font-size:12px; color:#666;">
            {{ getSelectedTaskDescription() }}
          </div>
        </div>

        <div style="margin-bottom:16px;">
          <div style="font-weight:bold; margin-bottom:8px;">渠道码 <span style="color: #f56c6c;">*</span></div>
          <el-input
            v-model="channelCode"
            placeholder="请输入渠道码"
            style="width:100%;"
            required
          />
          <div style="margin-top:4px; font-size:12px; color:#666;">
            渠道码用于追踪KOL的推广效果，请务必填写
          </div>
        </div>

        <div style="margin-bottom:16px;">
          <div style="font-weight:bold; margin-bottom:8px;">邀请留言（可选）</div>
          <el-input
            v-model="inviteMessage"
            type="textarea"
            :rows="3"
            placeholder="给KOL的留言，说明任务详情和合作期望..."
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="inviteDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="sendInvitation"
            :loading="inviting"
            :disabled="!selectedTaskId || !channelCode || channelCode.trim() === ''"
          >
            发送邀请
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'

const router = useRouter()

const kols = ref([])
const search = ref('')
const platformFilter = ref('')
const tagFilter = ref('')
const availableTasks = ref([])
const inviteDialogVisible = ref(false)
const selectedKOL = ref(null)
const selectedTaskId = ref('')
const inviteMessage = ref('')
const channelCode = ref('')
const inviting = ref(false)

const platformOptions = ref([])
const tagOptions = ref([])
const invitationHistory = ref([])

function avatarStyle(color) {
  return {
    background: color || '#409EFF'
  }
}

function cooperationTagType(status) {
  switch (status) {
    case '合作中': return 'info'
    case '可邀请': return 'success'
    case '繁忙': return 'warning'
    default: return ''
  }
}

function formatNumber(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

function getSelectedTaskDescription() {
  if (!selectedTaskId.value) return ''
  const task = availableTasks.value.find(t => t.id === selectedTaskId.value)
  return task ? task.description || '暂无描述' : ''
}

const filteredKOLs = computed(() => {
  return kols.value.filter(kol => {
    const matchName = !search.value || kol.platform_username.includes(search.value)
    const matchPlatform = !platformFilter.value || kol.platform === platformFilter.value
    const matchTag = !tagFilter.value || (kol.tag_name && kol.tag_name.split(',').includes(tagFilter.value))
    return matchName && matchPlatform && matchTag
  })
})

async function fetchKOLs() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('👥 User not logged in, skipping KOL fetch')
    kols.value = []
    return
  }

  try {
    const res = await apiService.getKolProfile()
    kols.value = Array.isArray(res.data) ? res.data : [res.data]
    platformOptions.value = [...new Set(kols.value.map(k => k.platform))]
    tagOptions.value = [...new Set(kols.value.flatMap(k => (k.tag_name ? k.tag_name.split(',') : [])))]
  } catch (e) {
    console.error('获取KOL数据失败', e)
    kols.value = []
  }
}

async function fetchAvailableTasks() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('📋 User not logged in, skipping available tasks fetch')
    availableTasks.value = []
    return
  }

  try {
    const res = await apiService.getPublishedTasks()
    availableTasks.value = res.data
  } catch (e) {
    console.error('获取可用任务失败', e)
    availableTasks.value = []
  }
}

function openInviteDialog(kol) {
  selectedKOL.value = kol
  selectedTaskId.value = ''
  inviteMessage.value = ''
  inviteDialogVisible.value = true
}

function handleInviteDialogClose() {
  inviteDialogVisible.value = false
  selectedKOL.value = null
  selectedTaskId.value = ''
  inviteMessage.value = ''
  channelCode.value = ''
}

async function sendInvitation() {
  if (!selectedKOL.value || !selectedTaskId.value) {
    ElMessage.error('请选择KOL和任务')
    return
  }

  if (!channelCode.value || channelCode.value.trim() === '') {
    ElMessage.error('请输入渠道码')
    return
  }

  inviting.value = true
  try {
    const res = await apiService.inviteKol({
      task_id: selectedTaskId.value,
      kol_id: selectedKOL.value.user_id,
      message: inviteMessage.value,
      channel_code: channelCode.value
    })

    ElMessage.success('邀请已发送成功！KOL将收到通知。')

    // 更新当前KOL的邀请状态
    const kolIndex = kols.value.findIndex(k => k.user_id === selectedKOL.value.user_id)
    if (kolIndex !== -1) {
      kols.value[kolIndex].invitation_status = 'pending'
      kols.value[kolIndex].invitation_count = (kols.value[kolIndex].invitation_count || 0) + 1
    }

    // 关闭对话框
    inviteDialogVisible.value = false

    // 刷新数据
    await Promise.all([
      fetchAvailableTasks(),
      fetchInvitationHistory()
    ])

  } catch (e) {
    console.error('发送邀请失败', e)
    const errorMsg = e.response?.data?.detail || '发送邀请失败'
    ElMessage.error(errorMsg)
  } finally {
    inviting.value = false
  }
}

function getInviteButtonType(kol) {
  // 根据邀请状态确定按钮类型
  if (kol.invitation_status === 'pending') {
    return 'info'
  } else if (kol.invitation_status === 'accepted') {
    return 'success'
  } else if (kol.cooperation_status === '繁忙') {
    return 'warning'
  } else {
    return 'primary'
  }
}

function getInviteButtonText(kol) {
  // 根据邀请状态确定按钮文本
  if (kol.invitation_status === 'pending') {
    return '已发送邀请'
  } else if (kol.invitation_status === 'accepted') {
    return '邀请已接受'
  } else if (kol.cooperation_status === '繁忙') {
    return '当前繁忙'
  } else {
    return '邀请合作'
  }
}

function getInviteButtonDisabled(kol) {
  // 按钮禁用条件：已发送邀请、已接受邀请或KOL繁忙
  return kol.invitation_status === 'pending' ||
         kol.invitation_status === 'accepted' ||
         kol.cooperation_status === '繁忙'
}

// 查看KOL详细资料
function viewKOLDetail(kol) {
  console.log('查看KOL详细资料:', kol)
  router.push(`/kol/${kol.id}/detail`)
}

onMounted(() => {
  fetchKOLs()
  fetchAvailableTasks()
  fetchInvitationHistory()
})

// 在 availableTasks 后添加过滤后的任务列表
const filteredAvailableTasks = computed(() => {
  if (!selectedKOL.value || !availableTasks.value.length) {
    return availableTasks.value
  }

  // 过滤掉已邀请过该KOL的任务
  return availableTasks.value.filter(task => {
    // 检查是否已邀请过该KOL执行此任务
    return !hasInvitedKOLForTask(selectedKOL.value.user_id, task.id)
  })
})

// 获取邀请历史 - 恢复使用 apiService
async function fetchInvitationHistory() {
  console.log('🔍 开始获取邀请历史...')

  const token = localStorage.getItem('token')
  const isLoggedIn = localStorage.getItem('isLoggedIn')

  console.log('🔍 登录状态检查:', { token: !!token, isLoggedIn })

  if (!token || isLoggedIn !== 'true') {
    console.log('📋 用户未登录，跳过邀请历史获取')
    invitationHistory.value = []
    return
  }

  try {
    console.log('📡 发起API请求: getBitInvitations')

    // 恢复使用 apiService
    const res = await apiService.getBitInvitations()
    console.log('📡 API响应:', res)

    if (res.data && Array.isArray(res.data)) {
      invitationHistory.value = res.data
      console.log('✅ 邀请历史记录获取成功:', res.data.length, '条记录')
    } else {
      invitationHistory.value = []
      console.log('⚠️ API返回数据格式异常:', res.data)
    }
  } catch (e) {
    console.error('❌ 获取邀请历史失败:', e)
    invitationHistory.value = []
  }
}

// 检查是否已邀请过某个KOL执行某个任务
function hasInvitedKOLForTask(kolId, taskId) {
  return invitationHistory.value.some(invitation =>
    invitation.kol_id === kolId &&
    invitation.task_id === taskId &&
    (invitation.status === 'pending' || invitation.status === 'accepted') // 排除待处理和已接受的
  )
}
</script>

<style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }
  .form {
    margin-top: 32px;
    .el-input,.el-select {
      margin-right: 10px;
    }
  }
  .cards {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    .card {
      background:#fff;
      padding:12px;
      width:360px;
      box-shadow:0 2px 4px #ccc;
      margin-bottom:18px;
      margin-right: 20px;
      .baseinfo {
        display: flex;
        .avatar {
          width:48px;
          height:48px;
          border-radius:50%;
          display:flex;
          align-items:center;
          justify-content:center;
          font-size:22px;
          font-weight:bold;
          margin-right:16px;
          color: #fff;
        }
        .name {
          font-weight: bold;
          font-size: 16px;
        }
        .platform {
          color: #888;
          padding-top: 5px;
        }
        .system-username {
          margin-top: 8px;
          .label {
            font-size: 12px;
            color: #888;
            margin-right: 5px;
          }
          .value {
            font-size: 12px;
            color: #444;
          }
        }
      }
      .tags {
        margin-top: 12px;
        .el-tag {
          margin-right: 8px;
        }
      }
      .fans {
        display: flex;
        margin-top: 12px;
        justify-content: space-between;
        align-items: center;
        border-top: #efefef 1px solid;
        padding-top: 8px;
        .count {
          color: #888;
          span {
            font-weight: bold;
            font-size: 16px;
            color: #444;
            padding-top: 5px;
          }
        }
        .status {
          text-align: right;
          color: #888;
          .el-tag {
            margin-top: 5px;
          }
        }
      }
      .btns {
        margin-top: 12px;
        border-top: #efefef 1px solid;
        padding-top: 12px;
      }
    }
  }
</style>
