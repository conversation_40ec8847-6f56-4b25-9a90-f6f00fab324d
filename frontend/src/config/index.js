// 全局配置文件
// 根据环境自动选择API基础URL

const config = {
  // 开发环境配置
  development: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'http://localhost:8002',
    WS_BASE_URL: process.env.VUE_APP_WS_URL || 'ws://localhost:8002',
    UPLOAD_URL: process.env.VUE_APP_UPLOAD_URL || 'http://localhost:8002/api/oss/upload'
  },
  
  // 生产环境配置
  production: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'https://console.inflink.io',
    WS_BASE_URL: process.env.VUE_APP_WS_URL || 'wss://console.inflink.io',
    UPLOAD_URL: process.env.VUE_APP_UPLOAD_URL || 'https://console.inflink.io/api/oss/upload'
  },
  
  // 测试环境配置
  test: {
    API_BASE_URL: process.env.VUE_APP_API_URL || 'http://test.inflink.io',
    WS_BASE_URL: process.env.VUE_APP_WS_URL || 'ws://test.inflink.io',
    UPLOAD_URL: process.env.VUE_APP_UPLOAD_URL || 'http://test.inflink.io/api/oss/upload'
  }
}

// 获取当前环境
const getEnv = () => {
  return process.env.NODE_ENV || 'development'
}

// 获取当前环境的配置
const getCurrentConfig = () => {
  const env = getEnv()
  return config[env] || config.development
}

// 导出配置
export default {
  ...getCurrentConfig(),
  ENV: getEnv(),
  
  // 工具方法
  getApiUrl: (path = '') => {
    const baseUrl = getCurrentConfig().API_BASE_URL
    return path ? `${baseUrl}${path.startsWith('/') ? '' : '/'}${path}` : baseUrl
  },
  
  getUploadUrl: () => getCurrentConfig().UPLOAD_URL,
  getWSUrl: () => getCurrentConfig().WS_BASE_URL,
  
  // 打印当前配置（调试用）
  printConfig: () => {
    console.log('🌐 Current Environment:', getEnv())
    console.log('⚙️  Current Config:', getCurrentConfig())
  }
} 