import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const isLoggedIn = ref(false)
  const token = ref('')

  // 计算属性
  const isKOL = computed(() => userInfo.value?.user_type === 'kol')
  const isMerchant = computed(() => userInfo.value?.user_type === 'merchant')
  const isAdmin = computed(() => userInfo.value?.username === 'admin')

  // 动作
  function setUserInfo(user) {
    userInfo.value = user
    isLoggedIn.value = true
    
    // 保存到localStorage
    localStorage.setItem('userInfo', JSON.stringify(user))
    localStorage.setItem('isLoggedIn', 'true')
  }

  function setToken(newToken) {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  function logout() {
    userInfo.value = null
    isLoggedIn.value = false
    token.value = ''
    
    // 清除localStorage
    localStorage.removeItem('userInfo')
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('token')
  }

  function initFromStorage() {
    // 从localStorage恢复状态
    const storedUser = localStorage.getItem('userInfo')
    const storedIsLoggedIn = localStorage.getItem('isLoggedIn')
    const storedToken = localStorage.getItem('token')

    if (storedUser && storedIsLoggedIn === 'true') {
      userInfo.value = JSON.parse(storedUser)
      isLoggedIn.value = true
    }

    if (storedToken) {
      token.value = storedToken
    }
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    token,
    
    // 计算属性
    isKOL,
    isMerchant,
    isAdmin,
    
    // 动作
    setUserInfo,
    setToken,
    logout,
    initFromStorage
  }
})
