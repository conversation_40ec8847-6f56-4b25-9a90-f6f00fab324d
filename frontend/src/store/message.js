import { defineStore } from 'pinia'
import apiService from '@/utils/api'
import { ref, onMounted, computed } from 'vue'

export const useMessageStore = defineStore('message', {
  state: () => ({
    unreadCount: 0,
    messages: [],
    lastFetchTime: 0,  // 添加最后获取时间，用于防抖
    loading: false
  }),
  
  actions: {
    async fetchUnreadCount() {
      // 检查是否已登录
      if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
        console.log('📨 User not logged in, skipping message fetch')
        this.messages = []
        this.unreadCount = 0
        return
      }

      try {
        console.log('📨 Fetching messages...')
        // 获取所有消息
        const res = await apiService.getMessageList()
        if (res.data.success && Array.isArray(res.data.data)) {
          this.messages = res.data.data
          // 计算未读消息数量
          this.unreadCount = this.messages.filter(msg => !msg.is_read).length
          this.lastFetchTime = Date.now()
          console.log('📨 Got messages:', this.messages.length, 'unread:', this.unreadCount)
        } else {
          console.error('Invalid message data format:', res.data)
          this.messages = []
          this.unreadCount = 0
        }
      } catch (error) {
        console.error('Failed to fetch messages:', error)
        this.messages = []
        this.unreadCount = 0
      }
    },

    async markAsRead(messageId) {
      try {
        const res = await apiService.readMessage(messageId)
        if (res.data.success) {
          // 更新本地消息状态
          const msgIndex = this.messages.findIndex(msg => msg.id === messageId)
          if (msgIndex !== -1) {
            this.messages[msgIndex].is_read = true
            this.unreadCount = Math.max(0, this.unreadCount - 1)
          }
        }
      } catch (error) {
        console.error('Failed to mark message as read:', error)
      }
    },

    async markAllAsRead() {
      try {
        const res = await apiService.readAllMessages()
        if (res.data.success) {
          // 更新所有消息状态
          this.messages.forEach(msg => msg.is_read = true)
          this.unreadCount = 0
        }
      } catch (error) {
        console.error('Failed to mark all messages as read:', error)
      }
    },

    async getUnreadCount() {
      // 检查是否已登录
      if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
        console.log('🔔 User not logged in, skipping unread count fetch')
        this.unreadCount = 0
        return 0
      }

      // 防抖：如果距离上次调用不到5秒，则跳过
      const now = Date.now()
      if (now - this.lastFetchTime < 5000) {
        console.log('🔔 Skipping getUnreadCount due to debounce')
        return this.unreadCount
      }

      try {
        console.log('🔔 Fetching unread count...')
        this.lastFetchTime = now
        const res = await apiService.getUnreadMessageCount()
        if (res.data.success) {
          this.unreadCount = res.data.count
          console.log('🔔 Got unread count:', this.unreadCount)
        }
        return this.unreadCount
      } catch (error) {
        console.error('Failed to get unread count:', error)
        return 0
      }
    },

    // 启动定时刷新
    startPolling() {
      // 每30秒检查一次新消息
      setInterval(() => {
        this.getUnreadCount()
      }, 30000)
    }
  }
})