/**
 * 错误处理工具函数
 * 用于统一提取和格式化API错误信息
 */

/**
 * 从API错误响应中提取友好的错误信息
 * @param {Error} error - API错误对象
 * @param {string} defaultMessage - 默认错误信息
 * @returns {string} 友好的错误信息
 */
export function extractErrorMessage(error, defaultMessage = '操作失败，请重试') {
  console.error('API Error:', error)
  console.error('Error response data:', error.response?.data)
  
  // 1. 优先使用后端返回的具体错误信息
  if (error.response?.data?.detail) {
    // FastAPI HTTPException 格式
    return error.response.data.detail
  } else if (error.response?.data?.message) {
    // 自定义业务异常格式
    return error.response.data.message
  }
  
  // 2. 根据HTTP状态码提供友好提示
  const status = error.response?.status
  if (status === 400) {
    return '请求参数有误，请检查输入信息'
  } else if (status === 401) {
    return '用户不存在或密码错误，请检查您的登录信息'
  } else if (status === 403) {
    return '您没有权限执行此操作'
  } else if (status === 404) {
    return '请求的资源不存在'
  } else if (status === 409) {
    return '数据冲突，请检查是否重复提交'
  } else if (status === 422) {
    return '输入数据格式错误，请检查后重试'
  } else if (status >= 500) {
    return '服务器暂时不可用，请稍后重试'
  }
  
  // 3. 使用错误对象的message或默认信息
  return error.message || defaultMessage
}

/**
 * 专门用于登录错误的处理
 * @param {Error} error - 登录错误对象
 * @returns {string} 登录错误信息
 */
export function extractLoginErrorMessage(error) {
  return extractErrorMessage(error, '登录失败，请重试')
}

/**
 * 专门用于注册错误的处理
 * @param {Error} error - 注册错误对象
 * @returns {string} 注册错误信息
 */
export function extractRegisterErrorMessage(error) {
  // 注册时的特殊处理
  if (error.response?.status === 409) {
    // 如果后端有具体信息，使用后端的；否则使用通用提示
    const backendMessage = error.response?.data?.message || error.response?.data?.detail
    return backendMessage || '用户名或邮箱已存在，请选择其他信息'
  }
  
  return extractErrorMessage(error, '注册失败，请重试')
}

export default {
  extractErrorMessage,
  extractLoginErrorMessage,
  extractRegisterErrorMessage
} 