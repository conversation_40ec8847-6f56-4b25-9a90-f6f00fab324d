#!/bin/bash

# KOL Hub 生产环境部署脚本
# 用于将项目部署到生产服务器

set -e

# 配置项
DEPLOY_USER="www-data"
DEPLOY_GROUP="www-data"
DEPLOY_PATH="/opt/kol-hub"
SERVICE_NAME_BACKEND="kol-hub-backend"
SERVICE_NAME_FRONTEND="kol-hub-frontend"
NGINX_SITE_NAME="kol-hub"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "请以root用户运行此脚本"
        exit 1
    fi
}

# 检查系统依赖
check_system_dependencies() {
    log "检查系统依赖..."
    
    # # 检查必要的包管理器
    # if ! command -v apt &> /dev/null && ! command -v yum &> /dev/null; then
    #     error "不支持的操作系统，需要 apt 或 yum 包管理器"
    #     exit 1
    # fi
    
    # # 安装基础依赖
    # if command -v apt &> /dev/null; then
    #     apt update
    #     apt install -y curl wget git python3 python3-pip nodejs npm nginx systemd
    # elif command -v yum &> /dev/null; then
    #     yum update -y
    #     yum install -y curl wget git python3 python3-pip nodejs npm nginx systemd
    # fi
    
    # 安装 uv
    if ! command -v uv &> /dev/null; then
        log "安装 uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source ~/.bashrc
    fi
    
    # 安装 serve (用于前端静态文件服务)
    if ! command -v serve &> /dev/null; then
        log "安装 serve..."
        npm install -g serve
    fi
    
    success "系统依赖检查完成"
}

# 创建部署用户
create_deploy_user() {
    log "创建部署用户..."
    
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d /var/www -m "$DEPLOY_USER"
        success "创建用户: $DEPLOY_USER"
    else
        log "用户 $DEPLOY_USER 已存在"
    fi
}

# 创建项目目录
create_project_directories() {
    log "创建项目目录..."
    
    mkdir -p "$DEPLOY_PATH"/{backend,frontend,logs,pids,scripts}
    chown -R "$DEPLOY_USER:$DEPLOY_GROUP" "$DEPLOY_PATH"
    chmod 755 "$DEPLOY_PATH"
    
    success "项目目录创建完成"
}

# 复制项目文件
copy_project_files() {
    log "复制项目文件..."
    
    local source_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    
    # # 复制后端文件
    # cp -r "$source_dir/backend"/* "$DEPLOY_PATH/backend/"
    
    # # 复制前端文件
    # cp -r "$source_dir/frontend"/* "$DEPLOY_PATH/frontend/"
    
    # # 复制脚本文件
    # cp -r "$source_dir/scripts"/* "$DEPLOY_PATH/scripts/"
    
    # 设置执行权限
    chmod +x "$DEPLOY_PATH/scripts/manage.sh"
    chmod +x "$DEPLOY_PATH/scripts/deploy.sh"
    
    # 设置所有权
    chown -R "$DEPLOY_USER:$DEPLOY_GROUP" "$DEPLOY_PATH"
    
    success "项目文件复制完成"
}

# 安装项目依赖
install_dependencies() {
    log "安装项目依赖..."
    
    # 安装后端依赖
    cd "$DEPLOY_PATH/backend"
    sudo -u "$DEPLOY_USER" uv sync
    
    # 安装前端依赖
    cd "$DEPLOY_PATH/frontend"
    sudo -u "$DEPLOY_USER" npm install
    
    # 构建前端生产版本
    sudo -u "$DEPLOY_USER" NODE_ENV=production npm run build
    
    success "项目依赖安装完成"
}

# 配置systemd服务
configure_systemd_services() {
    log "配置systemd服务..."
    
    # 更新服务文件中的路径
    sed -i "s|/opt/kol-hub|$DEPLOY_PATH|g" "$DEPLOY_PATH/scripts/kol-hub-backend.service"
    sed -i "s|/opt/kol-hub|$DEPLOY_PATH|g" "$DEPLOY_PATH/scripts/kol-hub-frontend.service"
    
    # 复制服务文件
    cp "$DEPLOY_PATH/scripts/kol-hub-backend.service" "/etc/systemd/system/"
    cp "$DEPLOY_PATH/scripts/kol-hub-frontend.service" "/etc/systemd/system/"
    
    # 重载systemd配置
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME_BACKEND"
    systemctl enable "$SERVICE_NAME_FRONTEND"
    
    success "systemd服务配置完成"
}

# 配置nginx
configure_nginx() {
    log "配置nginx..."
    
    cat > "/etc/nginx/sites-available/$NGINX_SITE_NAME" << EOF
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 后端API
    location /api/ {
        proxy_pass http://127.0.0.1:8002/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 处理CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'Authorization, Content-Type';
        
        if (\$request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:8080;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    # 启用站点
    ln -sf "/etc/nginx/sites-available/$NGINX_SITE_NAME" "/etc/nginx/sites-enabled/"
    
    # 测试nginx配置
    nginx -t
    
    # 启用nginx服务
    systemctl enable nginx
    
    success "nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw --force enable
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
    fi
    
    success "防火墙配置完成"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    # 启动后端服务
    systemctl start "$SERVICE_NAME_BACKEND"
    
    # 启动前端服务
    systemctl start "$SERVICE_NAME_FRONTEND"
    
    # 启动nginx
    systemctl start nginx
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME_BACKEND"; then
        success "后端服务启动成功"
    else
        error "后端服务启动失败"
        systemctl status "$SERVICE_NAME_BACKEND"
    fi
    
    if systemctl is-active --quiet "$SERVICE_NAME_FRONTEND"; then
        success "前端服务启动成功"
    else
        error "前端服务启动失败"
        systemctl status "$SERVICE_NAME_FRONTEND"
    fi
    
    if systemctl is-active --quiet nginx; then
        success "nginx服务启动成功"
    else
        error "nginx服务启动失败"
        systemctl status nginx
    fi
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    echo ""
    echo "=========================================="
    echo "KOL Hub 部署信息"
    echo "=========================================="
    echo "部署路径: $DEPLOY_PATH"
    echo "前端服务: http://localhost:8080"
    echo "后端服务: http://localhost:8002"
    echo "nginx代理: http://localhost"
    echo ""
    echo "服务管理命令:"
    echo "  systemctl start/stop/restart $SERVICE_NAME_BACKEND"
    echo "  systemctl start/stop/restart $SERVICE_NAME_FRONTEND"
    echo "  systemctl start/stop/restart nginx"
    echo ""
    echo "日志查看:"
    echo "  journalctl -u $SERVICE_NAME_BACKEND -f"
    echo "  journalctl -u $SERVICE_NAME_FRONTEND -f"
    echo "  journalctl -u nginx -f"
    echo ""
    echo "手动管理脚本:"
    echo "  $DEPLOY_PATH/scripts/manage.sh status"
    echo "  $DEPLOY_PATH/scripts/manage.sh restart all"
    echo "=========================================="
}

# 显示帮助信息
show_help() {
    echo "KOL Hub 生产环境部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --deploy-path PATH    设置部署路径 (默认: /opt/kol-hub)"
    echo "  --user USER           设置部署用户 (默认: www-data)"
    echo "  --help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                          # 使用默认配置部署"
    echo "  $0 --deploy-path /app       # 部署到 /app 目录"
    echo "  $0 --user deploy            # 使用 deploy 用户"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --deploy-path)
                DEPLOY_PATH="$2"
                shift 2
                ;;
            --user)
                DEPLOY_USER="$2"
                DEPLOY_GROUP="$2"
                shift 2
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    log "开始部署 KOL Hub 到生产环境..."
    
    check_root
    parse_arguments "$@"
    check_system_dependencies
    create_deploy_user
    create_project_directories
    copy_project_files
    install_dependencies
    configure_systemd_services
    # configure_nginx
    # configure_firewall
    start_services
    show_deployment_info
    
    success "部署完成！"
}

# 执行主函数
main "$@" 