# KOL Hub Nginx 配置
# 文件位置: /etc/nginx/conf.d/kol-hub.conf

# 上游服务器定义
upstream frontend_server {
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream backend_server {
    server 127.0.0.1:8002 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# 主服务器配置
server {
    listen 80;
    server_name console.inflink.io 13.212.228.26;  # 替换为你的域名

    # 服务器标识隐藏
    server_tokens off;

    # 日志配置
    access_log /var/log/nginx/kol-hub-access.log;
    error_log /var/log/nginx/kol-hub-error.log warn;

    # 客户端配置
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Forwarded-Proto $scheme always;

    # 前端应用代理 (Vue.js)
    location / {
        proxy_pass http://frontend_server;
        proxy_http_version 1.1;

        # WebSocket 支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';

        # 基础代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 缓存控制
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 86400;  # WebSocket 长连接
    }

    # 后端 API 代理 (FastAPI)
    location /api/ {
        proxy_pass http://backend_server/api/;
        proxy_http_version 1.1;

        # 代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # CORS 头（生产环境建议限制 Origin）
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header Access-Control-Allow-Headers 'Authorization, Content-Type, X-Requested-With, Accept, Origin' always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Max-Age 3600;

        # OPTIONS 请求处理
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # OpenAPI 文档代理
    location /openapi/ {
        proxy_pass http://backend_server/openapi/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
        proxy_pass http://frontend_server;

        # 缓存配置
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";

        # 压缩
        gzip_static on;

        # 缓存控制
        proxy_cache_valid 200 1y;
    }

    # 健康检查端点
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 安全配置：拒绝访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 拒绝访问特定文件类型
    location ~* \.(env|log|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}