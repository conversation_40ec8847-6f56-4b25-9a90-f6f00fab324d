# KOL Hub 部署和启停管理指南

本项目在 `scripts/` 目录下提供了完整的脚本来管理前后端服务，支持开发环境和生产环境的部署。

## 快速开始

### 开发环境

```bash
# 启动所有服务（前端+后端）
scripts/manage.sh start all

# 查看服务状态
scripts/manage.sh status

# 停止所有服务
scripts/manage.sh stop all
```

### 生产环境

```bash
# 完整部署到生产服务器（需要root权限）
sudo scripts/deploy.sh

# 生产模式启动服务
NODE_ENV=production scripts/manage.sh start all

# 查看状态
scripts/manage.sh status
```

## 脚本说明

### 项目管理脚本 (`scripts/manage.sh`)

用于开发和生产环境的服务管理，支持前后端分别控制。

**功能特性：**
- 自动安装和管理依赖（uv, npm）
- 支持前后端分别启停
- 实时日志查看和清理
- 进程管理和状态检查
- 优雅停止机制
- 支持生产模式（NODE_ENV=production）

**使用方法：**

```bash
# 启动服务
scripts/manage.sh start all      # 启动所有服务
scripts/manage.sh start backend  # 仅启动后端
scripts/manage.sh start frontend # 仅启动前端

# 停止服务
scripts/manage.sh stop all       # 停止所有服务
scripts/manage.sh stop backend   # 仅停止后端
scripts/manage.sh stop frontend  # 仅停止前端

# 重启服务
scripts/manage.sh restart all    # 重启所有服务
scripts/manage.sh restart backend # 仅重启后端
scripts/manage.sh restart frontend # 仅重启前端

# 状态和日志
scripts/manage.sh status         # 查看服务状态
scripts/manage.sh logs backend 100 # 查看后端最近100行日志
scripts/manage.sh logs frontend  # 查看前端日志
scripts/manage.sh clean-logs     # 清理所有日志

# 生产模式
NODE_ENV=production scripts/manage.sh start all
```

### 生产部署脚本 (`scripts/deploy.sh`)

用于生产环境的完整部署，自动配置服务器环境。

**功能特性：**
- 自动创建部署用户和目录
- 安装系统依赖（uv, Node.js, Nginx）
- 配置 systemd 服务
- 配置 Nginx 反向代理
- 配置防火墙规则
- 完整的生产环境优化

**使用方法：**

```bash
# 完整部署（需要root权限）
sudo scripts/deploy.sh

# 部署后的服务管理通过 systemd
sudo systemctl status kol-hub-backend
sudo systemctl status kol-hub-frontend
sudo systemctl restart kol-hub-backend
```

## 环境配置

### 环境变量

**开发环境：**
```bash
# 无需特殊配置，使用默认端口
# 后端: http://localhost:8002
# 前端: http://localhost:8080
```

**生产环境：**
```bash
# 可选环境变量
export BACKEND_PORT=8002         # 后端服务端口
export FRONTEND_PORT=80          # 前端服务端口（Nginx）
```

### 依赖要求

**开发环境：**
- Python 3.12+
- uv (Python 包管理器)
- Node.js 18+
- npm

**生产环境：**
- 开发环境的所有依赖
- Nginx (用于前端静态文件服务)
- 可选：Systemd (用于服务管理)

## 目录结构

脚本系统会自动创建以下目录结构：

```
kol-hub/
├── scripts/                   # 脚本目录
│   ├── manage.sh             # 项目管理脚本
│   ├── deploy.sh             # 生产部署脚本
│   ├── kol-hub-backend.service  # 后端 systemd 配置
│   └── kol-hub-frontend.service # 前端 systemd 配置
├── pids/                      # 进程 PID 文件
│   ├── backend.pid
│   └── frontend.pid
├── logs/                      # 日志文件
│   ├── backend.log
│   ├── frontend.log
│   ├── backend_error.log
│   └── frontend_error.log
├── backend/
└── frontend/
    └── dist/                  # 前端构建输出目录（生产部署时）
```

## 生产部署步骤

### 1. 环境准备

```bash
# 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装 Node.js 和 npm
# Ubuntu/Debian:
sudo apt update
sudo apt install nodejs npm nginx

# CentOS/RHEL:
sudo yum install nodejs npm nginx
```

### 2. 项目部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd kol-hub

# 2. 给脚本执行权限
chmod +x scripts/*.sh

# 3. 执行完整部署（需要root权限）
sudo scripts/deploy.sh

# 或者手动部署步骤：
# 3a. 开发环境测试
scripts/manage.sh start all
scripts/manage.sh status

# 3b. 生产模式启动
NODE_ENV=production scripts/manage.sh start all

# 3c. 配置 systemd 服务（生产推荐）
sudo cp scripts/*.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable kol-hub-backend kol-hub-frontend
sudo systemctl start kol-hub-backend kol-hub-frontend
```

### 3. 验证部署

```bash
# 检查服务状态
scripts/manage.sh status

# 检查 systemd 服务状态
sudo systemctl status kol-hub-backend
sudo systemctl status kol-hub-frontend

# 测试 API 访问
curl http://localhost:8002/docs

# 测试前端访问
curl http://localhost:8080/  # 开发模式
curl http://localhost/       # 生产模式（Nginx）
```

## 常见问题

### Q: 端口被占用怎么办？

**A:** 脚本会自动检测端口冲突。如果需要更换端口：

```bash
# 开发环境：编辑 scripts/manage.sh 中的端口配置
# 或者通过环境变量配置（如果脚本支持）
# 生产环境：通过 systemd 服务配置文件修改端口
sudo vim /etc/systemd/system/kol-hub-backend.service
sudo systemctl daemon-reload
sudo systemctl restart kol-hub-backend
```

### Q: 如何查看详细日志？

**A:** 使用日志命令查看：

```bash
# 实时查看日志
scripts/manage.sh logs backend
scripts/manage.sh logs frontend

# 查看指定行数的日志
scripts/manage.sh logs backend 100

# 查看历史日志
tail -n 100 logs/backend.log
tail -n 100 logs/frontend.log

# 查看 systemd 服务日志（生产环境）
sudo journalctl -u kol-hub-backend -f
sudo journalctl -u kol-hub-frontend -f
```

### Q: 服务启动失败怎么办？

**A:** 按以下步骤排查：

```bash
# 1. 检查服务状态
scripts/manage.sh status

# 2. 查看日志
scripts/manage.sh logs backend 100
scripts/manage.sh logs frontend 50

# 3. 检查依赖
cd backend && uv sync
cd frontend && npm install

# 4. 检查端口占用
netstat -tulpn | grep :8002
netstat -tulpn | grep :8080

# 5. 重启服务
scripts/manage.sh restart all
```

### Q: 如何升级部署？

**A:** 生产环境升级流程：

```bash
# 1. 停止服务
scripts/manage.sh stop all
# 或者停止 systemd 服务
sudo systemctl stop kol-hub-backend kol-hub-frontend

# 2. 更新代码
git pull

# 3. 重新部署
# 开发环境
scripts/manage.sh start all

# 生产环境
NODE_ENV=production scripts/manage.sh start all
# 或者重启 systemd 服务
sudo systemctl restart kol-hub-backend kol-hub-frontend

# 4. 验证服务
scripts/manage.sh status
sudo systemctl status kol-hub-backend kol-hub-frontend
```

## 高级配置

### 自定义配置文件

可以修改配置文件来满足特定需求：

```bash
# 项目管理脚本配置
vim scripts/manage.sh

# Systemd 配置
vim scripts/kol-hub-backend.service
vim scripts/kol-hub-frontend.service

# 生产部署脚本配置
vim scripts/deploy.sh
```

### 多环境部署

可以通过环境变量和配置文件支持多环境：

```bash
# 开发环境
scripts/manage.sh start all

# 测试环境（生产模式）
NODE_ENV=production scripts/manage.sh start all

# 预生产环境（修改 systemd 配置文件中的端口）
# 编辑 scripts/*.service 文件，修改端口配置
# 然后重新部署
sudo scripts/deploy.sh
```

### 监控和告警

结合系统监控工具：

```bash
# 配置日志轮转
sudo logrotate -d /etc/logrotate.d/kol-hub

# 配置系统监控
# 可以使用 Prometheus、Grafana 等工具监控服务状态
```

## 安全建议

1. **防火墙配置**：确保只开放必要的端口
2. **SSL/TLS**：生产环境建议配置 HTTPS
3. **访问控制**：配置适当的网络访问策略
4. **日志管理**：定期清理和归档日志文件
5. **备份策略**：定期备份数据库和配置文件

## 故障排除

### 常见错误代码

- **端口占用**：错误代码 1，检查端口使用情况
- **依赖缺失**：错误代码 1，重新安装依赖
- **权限不足**：错误代码 1，检查文件权限
- **配置错误**：错误代码 1，检查配置文件语法

### 应急恢复

```bash
# 强制停止所有相关进程
pkill -f "uvicorn api:app"
pkill -f "npm run serve"

# 清理 PID 文件
rm -f pids/*.pid

# 清理日志（可选）
scripts/manage.sh clean-logs

# 重新启动
scripts/manage.sh start all

# 或者重启 systemd 服务
sudo systemctl restart kol-hub-backend kol-hub-frontend
```

## 技术支持

如遇到问题，请提供以下信息：

1. 操作系统版本
2. Python 和 Node.js 版本
3. 错误日志
4. 执行的命令
5. 项目版本信息

```bash
# 收集系统信息
scripts/manage.sh status > debug-info.txt
echo "\n=== Backend Logs ===" >> debug-info.txt
scripts/manage.sh logs backend 100 >> debug-info.txt
echo "\n=== Frontend Logs ===" >> debug-info.txt
scripts/manage.sh logs frontend 100 >> debug-info.txt
echo "\n=== System Info ===" >> debug-info.txt
uname -a >> debug-info.txt
python --version >> debug-info.txt
node --version >> debug-info.txt
``` 