[Unit]
Description=KOL Hub Backend Service
After=network.target
Wants=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/opt/kol-hub/backend
Environment=PATH=/opt/kol-hub/backend/.venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/kol-hub/backend
Environment=NODE_ENV=production
ExecStartPre=/usr/bin/uv sync
ExecStart=/usr/bin/uv run uvicorn api:app --host 0.0.0.0 --port 8002 --workers 4 --daemon
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/kol-hub/pids/backend.pid
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/kol-hub/logs /opt/kol-hub/pids /tmp

[Install]
WantedBy=multi-user.target 