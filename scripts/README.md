## 📚 `manage.sh` 和 `deploy.sh` 的关系和使用场景

这两个脚本有**不同的职责和使用场景**，它们是**互补关系**而不是竞争关系：

## 🛠️ `scripts/manage.sh` - **项目运维管理脚本**

### 功能定位
- **日常运维管理工具** 📈
- **开发和生产环境通用**
- **轻量级服务控制**

### 主要功能
```bash
# 服务控制
scripts/manage.sh start backend    # 启动后端
scripts/manage.sh stop frontend    # 停止前端
scripts/manage.sh restart all      # 重启所有服务

# 状态监控
scripts/manage.sh status           # 查看服务状态
scripts/manage.sh logs backend 100 # 查看日志

# 维护工具
scripts/manage.sh clean-logs       # 清理日志
```

### 使用场景
1. **开发环境**：日常开发时启停服务
2. **测试环境**：测试时的服务管理
3. **生产环境**：部署后的日常运维
4. **故障排查**：查看日志、重启服务
5. **维护操作**：清理日志、检查状态

---

## 🚀 `scripts/deploy.sh` - **生产环境部署脚本**

### 功能定位
- **一次性部署工具** 🎯
- **生产环境专用**
- **完整环境搭建**

### 主要功能
```bash
# 完整部署流程
sudo scripts/deploy.sh
```

### 内部执行步骤
1. **环境检查**：检查root权限、系统依赖
2. **用户管理**：创建专用部署用户(`www-data`)
3. **目录创建**：创建`/opt/kol-hub`等部署目录
4. **文件复制**：复制项目文件到生产目录
5. **依赖安装**：安装后端(uv)和前端(npm)依赖
6. **构建前端**：执行生产构建(`npm run build`)
7. **服务配置**：配置systemd服务
8. **Web服务器**：配置Nginx反向代理
9. **安全配置**：配置防火墙规则
10. **服务启动**：启动所有服务

### 使用场景
1. **首次部署**：在全新服务器上部署项目
2. **环境重建**：重新搭建完整生产环境
3. **大版本升级**：需要重新部署的情况

---

## 🔄 两者关系和配合使用

### 时间线关系
```
首次部署: deploy.sh → 日常运维: manage.sh
   ↓                      ↓
完整环境搭建          →    服务启停管理
```

### 实际使用流程

#### 1️⃣ **首次生产部署**
```bash
# Step 1: 完整部署（仅执行一次）
sudo scripts/deploy.sh

# Step 2: 验证部署
scripts/manage.sh status

# 此时系统会配置为 systemd 服务，以后可以用：
sudo systemctl status kol-hub-backend
sudo systemctl status kol-hub-frontend
```

#### 2️⃣ **日常开发环境**
```bash
# 完全使用 manage.sh
scripts/manage.sh start all
scripts/manage.sh logs backend
scripts/manage.sh restart frontend
```

#### 3️⃣ **生产环境日常运维**
```bash
# 使用 manage.sh 进行日常管理
scripts/manage.sh status
scripts/manage.sh logs backend 100

# 或者使用 systemd（deploy.sh 配置的）
sudo systemctl restart kol-hub-backend
sudo journalctl -u kol-hub-backend -f
```

#### 4️⃣ **代码更新部署**
```bash
# 更新代码
git pull

# 选择方式 A：使用 manage.sh 重启
scripts/manage.sh restart all

# 选择方式 B：使用 systemd 重启
sudo systemctl restart kol-hub-backend kol-hub-frontend

# 选择方式 C：如果有重大变更，重新完整部署
sudo scripts/deploy.sh
```

## 📊 对比表格

| 特性 | `manage.sh` | `deploy.sh` |
|------|-------------|-------------|
| **使用频率** | 高频（日常使用） | 低频（一次性） |
| **权限要求** | 普通用户 | root权限 |
| **执行时间** | 快速（秒级） | 较慢（分钟级） |
| **环境** | 开发+生产 | 仅生产环境 |
| **功能范围** | 服务管理 | 完整部署 |
| **依赖安装** | 检查，不安装系统依赖 | 完整安装所有依赖 |
| **配置文件** | 不修改系统配置 | 创建systemd、nginx配置 |

## 🎯 最佳实践建议

### 开发环境
```bash
# 只需要 manage.sh
scripts/manage.sh start all
scripts/manage.sh logs backend
```

### 生产环境首次部署
```bash
# 1. 使用 deploy.sh 完整部署
sudo scripts/deploy.sh

# 2. 后续运维使用 manage.sh 或 systemd
scripts/manage.sh status
sudo systemctl status kol-hub-backend
```

### 生产环境日常运维
```bash
# 优先使用 systemd（更稳定）
sudo systemctl restart kol-hub-backend

# 或者使用 manage.sh（更灵活）
scripts/manage.sh restart backend
scripts/manage.sh logs backend 50
```

总结：**`deploy.sh`是房子的建造者，`manage.sh`是房子的管家** 🏠👨‍💼