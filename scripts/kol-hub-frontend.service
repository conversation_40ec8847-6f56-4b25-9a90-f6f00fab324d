[Unit]
Description=KOL Hub Frontend Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/kol-hub/frontend
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=NODE_ENV=production
ExecStartPre=/usr/bin/npm run build
ExecStart=/usr/bin/serve -s dist -l 8080
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/kol-hub/logs /opt/kol-hub/pids /tmp

[Install]
WantedBy=multi-user.target 