#!/bin/bash

# KOL Hub 项目启停管理脚本
# 支持前后端独立管理和生产环境部署

set -e

# 配置项
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/pids"

# 创建必要的目录
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

# PID文件路径
BACKEND_PID_FILE="$PIDS_DIR/backend.pid"
FRONTEND_PID_FILE="$PIDS_DIR/frontend.pid"

# 日志文件路径
BACKEND_LOG_FILE="$LOGS_DIR/backend.log"
FRONTEND_LOG_FILE="$LOGS_DIR/frontend.log"
BACKEND_ERROR_LOG="$LOGS_DIR/backend_error.log"
FRONTEND_ERROR_LOG="$LOGS_DIR/frontend_error.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    local component=$1
    
    case $component in
        "backend")
            if ! command -v uv &> /dev/null; then
                error "uv 未安装，请先安装 uv"
                exit 1
            fi
            if [ ! -f "$BACKEND_DIR/pyproject.toml" ]; then
                error "后端项目配置文件不存在：$BACKEND_DIR/pyproject.toml"
                exit 1
            fi
            ;;
        "frontend")
            if ! command -v npm &> /dev/null; then
                error "npm 未安装，请先安装 Node.js 和 npm"
                exit 1
            fi
            if [ ! -f "$FRONTEND_DIR/package.json" ]; then
                error "前端项目配置文件不存在：$FRONTEND_DIR/package.json"
                exit 1
            fi
            ;;
    esac
}

# 检查进程是否运行
is_running() {
    local pid_file=$1
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 获取进程状态
get_status() {
    local component=$1
    local pid_file
    
    case $component in
        "backend")
            pid_file="$BACKEND_PID_FILE"
            ;;
        "frontend")
            pid_file="$FRONTEND_PID_FILE"
            ;;
    esac
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        success "$component 正在运行 (PID: $pid)"
    else
        warning "$component 未运行"
    fi
}

# 启动后端
start_backend() {
    log "启动后端服务..."
    
    if is_running "$BACKEND_PID_FILE"; then
        warning "后端服务已在运行"
        return 0
    fi
    
    check_dependencies "backend"
    
    cd "$BACKEND_DIR"
    
    # 安装依赖（如果需要）
    log "检查后端依赖..."
    uv sync
    
    # 启动服务
    log "启动后端服务器..."
    nohup uv run uvicorn api:app \
        --host 0.0.0.0 \
        --port 8002 \
        --workers 4 \
        --access-log \
        --log-level info \
        > "$BACKEND_LOG_FILE" 2> "$BACKEND_ERROR_LOG" &
    
    local pid=$!
    echo $pid > "$BACKEND_PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    if is_running "$BACKEND_PID_FILE"; then
        success "后端服务启动成功 (PID: $pid)"
        log "访问地址: http://localhost:8002"
        log "日志文件: $BACKEND_LOG_FILE"
        log "错误日志: $BACKEND_ERROR_LOG"
    else
        error "后端服务启动失败"
        cat "$BACKEND_ERROR_LOG"
        exit 1
    fi
}

# 启动前端
start_frontend() {
    log "启动前端服务..."
    
    if is_running "$FRONTEND_PID_FILE"; then
        warning "前端服务已在运行"
        return 0
    fi
    
    check_dependencies "frontend"
    
    cd "$FRONTEND_DIR"
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        log "安装前端依赖..."
        npm install
    fi
    
    # 构建生产版本（生产环境）
    if [ "${NODE_ENV:-development}" = "production" ]; then
        log "构建生产版本..."
        npm run build
        
        # 使用 serve 启动静态文件服务
        if ! command -v serve &> /dev/null; then
            log "安装 serve..."
            npm install -g serve
        fi
        
        nohup serve -s dist -l 8080 \
            > "$FRONTEND_LOG_FILE" 2> "$FRONTEND_ERROR_LOG" &
    else
        # 开发环境
        nohup npm run serve \
            > "$FRONTEND_LOG_FILE" 2> "$FRONTEND_ERROR_LOG" &
    fi
    
    local pid=$!
    echo $pid > "$FRONTEND_PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    if is_running "$FRONTEND_PID_FILE"; then
        success "前端服务启动成功 (PID: $pid)"
        if [ "${NODE_ENV:-development}" = "production" ]; then
            log "访问地址: http://localhost:8080"
        else
            log "访问地址: http://localhost:5173"
        fi
        log "日志文件: $FRONTEND_LOG_FILE"
        log "错误日志: $FRONTEND_ERROR_LOG"
    else
        error "前端服务启动失败"
        cat "$FRONTEND_ERROR_LOG"
        exit 1
    fi
}

# 停止服务
stop_service() {
    local component=$1
    local pid_file
    local service_name
    
    case $component in
        "backend")
            pid_file="$BACKEND_PID_FILE"
            service_name="后端服务"
            ;;
        "frontend")
            pid_file="$FRONTEND_PID_FILE"
            service_name="前端服务"
            ;;
    esac
    
    log "停止$service_name..."
    
    if ! is_running "$pid_file"; then
        warning "$service_name 未运行"
        return 0
    fi
    
    local pid=$(cat "$pid_file")
    
    # 尝试优雅停止
    if kill -TERM "$pid" 2>/dev/null; then
        log "发送停止信号给 $service_name (PID: $pid)"
        
        # 等待进程停止
        for i in {1..10}; do
            if ! kill -0 "$pid" 2>/dev/null; then
                break
            fi
            sleep 1
        done
        
        # 如果还在运行，强制终止
        if kill -0 "$pid" 2>/dev/null; then
            warning "优雅停止失败，强制终止 $service_name"
            kill -KILL "$pid" 2>/dev/null
        fi
    fi
    
    rm -f "$pid_file"
    success "$service_name 已停止"
}

# 重启服务
restart_service() {
    local component=$1
    
    log "重启 $component 服务..."
    stop_service "$component"
    sleep 2
    
    case $component in
        "backend")
            start_backend
            ;;
        "frontend")
            start_frontend
            ;;
    esac
}

# 显示日志
show_logs() {
    local component=$1
    local lines=${2:-50}
    
    case $component in
        "backend")
            if [ -f "$BACKEND_LOG_FILE" ]; then
                log "后端服务日志 (最近 $lines 行):"
                tail -n "$lines" "$BACKEND_LOG_FILE"
            else
                warning "后端日志文件不存在"
            fi
            
            if [ -f "$BACKEND_ERROR_LOG" ] && [ -s "$BACKEND_ERROR_LOG" ]; then
                error "后端错误日志:"
                tail -n "$lines" "$BACKEND_ERROR_LOG"
            fi
            ;;
        "frontend")
            if [ -f "$FRONTEND_LOG_FILE" ]; then
                log "前端服务日志 (最近 $lines 行):"
                tail -n "$lines" "$FRONTEND_LOG_FILE"
            else
                warning "前端日志文件不存在"
            fi
            
            if [ -f "$FRONTEND_ERROR_LOG" ] && [ -s "$FRONTEND_ERROR_LOG" ]; then
                error "前端错误日志:"
                tail -n "$lines" "$FRONTEND_ERROR_LOG"
            fi
            ;;
    esac
}

# 清理日志
clean_logs() {
    log "清理日志文件..."
    
    > "$BACKEND_LOG_FILE"
    > "$FRONTEND_LOG_FILE"
    > "$BACKEND_ERROR_LOG"
    > "$FRONTEND_ERROR_LOG"
    
    success "日志文件已清理"
}

# 显示帮助信息
show_help() {
    echo "KOL Hub 项目管理脚本"
    echo ""
    echo "用法: $0 <command> [component] [options]"
    echo ""
    echo "Commands:"
    echo "  start <component>     启动服务"
    echo "  stop <component>      停止服务"
    echo "  restart <component>   重启服务"
    echo "  status [component]    查看服务状态"
    echo "  logs <component> [n]  查看日志 (n=行数，默认50)"
    echo "  clean-logs            清理所有日志"
    echo "  help                  显示此帮助信息"
    echo ""
    echo "Components:"
    echo "  backend              后端服务"
    echo "  frontend             前端服务"
    echo "  all                  所有服务"
    echo ""
    echo "环境变量:"
    echo "  NODE_ENV=production  生产环境模式"
    echo ""
    echo "示例:"
    echo "  $0 start backend           # 启动后端"
    echo "  $0 start frontend          # 启动前端"
    echo "  $0 start all               # 启动所有服务"
    echo "  $0 stop all                # 停止所有服务"
    echo "  $0 status                  # 查看所有服务状态"
    echo "  $0 logs backend 100        # 查看后端最近100行日志"
    echo "  NODE_ENV=production $0 start frontend  # 生产模式启动前端"
}

# 主函数
main() {
    local command=$1
    local component=$2
    local option=$3
    
    case $command in
        "start")
            case $component in
                "backend")
                    start_backend
                    ;;
                "frontend")
                    start_frontend
                    ;;
                "all")
                    start_backend
                    start_frontend
                    ;;
                *)
                    error "无效的组件: $component"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        "stop")
            case $component in
                "backend"|"frontend")
                    stop_service "$component"
                    ;;
                "all")
                    stop_service "frontend"
                    stop_service "backend"
                    ;;
                *)
                    error "无效的组件: $component"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        "restart")
            case $component in
                "backend"|"frontend")
                    restart_service "$component"
                    ;;
                "all")
                    restart_service "backend"
                    restart_service "frontend"
                    ;;
                *)
                    error "无效的组件: $component"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        "status")
            if [ -z "$component" ] || [ "$component" = "all" ]; then
                get_status "backend"
                get_status "frontend"
            else
                get_status "$component"
            fi
            ;;
        "logs")
            if [ -z "$component" ]; then
                error "请指定组件"
                show_help
                exit 1
            fi
            show_logs "$component" "$option"
            ;;
        "clean-logs")
            clean_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error "无效的命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@" 