# 按日结算统计功能说明

## 功能概述

结算统计功能已从按月生成改为**按日生成**，提供更精细的时间粒度控制。系统会根据 `MarketingTask` 的任务信息和 `KOLMarketingStats` 的统计数据，每日自动生成结算统计记录。

## 核心改动

### 1. 定时任务调整
- **原来**: 每月1号凌晨2点执行，生成上个月统计
- **现在**: **每天凌晨2点执行，生成昨天的统计数据**

### 2. 数据查询逻辑
- **原来**: 按 `stat_month` 字段查询月度数据
- **现在**: 按 `func.date(stat_date)` 查询指定日期数据

### 3. API接口变化

#### 新增的日度接口：
```
POST /api/settlement-stats/generate-daily              # 手动触发日度统计
GET  /api/settlement-stats/summary/{date}              # 获取日度汇总 (YYYY-MM-DD)
GET  /api/settlement-stats/records/date/{date}         # 获取日度记录
```

#### 保留的月度接口：
```
GET  /api/settlement-stats/summary/month/{month}       # 获取月度汇总 (YYYY-MM)
GET  /api/settlement-stats/records/month/{month}       # 获取月度记录
GET  /api/settlement-stats/records/kol/{id}            # 获取KOL的记录
POST /api/settlement-stats/manual-generate             # 手动生成统计
```

## 统计逻辑

### 数据来源
- **任务信息**: `MarketingTask` 表 (`base_reward`, `performance_rate`)
- **统计数据**: `KOLMarketingStats` 表 (按日期汇总: `click_count`, `register_count`, `ftt`, `deposit_amount`)
- **生成记录**: `KolSettlement` 表 (按日生成，包含 `settlement_date` 字段)

### 计算公式
```
基础费用 = 营销单价 × 当日营销次数
效果值 = 当日注册量×1.0 + FTT值×0.1 + 入金金额×0.01
效果佣金 = 效果值 × 效果单价
总费用 = 基础费用 + 效果佣金
```

### 计算示例
假设某KOL某任务某日的数据：
- 营销单价: 10.00，当日营销次数: 5
- 当日注册量: 20，FTT值: 100，入金金额: 5000
- 效果单价: 2.00

计算过程：
- 基础费用 = 10.00 × 5 = 50.00
- 效果值 = 20×1.0 + 100×0.1 + 5000×0.01 = 80
- 效果佣金 = 80 × 2.00 = 160.00
- **总费用 = 50.00 + 160.00 = 210.00**

## 文件结构

### 服务层
- `backend/services/settlement_stats_service.py` - 核心业务逻辑
  - `generate_daily_settlement_stats()` - 生成日度统计
  - `get_settlement_stats_summary()` - 按日期获取汇总
  - `get_settlement_stats_summary_by_month()` - 按月份获取汇总

### API层
- `backend/apis/settlement_stats_apis.py` - REST API接口

### 定时任务
- `backend/services/scheduler_service.py` - 定时任务调度
  - `daily_settlement_stats_job()` - 日度统计任务

### 测试脚本
- `backend/test_settlement_stats.py` - 功能测试脚本

## 使用方式

### 1. 自动执行
系统每天凌晨2点自动生成昨天的结算统计数据。

### 2. 手动触发
```bash
# 手动生成昨天的统计
curl -X POST "http://localhost:8000/api/settlement-stats/generate-daily"

# 手动生成指定日期的统计
curl -X POST "http://localhost:8000/api/settlement-stats/generate-daily?target_date=2024-01-15"
```

### 3. 查询统计
```bash
# 获取某日的汇总
curl "http://localhost:8000/api/settlement-stats/summary/2024-01-15"

# 获取某月的汇总
curl "http://localhost:8000/api/settlement-stats/summary/month/2024-01"

# 获取某日的记录列表
curl "http://localhost:8000/api/settlement-stats/records/date/2024-01-15"
```

### 4. 手动生成特定记录
```bash
curl -X POST "http://localhost:8000/api/settlement-stats/manual-generate" \
  -H "Content-Type: application/json" \
  -d '{"kol_id": 1, "task_id": 1, "settlement_date": "2024-01-15"}'
```

## 优势

1. **精细化管理**: 按日统计提供更精确的数据追踪
2. **及时性**: 每日生成，数据更加及时
3. **灵活查询**: 支持按日期和按月份两种维度查询
4. **数据完整性**: 避免月末大批量处理可能造成的性能问题

## 注意事项

1. **数据依赖**: 需要 `KOLMarketingStats` 表中有对应日期的统计数据
2. **重复生成**: 系统会检查是否已存在记录，避免重复生成
3. **错误处理**: 单个任务或KOL的统计失败不会影响其他记录的生成
4. **性能考虑**: 大量数据时建议在低峰期执行手动生成操作

## 测试

运行测试脚本查看功能：
```bash
# 查看使用说明
python test_settlement_stats.py help

# 运行功能测试
python test_settlement_stats.py

# 测试手动生成
python test_settlement_stats.py manual
``` 