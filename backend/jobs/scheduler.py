"""
定时任务调度器管理

负责启动、停止和管理所有定时任务
"""

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from utils.logger import logger

# 导入所有任务配置
from .daily_settlement import DAILY_SETTLEMENT_JOB_CONFIG
from .test_job import TEST_JOBS_CONFIG
from .twitter_metrics_sync import TWITTER_METRICS_SYNC_JOB_CONFIG

# 全局调度器实例
_scheduler = None


def get_all_job_configs():
    """
    获取所有任务配置

    Returns:
        List[Dict]: 任务配置列表
    """
    job_configs = []

    # 添加日度结算统计任务
    job_configs.append(DAILY_SETTLEMENT_JOB_CONFIG)

    # 添加测试任务
    job_configs.extend(TEST_JOBS_CONFIG)

    # 添加 Twitter 指标同步任务
    job_configs.append(TWITTER_METRICS_SYNC_JOB_CONFIG)

    return job_configs


def add_job_from_config(scheduler, job_config):
    """
    根据配置添加任务到调度器
    
    Args:
        scheduler: APScheduler调度器实例
        job_config: 任务配置字典
    """
    func = job_config['func']
    trigger_type = job_config['trigger']
    job_id = job_config['id']
    max_instances = job_config.get('max_instances', 1)
    description = job_config.get('description', '')
    
    # 创建触发器
    if trigger_type == 'cron':
        # 提取cron参数
        cron_params = {k: v for k, v in job_config.items() 
                      if k in ['second', 'minute', 'hour', 'day', 'month', 'day_of_week', 'year']}
        trigger = CronTrigger(**cron_params)
    elif trigger_type == 'interval':
        # 提取interval参数
        interval_params = {k: v for k, v in job_config.items() 
                         if k in ['seconds', 'minutes', 'hours', 'days', 'weeks']}
        trigger = IntervalTrigger(**interval_params)
    else:
        logger.warning(f"未知的触发器类型: {trigger_type}")
        return
    
    # 添加任务
    scheduler.add_job(
        func=func,
        trigger=trigger,
        id=job_id,
        max_instances=max_instances
    )
    
    logger.info(f"已添加定时任务: {job_id} - {description}")


def start_scheduler():
    """
    启动定时任务调度器
    
    Returns:
        BackgroundScheduler: 调度器实例
    """
    global _scheduler
    
    if _scheduler and _scheduler.running:
        logger.warning("调度器已在运行中")
        return _scheduler
    
    # 创建调度器
    _scheduler = BackgroundScheduler()
    
    # 获取所有任务配置并添加到调度器
    job_configs = get_all_job_configs()
    
    for job_config in job_configs:
        try:
            add_job_from_config(_scheduler, job_config)
        except Exception as e:
            logger.error(f"添加任务失败 {job_config.get('id', 'unknown')}: {e}")
    
    # 启动调度器
    _scheduler.start()
    
    logger.info("APScheduler 已启动，包含以下定时任务:")
    for job_config in job_configs:
        logger.info(f"- {job_config.get('description', job_config.get('id', 'unknown'))}")
    
    return _scheduler


def stop_scheduler():
    """
    停止定时任务调度器
    """
    global _scheduler
    
    if _scheduler and _scheduler.running:
        _scheduler.shutdown()
        logger.info("APScheduler 已停止")
    else:
        logger.warning("调度器未在运行")


def get_scheduler():
    """
    获取调度器实例
    
    Returns:
        BackgroundScheduler: 调度器实例，如果未启动则返回None
    """
    return _scheduler


def list_jobs():
    """
    列出所有已注册的任务
    
    Returns:
        List[str]: 任务ID列表
    """
    if not _scheduler:
        return []
    
    return [job.id for job in _scheduler.get_jobs()]


def add_custom_job(func, trigger_config, job_id, max_instances=1, description=""):
    """
    动态添加自定义任务
    
    Args:
        func: 任务函数
        trigger_config: 触发器配置
        job_id: 任务ID
        max_instances: 最大实例数
        description: 任务描述
    """
    if not _scheduler:
        logger.error("调度器未启动，无法添加任务")
        return False
    
    try:
        job_config = {
            'func': func,
            'id': job_id,
            'max_instances': max_instances,
            'description': description,
            **trigger_config
        }
        
        add_job_from_config(_scheduler, job_config)
        return True
        
    except Exception as e:
        logger.error(f"动态添加任务失败 {job_id}: {e}")
        return False


def remove_job(job_id):
    """
    移除指定任务
    
    Args:
        job_id: 任务ID
        
    Returns:
        bool: 是否成功移除
    """
    if not _scheduler:
        logger.error("调度器未启动")
        return False
    
    try:
        _scheduler.remove_job(job_id)
        logger.info(f"已移除任务: {job_id}")
        return True
    except Exception as e:
        logger.error(f"移除任务失败 {job_id}: {e}")
        return False 