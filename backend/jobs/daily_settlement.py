"""
日度结算统计定时任务

负责每日自动生成结算统计数据的定时任务
"""

from utils.logger import logger


def daily_settlement_stats_job():
    """
    日度结算统计定时任务
    每天凌晨2点执行，生成昨天的结算统计数据
    """
    logger.info("开始执行日度结算统计定时任务")
    
    try:
        # 导入必要的模块（避免循环导入）
        from sqlalchemy.orm import sessionmaker
        from models.db import engine
        from services.settlement_stats_service import SettlementStatsService
        
        # 创建数据库会话
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 创建结算统计服务实例
            stats_service = SettlementStatsService(db)
            
            # 生成日度结算统计数据（默认生成昨天）
            result = stats_service.generate_daily_settlement_stats()
            
            logger.info(f"日度结算统计任务完成: {result}")
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"日度结算统计定时任务执行失败: {e}")


# 任务配置信息
DAILY_SETTLEMENT_JOB_CONFIG = {
    'func': daily_settlement_stats_job,
    'trigger': 'cron',
    'hour': 2,
    'minute': 0,
    'id': 'daily_settlement_stats',
    'max_instances': 1,
    'description': '日度结算统计任务: 每天凌晨2点执行'
} 