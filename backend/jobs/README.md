# 定时任务模块 (Jobs)

本目录包含所有定时任务的实现和调度管理功能。

## 目录结构

```
backend/jobs/
├── __init__.py               # 模块初始化文件
├── scheduler.py              # 调度器管理核心
├── daily_settlement.py       # 日度结算统计任务
├── test_job.py              # 测试任务
└── README.md                # 本说明文档
```

## 核心组件

### 1. scheduler.py - 调度器管理
负责启动、停止和管理所有定时任务的核心模块。

**主要功能：**
- `start_scheduler()` - 启动调度器
- `stop_scheduler()` - 停止调度器
- `add_custom_job()` - 动态添加任务
- `remove_job()` - 移除任务
- `list_jobs()` - 列出所有任务

### 2. daily_settlement.py - 日度结算统计任务
每日自动生成结算统计数据的定时任务。

**执行时间：** 每天凌晨2点  
**功能：** 生成昨天的结算统计数据

### 3. test_job.py - 测试任务
用于测试调度器功能的示例任务。

**包含任务：**
- `test_job_a` - 每分钟执行
- `test_job_b` - 每5分钟执行

## 使用方法

### 启动调度器

```python
from jobs import start_scheduler

# 启动所有定时任务
scheduler = start_scheduler()
```

### 停止调度器

```python
from jobs import stop_scheduler

# 停止所有定时任务
stop_scheduler()
```

### 查看运行中的任务

```python
from jobs.scheduler import list_jobs

# 获取所有任务ID
job_ids = list_jobs()
print(f"运行中的任务: {job_ids}")
```

## 添加新的定时任务

### 1. 创建任务文件
在 `backend/jobs/` 目录下创建新的任务文件，例如 `my_new_job.py`：

```python
"""
我的新定时任务
"""

from utils.logger import logger


def my_custom_job():
    """
    自定义任务函数
    """
    logger.info("执行我的自定义任务")
    # 任务逻辑...


# 任务配置
MY_JOB_CONFIG = {
    'func': my_custom_job,
    'trigger': 'cron',
    'hour': 10,
    'minute': 30,
    'id': 'my_custom_job',
    'max_instances': 1,
    'description': '我的自定义任务: 每天10:30执行'
}
```

### 2. 在调度器中注册任务
修改 `scheduler.py` 文件的 `get_all_job_configs()` 函数：

```python
# 导入新任务配置
from .my_new_job import MY_JOB_CONFIG

def get_all_job_configs():
    job_configs = []
    
    # 添加日度结算统计任务
    job_configs.append(DAILY_SETTLEMENT_JOB_CONFIG)
    
    # 添加测试任务
    job_configs.extend(TEST_JOBS_CONFIG)
    
    # 添加新任务
    job_configs.append(MY_JOB_CONFIG)
    
    return job_configs
```

### 3. 动态添加任务
也可以在运行时动态添加任务：

```python
from jobs.scheduler import add_custom_job

def my_dynamic_job():
    print("动态添加的任务")

# 动态添加任务
trigger_config = {
    'trigger': 'cron',
    'minute': '*/10'  # 每10分钟执行
}

add_custom_job(
    func=my_dynamic_job,
    trigger_config=trigger_config,
    job_id='dynamic_job',
    description='动态添加的任务'
)
```

## 任务配置格式

### Cron 触发器
```python
JOB_CONFIG = {
    'func': job_function,
    'trigger': 'cron',
    'minute': '0',        # 分钟 (0-59)
    'hour': '2',          # 小时 (0-23)
    'day': '*',           # 日 (1-31)
    'month': '*',         # 月 (1-12)
    'day_of_week': '*',   # 周几 (0-6, 0=周一)
    'id': 'job_id',
    'max_instances': 1,
    'description': '任务描述'
}
```

### Interval 触发器
```python
JOB_CONFIG = {
    'func': job_function,
    'trigger': 'interval',
    'seconds': 30,        # 每30秒执行
    # 或者
    'minutes': 5,         # 每5分钟执行
    # 或者
    'hours': 1,           # 每1小时执行
    'id': 'job_id',
    'max_instances': 1,
    'description': '任务描述'
}
```

## 常用Cron表达式示例

| 描述 | 配置 |
|------|------|
| 每分钟执行 | `minute='*'` |
| 每5分钟执行 | `minute='*/5'` |
| 每小时执行 | `minute='0'` |
| 每天凌晨2点执行 | `hour='2', minute='0'` |
| 每周一上午9点执行 | `day_of_week='0', hour='9', minute='0'` |
| 每月1号凌晨执行 | `day='1', hour='0', minute='0'` |

## 最佳实践

1. **任务函数设计**
   - 保持任务函数简单、专一
   - 添加充分的日志记录
   - 实现适当的异常处理

2. **避免长时间运行**
   - 避免在任务中执行耗时过长的操作
   - 考虑将大任务分解为多个小任务

3. **数据库连接管理**
   - 在任务内部创建和关闭数据库连接
   - 避免全局连接对象

4. **任务ID命名**
   - 使用有意义的任务ID
   - 遵循命名规范 (如：功能_频率)

5. **错误处理**
   - 实现适当的错误恢复机制
   - 记录详细的错误信息

## 监控和调试

### 查看调度器状态
```python
from jobs.scheduler import get_scheduler

scheduler = get_scheduler()
if scheduler and scheduler.running:
    print("调度器正在运行")
    
    # 查看所有任务
    jobs = scheduler.get_jobs()
    for job in jobs:
        print(f"任务: {job.id}, 下次执行: {job.next_run_time}")
```

### 手动执行任务
```python
# 获取任务并手动执行
job = scheduler.get_job('daily_settlement_stats')
if job:
    job.func()  # 手动执行任务函数
```

## 注意事项

1. **重复启动防护**：调度器会检查是否已在运行，避免重复启动
2. **任务实例限制**：通过 `max_instances` 控制同时运行的任务实例数
3. **异常隔离**：单个任务失败不会影响其他任务的执行
4. **资源管理**：确保任务中正确释放资源（数据库连接等） 