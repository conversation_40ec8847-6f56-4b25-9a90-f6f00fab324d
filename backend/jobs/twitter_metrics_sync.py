"""
Twitter 帖子指标同步定时任务

负责定期获取 Twitter 帖子的公共指标数据并更新到数据库
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy import Table, select, update, and_
from sqlalchemy.engine import Result
from models import metadata, engine
from services.twitter_service import get_twitter_service
from utils.logger import logger

# 数据库表
marketing_task = Table('marketing_task', metadata, autoload_with=engine)
kol_marketing_stats = Table('kol_marketing_stats', metadata, autoload_with=engine)


async def sync_twitter_metrics_job():
    """
    Twitter 帖子指标同步任务
    
    触发条件：
    1. 发布链接后触发一次更新
    2. 之后每12个小时更新一次
    3. 只同步 completed 状态且在生效日期内的任务
    4. 如果任务已过结束时间则不更新
    """
    logger.info("开始执行 Twitter 帖子指标同步任务")
    
    try:
        with engine.connect() as conn:
            # 获取需要同步指标的任务
            tasks_to_sync = await get_tasks_with_twitter_posts(conn)
            
            if not tasks_to_sync:
                logger.info("没有需要同步的 Twitter 帖子")
                return
            
            # 获取 Twitter 服务实例
            twitter_service = get_twitter_service()
            
            sync_count = 0
            error_count = 0
            
            for task in tasks_to_sync:
                try:
                    await sync_task_twitter_metrics(conn, task, twitter_service)
                    sync_count += 1
                except Exception as e:
                    logger.error(f"同步任务 {task['id']} Twitter 指标失败: {str(e)}")
                    error_count += 1
            
            logger.info(f"Twitter 指标同步完成: 成功 {sync_count}, 失败 {error_count}")
            
    except Exception as e:
        logger.error(f"Twitter 指标同步任务执行失败: {str(e)}")


async def get_tasks_with_twitter_posts(conn) -> List[Dict[str, Any]]:
    """获取包含 Twitter 帖子链接的已完成任务"""
    
    # 查询条件：
    # 1. 任务状态为 running（运行中）
    # 2. 有 published_links 数据
    # 3. 在生效日期内（start_date <= 今天 <= end_date）
    
    today = datetime.now().date()
    
    stmt = select(marketing_task).where(
        and_(
            marketing_task.c.task_status == 'running',
            marketing_task.c.published_links.isnot(None),
            marketing_task.c.published_links != '',
            marketing_task.c.start_date <= today,
            marketing_task.c.end_date >= today
        )
    )
    
    result: Result = conn.execute(stmt)
    tasks = []
    
    for row in result:
        task_dict = dict(row._mapping)
        
        # 解析 published_links 数据（数组格式）
        try:
            published_data_list = json.loads(task_dict['published_links'])
            
            # 确保是数组格式
            if isinstance(published_data_list, list):
                has_twitter_links = False
                for published_data in published_data_list:
                    # 检查是否是 Twitter 平台且有链接
                    if (published_data.get('platform') == 'twitter' and 
                        published_data.get('links') and 
                        len(published_data['links']) > 0):
                        
                        # 检查是否需要更新
                        if should_update_metrics(published_data):
                            has_twitter_links = True
                            break
                
                if has_twitter_links:
                    tasks.append(task_dict)
                    
        except (json.JSONDecodeError, KeyError, TypeError):
            continue
    
    logger.info(f"找到 {len(tasks)} 个需要同步 Twitter 指标的任务")
    return tasks


def should_update_metrics(published_data: Dict[str, Any]) -> bool:
    """判断是否需要更新指标"""
    
    last_metrics_update = published_data.get('last_metrics_update')
    
    # 如果从未更新过，需要更新
    if not last_metrics_update:
        return True
    
    try:
        last_update_time = datetime.fromisoformat(last_metrics_update.replace('Z', '+00:00'))
        
        # 如果距离上次更新超过12小时，需要更新
        if datetime.now() - last_update_time >= timedelta(hours=12):
            return True
            
    except (ValueError, AttributeError):
        # 如果时间格式有问题，需要更新
        return True
    
    return False


async def sync_task_twitter_metrics(conn, task: Dict[str, Any], twitter_service):
    """同步单个任务的 Twitter 指标"""
    
    try:
        # 解析 published_links 数据（数组格式）
        published_data_list = json.loads(task['published_links'])
        
        all_twitter_links = []
        updated_submissions = []
        
        # 遍历所有提交记录，收集 Twitter 链接
        for i, published_data in enumerate(published_data_list):
            if published_data.get('platform') == 'twitter':
                twitter_links = published_data.get('links', [])
                all_twitter_links.extend(twitter_links)
                
                # 保存提交记录的索引，用于后续更新
                updated_submissions.append({
                    'index': i,
                    'data': published_data.copy()
                })
        
        if not all_twitter_links:
            logger.info(f"任务 {task['id']} 没有 Twitter 链接")
            return
        
        updated_posts = []
        
        for link in all_twitter_links:
            # 提取推文 ID
            tweet_id = twitter_service.extract_tweet_id_from_url(link)
            if not tweet_id:
                logger.warning(f"无法从 URL 提取推文 ID: {link}")
                continue

            # 获取推文指标，传递原始 URL
            metrics_data = await twitter_service.get_tweet_metrics(tweet_id, link)

            if metrics_data:
                updated_posts.append(metrics_data)
                logger.info(f"成功获取推文指标: {metrics_data}")
            else:
                logger.warning(f"获取推文指标失败: {tweet_id}")
        
        if updated_posts:
            # 构建 platform_metrics 数据
            platform_metrics = {
                "posts": updated_posts
            }
            
            # 更新所有 Twitter 提交记录的最后更新时间
            current_time = datetime.now().isoformat()
            for submission in updated_submissions:
                published_data_list[submission['index']]['last_metrics_update'] = current_time
            
            # 更新数据库
            await update_task_metrics(conn, task, platform_metrics, published_data_list)
        
    except Exception as e:
        logger.error(f"同步任务 {task['id']} Twitter 指标失败: {str(e)}")
        raise


async def update_task_metrics(conn, task: Dict[str, Any], platform_metrics: Dict[str, Any], 
                            published_data_list: List[Dict[str, Any]]):
    """更新任务的指标数据"""
    
    # 更新 marketing_task 表的 published_links 字段（添加最后更新时间）
    stmt = update(marketing_task).where(
        marketing_task.c.id == task['id']
    ).values(
        published_links=json.dumps(published_data_list, ensure_ascii=False),
        update_time=datetime.now()
    )
    conn.execute(stmt)
    
    # 更新或创建 kol_marketing_stats 记录
    await update_platform_metrics(conn, task, platform_metrics)
    
    conn.commit()
    logger.info(f"已更新任务 {task['id']} 的 Twitter 指标数据")


async def update_platform_metrics(conn, task: Dict[str, Any], platform_metrics: Dict[str, Any]):
    """更新 published_links_metrics 字段"""
    
    # 直接更新 marketing_task 表的 published_links_metrics 字段
    update_stmt = update(marketing_task).where(
        marketing_task.c.id == task['id']
    ).values(
        published_links_metrics=json.dumps(platform_metrics, ensure_ascii=False),
        update_time=datetime.now()
    )
    conn.execute(update_stmt)
    
    logger.info(f"已更新任务 {task['id']} 的 published_links_metrics")


# 任务配置
TWITTER_METRICS_SYNC_JOB_CONFIG = {
    'func': sync_twitter_metrics_job,
    'trigger': 'cron',
    'hour': '*/1',  # 每1小时执行一次，联调阶段间隔时间短一点，后期稳定了再调长
    'id': 'twitter_metrics_sync',
    'max_instances': 1,
    'description': 'Twitter 帖子指标同步任务: 每1小时执行'
}
