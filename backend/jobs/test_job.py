"""
测试定时任务

用于测试调度器功能的简单任务
"""

from utils.logger import logger


def test_job_a():
    """
    测试任务A
    每分钟执行一次，用于测试调度器是否正常工作
    """
    logger.info("定时任务执行 A ！")


def test_job_b():
    """
    测试任务B
    每5分钟执行一次，用于测试多任务调度
    """
    logger.info("定时任务执行 B ！")


# 任务配置信息
TEST_JOBS_CONFIG = [
    {
        'func': test_job_a,
        'trigger': 'cron',
        'minute': '*',
        'id': 'test_job_a',
        'max_instances': 1,
        'description': '测试任务A: 每分钟执行'
    },
    {
        'func': test_job_b,
        'trigger': 'cron',
        'minute': '*/5',
        'id': 'test_job_b',
        'max_instances': 1,
        'description': '测试任务B: 每5分钟执行'
    }
] 