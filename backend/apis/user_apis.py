from fastapi import API<PERSON><PERSON><PERSON>, Header, HTTPException, Depends, Form, UploadFile, Request, File
from services.api_key_service import (
    create_api_key, ApiKeyCreateRequest, check_api_key,
    ApiKeyUpdateRequest, ApiKeyResponse, get_api_keys, update_api_key
)
from services.user_service import (
    UserService, RegisterRequest, UserResponse,
    LoginResponse, LoginRequest, UpdateProfileRequest,
    UserVerifyRequest
)
from dependencies import get_user_service
from typing import Literal, Optional
from pydantic import BaseModel
from utils.jwt import get_current_user
from utils.logger import logger
from models.user import UserInfo

router = APIRouter(prefix="/user", tags=["user"])

@router.post("/register", response_model=LoginResponse)
async def register(
    req: RegisterRequest,
    user_service: UserService = Depends(get_user_service)
):
    """用户注册并自动登录"""
    logger.info(f"User registration attempt: {req.username}")
    return user_service.register_user(req)

@router.post("/login", response_model=LoginResponse)
async def login(
    req: LoginRequest,
    user_service: UserService = Depends(get_user_service)
):
    """用户登录"""
    logger.info(f"User login attempt: {req.username}")
    return user_service.login_user(req)

@router.put("/profile", response_model=UserResponse)
async def update_profile(
    req: UpdateProfileRequest,
    current_user: UserInfo = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """更新用户资料"""
    # 🔧 安全修复：确保用户只能更新自己的资料
    if req.id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限修改此用户资料")

    logger.info(f"Profile update request for user: {req.id}")
    return user_service.update_user_profile(req)

@router.get("/profile/{user_id}", response_model=UserResponse)
async def get_user_profile(
    user_id: int,
    current_user: UserInfo = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """获取用户资料"""
    # 🔧 安全修复：只能查看自己的用户资料
    if user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此用户资料")

    user = user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user

@router.post("/api_key/create")
def api_key_create(req: ApiKeyCreateRequest):
    return create_api_key(req)

@router.post("/api_key/update", response_model=ApiKeyResponse)
def api_key_update(req: ApiKeyUpdateRequest):
    return update_api_key(req)

@router.get("/api_key/list", response_model=list[ApiKeyResponse])
def api_key_list(user_id: int):
    return get_api_keys(user_id)

# 通用依赖：校验API Key
def get_api_key(authorization: str = Header(...)):
    if not authorization.startswith('Bearer '):
        raise HTTPException(401, "无效的认证方式")
    api_key = authorization.split(' ')[1]
    return check_api_key(api_key)

# 示例受保护接口
@router.get("/protected")
def protected_api(current_key=Depends(get_api_key)):
    return {"msg": "API Key校验通过", "api_key": current_key.api_key}

class ReviewRequest(BaseModel):
    user_id: int
    status: Literal['approved', 'rejected']
    remark: str = None

@router.post("/verify/review")
async def verify_review(
    req: ReviewRequest,
    current_user: UserInfo = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """审核用户认证（管理员功能）"""
    # TODO: Add admin permission check here
    logger.info(f"Admin verification review: {req.user_id} -> {req.status}")
    return user_service.verify_user(req.user_id, req.status, req.remark)

@router.post("/verify")
async def user_verify(
    real_name: str = Form(...),
    id_type: str = Form(...),
    id_number: str = Form(...),
    id_validity: str = Form(...),
    is_long_term: bool = Form(...),
    id_card_front: UploadFile = File(...),
    id_card_back: UploadFile = File(...),
    current_user: UserInfo = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """提交用户认证"""
    user_id = current_user.id
    logger.info(f"User verification submission: {user_id}")
    return user_service.submit_verification(
        user_id, real_name, id_type, id_number,
        id_validity, is_long_term, id_card_front, id_card_back
    )

@router.get("/verify/status")
async def verify_status(
    current_user: UserInfo = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """获取当前用户认证状态"""
    user_id = current_user.id
    return user_service.get_verification_status(user_id)