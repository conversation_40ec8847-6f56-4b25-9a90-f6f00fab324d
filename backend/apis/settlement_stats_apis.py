from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from dependencies import get_database_session
from services.settlement_stats_service import SettlementStatsService
from models.settlement import KolSettlementSchema
from utils.logger import logger

router = APIRouter(prefix="/settlement-stats", tags=["settlement_stats"])

# Pydantic 模型
class SettlementStatsResponse(BaseModel):
    target_date: str
    processed_tasks: int
    generated_records: int
    message: str

class SettlementStatsSummaryResponse(BaseModel):
    settlement_date: str
    total_count: int
    total_amount: float
    kol_count: int
    task_count: int

class SettlementMonthSummaryResponse(BaseModel):
    settlement_month: str
    total_count: int
    total_amount: float
    kol_count: int
    task_count: int

class ManualStatsRequest(BaseModel):
    kol_id: int
    task_id: int
    settlement_date: str

@router.post("/generate-daily", response_model=SettlementStatsResponse)
def generate_daily_settlement_stats(
    target_date: Optional[str] = Query(None, description="统计日期，格式YYYY-MM-DD，不传则统计昨天"),
    db: Session = Depends(get_database_session)
):
    """
    手动触发日度结算统计数据生成
    """
    try:
        stats_service = SettlementStatsService(db)
        result = stats_service.generate_daily_settlement_stats(target_date)
        
        logger.info(f"手动触发日度结算统计完成: {result}")
        return result
        
    except Exception as e:
        logger.error(f"手动日度结算统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"统计失败: {str(e)}")

@router.get("/summary/{settlement_date}", response_model=SettlementStatsSummaryResponse)
def get_settlement_stats_summary(
    settlement_date: str,
    db: Session = Depends(get_database_session)
):
    """
    获取指定日期的结算统计汇总
    """
    try:
        stats_service = SettlementStatsService(db)
        summary = stats_service.get_settlement_stats_summary(settlement_date)
        
        return summary
        
    except Exception as e:
        logger.error(f"获取结算统计汇总失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取汇总失败: {str(e)}")

@router.get("/summary/month/{settlement_month}", response_model=SettlementMonthSummaryResponse)
def get_settlement_stats_month_summary(
    settlement_month: str,
    db: Session = Depends(get_database_session)
):
    """
    获取指定月份的结算统计汇总
    """
    try:
        stats_service = SettlementStatsService(db)
        summary = stats_service.get_settlement_stats_summary_by_month(settlement_month)
        
        return summary
        
    except Exception as e:
        logger.error(f"获取月度结算统计汇总失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取汇总失败: {str(e)}")

@router.get("/records/date/{settlement_date}", response_model=List[KolSettlementSchema])
def get_settlement_records_by_date(
    settlement_date: str,
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制返回数量"),
    db: Session = Depends(get_database_session)
):
    """
    获取指定日期的结算统计记录
    """
    try:
        from models.settlement import KolSettlement
        from sqlalchemy import func
        
        records = db.query(KolSettlement).filter(
            func.date(KolSettlement.settlement_date) == settlement_date
        ).offset(skip).limit(limit).all()
        
        return records
        
    except Exception as e:
        logger.error(f"获取结算统计记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.get("/records/month/{settlement_month}", response_model=List[KolSettlementSchema])
def get_settlement_records_by_month(
    settlement_month: str,
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制返回数量"),
    db: Session = Depends(get_database_session)
):
    """
    获取指定月份的结算统计记录
    """
    try:
        from models.settlement import KolSettlement
        
        records = db.query(KolSettlement).filter(
            KolSettlement.settlement_month == settlement_month
        ).offset(skip).limit(limit).all()
        
        return records
        
    except Exception as e:
        logger.error(f"获取结算统计记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.get("/records/kol/{kol_id}", response_model=List[KolSettlementSchema])
def get_settlement_records_by_kol(
    kol_id: int,
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制返回数量"),
    db: Session = Depends(get_database_session)
):
    """
    获取指定KOL的结算统计记录
    """
    try:
        from models.settlement import KolSettlement
        
        records = db.query(KolSettlement).filter(
            KolSettlement.kol_id == kol_id
        ).order_by(KolSettlement.settlement_month.desc()).offset(skip).limit(limit).all()
        
        return records
        
    except Exception as e:
        logger.error(f"获取KOL结算统计记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.post("/manual-generate")
def manual_generate_settlement_stats(
    request: ManualStatsRequest,
    db: Session = Depends(get_database_session)
):
    """
    手动生成指定KOL和任务的结算统计
    """
    try:
        stats_service = SettlementStatsService(db)
        
        success = stats_service.manual_generate_stats(
            request.kol_id, request.task_id, request.settlement_date
        )
        
        if success:
            return {"success": True, "message": "结算统计生成成功"}
        else:
            return {"success": False, "message": "结算统计生成失败，可能数据不足或记录已存在"}
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"手动生成结算统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

@router.get("/{settlement_id}", response_model=KolSettlementSchema)
def get_settlement_record_detail(
    settlement_id: int,
    db: Session = Depends(get_database_session)
):
    """
    获取结算统计记录详情
    """
    try:
        from models.settlement import KolSettlement
        
        record = db.query(KolSettlement).filter(
            KolSettlement.id == settlement_id
        ).first()
        
        if not record:
            raise HTTPException(status_code=404, detail="结算统计记录不存在")
        
        return record
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取结算统计详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}") 