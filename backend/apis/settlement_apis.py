from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional

from services.kol_settlement_service import KolSettlementService
from models.settlement import KolSettlementSchema
from utils.jwt import get_current_user
from models.user import UserInfo
from utils.logger import logger

router = APIRouter(prefix="/settlements", tags=["Settlement"])

@router.get("/user", response_model=List[KolSettlementSchema])
async def get_user_settlements(
    offset: int = 0,
    limit: int = 100,
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取当前登录广告主的结算信息列表。
    """

    logger.info(f'get settlements detail for user_id: {current_user.id}')
    settlements = KolSettlementService.get_settlements_by_user_id(
        user_id=current_user.id, 
        offset=offset, 
        limit=limit
    )
    return settlements

@router.get("/kol")
async def get_kol_settlements(
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(10, description="每页数量"),
    task_id: Optional[int] = Query(None, description="任务ID筛选"),
    settlement_month: Optional[str] = Query(None, description="结算月份筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    payment_method: Optional[str] = Query(None, description="支付方式筛选"),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取当前登录KOL的结算信息列表（增强版）
    支持按任务ID筛选，用于查看特定任务的收益数据
    """
    logger.info(f'get settlements detail for kol_id: {current_user.id}, task_id: {task_id}')

    try:
        # 如果指定了task_id，使用简化的查询方法
        if task_id is not None:
            settlements = KolSettlementService.get_settlements_by_kol_id(
                kol_id=current_user.id,
                offset=skip,
                limit=limit,
                task_id=task_id
            )
            return {
                "data": settlements,
                "total": len(settlements),
                "summary": None
            }
        else:
            # 使用完整的查询方法（包含任务信息和汇总）
            result = KolSettlementService.get_kol_settlements_with_task_info(
                kol_id=current_user.id,
                offset=skip,
                limit=limit,
                settlement_month=settlement_month,
                status=status,
                payment_method=payment_method
            )
            return result
    except Exception as e:
        logger.error(f"获取KOL结算记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取结算记录失败")

@router.get("/kol/{settlement_id}")
async def get_settlement_detail(
    settlement_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取结算记录详情
    """
    try:
        detail = KolSettlementService.get_settlement_detail(settlement_id)
        if not detail:
            raise HTTPException(status_code=404, detail="结算记录不存在")
        
        # 验证权限 - 只能查看自己的结算记录
        if detail["kol_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权限访问")
            
        return detail
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取结算详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取结算详情失败")

@router.get("/kol/summary")
async def get_kol_settlement_summary(
    current_user: UserInfo = Depends(get_current_user)
):
    """
    获取KOL结算汇总统计
    """
    try:
        summary = KolSettlementService.get_settlement_summary(current_user.id)
        return summary
    except Exception as e:
        logger.error(f"获取结算汇总失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取结算汇总失败") 