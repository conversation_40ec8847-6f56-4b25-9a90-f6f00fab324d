from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import HTMLResponse
from typing import Optional
from pydantic import BaseModel
from datetime import datetime

from utils.jwt import get_current_user
from models.user import UserInfo
from services.twitter_service import TwitterService, get_twitter_service
from services.kol_profile_service import KolProfileService, KolProfileResponse
from dependencies import get_kol_profile_service
from utils.logger import logger

router = APIRouter(prefix="/kol/twitter", tags=["Twitter Integration"])

class TwitterSyncResponse(BaseModel):
    success: bool
    profile: Optional[KolProfileResponse] = None
    message: str

@router.get("/oauth/url")
async def get_oauth_url(current_user: UserInfo = Depends(get_current_user)):
    """获取Twitter OAuth授权URL"""
    try:
        logger.info(f"Generating Twitter OAuth URL for user {current_user.id}")

        # 获取Twitter服务实例
        twitter_service = get_twitter_service()

        # 获取OAuth授权URL，使用user_id作为state
        auth_url = await twitter_service.get_oauth_url(str(current_user.id))

        logger.info(f"OAuth URL generated for user {current_user.id}")

        return {
            "auth_url": auth_url,
            "message": "请在新窗口中完成Twitter授权"
        }

    except Exception as e:
        logger.error(f"Failed to get Twitter OAuth URL for user {current_user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="获取授权链接失败")

@router.get("/oauth/callback")
async def oauth_callback(
    oauth_token: str,
    oauth_verifier: str,
    state: Optional[str] = None,  # 🔧 修复：state 参数改为可选
    kol_service: KolProfileService = Depends(get_kol_profile_service)
):
    """处理Twitter OAuth回调"""
    try:
        logger.info(f"Processing Twitter OAuth callback with oauth_token: {oauth_token[:10]}...")

        # 获取Twitter服务实例
        twitter_service = get_twitter_service()

        # 🔧 修复：处理OAuth回调，不依赖state参数
        twitter_data = await twitter_service.handle_oauth_callback(oauth_token, oauth_verifier, state)

        if not twitter_data:
            raise HTTPException(status_code=400, detail="OAuth授权失败")

        # 🔧 从返回的数据中获取关联的用户ID
        user_id = twitter_data.get('associated_user_id')
        if not user_id:
            raise HTTPException(status_code=400, detail="无法获取关联的用户ID")

        # OAuth流程中用户已通过认证，直接绑定即可
        # 如果需要检查重复绑定，可以在bind_twitter_account方法中处理

        # 绑定Twitter账号到KOL Profile
        profile = kol_service.bind_twitter_account(user_id, twitter_data)

        logger.info(f"Twitter OAuth completed for user {user_id}")

        # 返回成功页面
        html_content = f"""
        <html>
        <head><title>Twitter授权成功</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5;">
            <div style="background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 400px; margin: 0 auto;">
                <h2 style="color: #4CAF50; margin-bottom: 20px;">✅ Twitter授权成功！</h2>
                <p style="color: #666; margin-bottom: 20px;">您的Twitter账号已成功绑定</p>
                <p style="color: #999; font-size: 14px;">此窗口将在3秒后自动关闭</p>
            </div>
            <script>
                // 通知父窗口授权完成
                if (window.opener) {{
                    window.opener.postMessage({{type: 'twitter_auth_success'}}, '*');
                }}
                // 3秒后自动关闭窗口
                setTimeout(() => window.close(), 3000);
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Twitter OAuth callback failed: {str(e)}")
        # 返回错误页面
        error_html_content = f"""
        <html>
        <head><title>Twitter授权失败</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5;">
            <div style="background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 400px; margin: 0 auto;">
                <h2 style="color: #f44336; margin-bottom: 20px;">❌ Twitter授权失败</h2>
                <p style="color: #666; margin-bottom: 20px;">错误信息: {str(e)}</p>
                <p style="color: #999; font-size: 14px;">请关闭此窗口并重试</p>
            </div>
            <script>
                if (window.opener) {{
                    window.opener.postMessage({{type: 'twitter_auth_error', error: '{str(e)}'}}, '*');
                }}
                setTimeout(() => window.close(), 5000);
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=error_html_content)

@router.post("/sync", response_model=TwitterSyncResponse)
async def sync_twitter_data(
    current_user: UserInfo = Depends(get_current_user),
    kol_service: KolProfileService = Depends(get_kol_profile_service)
):
    """同步Twitter数据"""
    try:
        # 获取用户的KOL资料
        profile = kol_service.get_kol_profile_by_user_id(current_user.id)
        if not profile or not profile.platform_username:
            raise HTTPException(status_code=404, detail="未绑定Twitter账号")

        # 获取Twitter服务实例
        twitter_service = get_twitter_service()

        # 获取最新Twitter数据
        twitter_data = await twitter_service.get_user_data_by_username(profile.platform_username)

        if not twitter_data:
            raise HTTPException(status_code=404, detail="无法获取Twitter数据")

        # 同步数据
        updated_profile = kol_service.sync_twitter_data(current_user.id, twitter_data)

        logger.info(f"Twitter data synced for user {current_user.id}")

        return TwitterSyncResponse(
            success=True,
            profile=updated_profile,
            message="Twitter数据同步成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Twitter sync failed for user {current_user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="数据同步失败，请重试")

@router.delete("/unbind")
async def unbind_twitter(
    current_user: UserInfo = Depends(get_current_user),
    kol_service: KolProfileService = Depends(get_kol_profile_service)
):
    """解除Twitter绑定"""
    try:
        # 🔧 修复：直接调用服务层的解绑方法，确保所有字段都被正确清除
        success = kol_service.unbind_twitter_account(current_user.id)

        # 🔧 修复：解绑操作采用幂等设计，即使用户本来就没有绑定也返回成功
        # 这样避免了用户重复解绑时出现 404 错误
        logger.info(f"Twitter unbind attempted for user {current_user.id}, success: {success}")

        return {
            "success": True,
            "message": "Twitter账号解绑成功" if success else "Twitter账号未绑定或已解绑"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Twitter unbind failed for user {current_user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="解绑失败，请重试")

@router.get("/status")
async def get_twitter_status(
    current_user: UserInfo = Depends(get_current_user),
    kol_service: KolProfileService = Depends(get_kol_profile_service)
):
    """获取Twitter绑定状态"""
    try:
        status = kol_service.get_twitter_binding_status(current_user.id)
        return status

    except Exception as e:
        logger.error(f"Failed to get Twitter status for user {current_user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="获取绑定状态失败")

@router.get("/mock-auth")
async def mock_twitter_auth(state: str):
    """模拟Twitter授权页面"""
    html_content = f"""
    <html>
    <head>
        <title>模拟Twitter授权</title>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: #1da1f2;
                color: white;
                text-align: center;
                padding: 50px;
                margin: 0;
            }}
            .auth-container {{
                background: white;
                color: #333;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 2px 20px rgba(0,0,0,0.3);
                max-width: 400px;
                margin: 0 auto;
            }}
            .twitter-logo {{
                font-size: 48px;
                margin-bottom: 20px;
            }}
            .auth-button {{
                background: #1da1f2;
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 16px;
                border-radius: 25px;
                cursor: pointer;
                margin: 10px;
                transition: background 0.3s;
            }}
            .auth-button:hover {{
                background: #0d8bd9;
            }}
            .cancel-button {{
                background: #657786;
            }}
            .cancel-button:hover {{
                background: #5a6c79;
            }}
        </style>
    </head>
    <body>
        <div class="auth-container">
            <div class="twitter-logo">🐦</div>
            <h2>授权KOL Hub访问您的Twitter账号</h2>
            <p style="color: #666; margin: 20px 0;">
                这是一个模拟的Twitter授权页面。<br>
                在真实环境中，这里会是Twitter官方的授权页面。
            </p>
            <p style="color: #666; font-size: 14px; margin: 20px 0;">
                KOL Hub将能够：
            </p>
            <ul style="text-align: left; color: #666; font-size: 14px; margin: 20px 0;">
                <li>查看您的基本资料信息</li>
                <li>查看您的粉丝和关注数据</li>
                <li>查看您的推文统计</li>
            </ul>

            <button class="auth-button" onclick="authorize()">
                ✅ 授权应用
            </button>
            <button class="auth-button cancel-button" onclick="cancel()">
                ❌ 取消
            </button>
        </div>

        <script>
            function authorize() {{
                // 模拟授权成功，跳转到回调URL
                const callbackUrl = `http://localhost:8002/api/kol/twitter/oauth/callback?oauth_token=mock_token&oauth_verifier=mock_verifier&state={state}`;
                window.location.href = callbackUrl;
            }}

            function cancel() {{
                // 通知父窗口取消授权
                if (window.opener) {{
                    window.opener.postMessage({{type: 'twitter_auth_error', error: '用户取消授权'}}, '*');
                }}
                window.close();
            }}

            // 3秒后自动授权（用于演示）
            setTimeout(() => {{
                document.querySelector('.auth-button').style.background = '#28a745';
                document.querySelector('.auth-button').innerHTML = '⏰ 3秒后自动授权...';
                setTimeout(authorize, 3000);
            }}, 1000);
        </script>
    </body>
    </html>
    """

    return HTMLResponse(content=html_content)