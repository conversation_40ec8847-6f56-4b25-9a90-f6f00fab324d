from fastapi import APIRouter, Request, HTTPException, Depends, Query
from typing import Optional, Annotated, List
from pydantic import BaseModel, Field

from services.kol_task_service import get_kol_task, accept_task, submit_content, get_task_by_id, TaskAcceptRequest, ContentSubmitRequest, submit_draft, DraftSubmitRequest, submit_publish_links, PublishLinksRequest
from services.kol_settlement_service import KolSettlementService
from services.stats_service import get_kol_performance, get_kol_monthly_summary, get_kol_content_summary, get_task_stats_by_kol, get_performance_filter_options, get_kol_monthly_summary_filtered, get_kol_content_summary_filtered
from services.kol_profile_service import KolProfileService, KolProfileUpsert, KolProfileResponse
from services.kol_invitation_service import KolInvitationService
from dependencies import get_kol_profile_service
from utils.jwt import get_current_user
from models.user import UserInfo
from utils.logger import logger
from sqlalchemy import Table, select, func, or_
from sqlalchemy.engine import Result
from models import metadata, engine

# 数据库表
marketing_task = Table('marketing_task', metadata, autoload_with=engine)

router = APIRouter(prefix="/kol", tags=["kol"])

# KOL邀请响应模型
class InvitationResponseRequest(BaseModel):
    invitation_id: int
    action: str  # "accept" or "reject"
    reason: Optional[str] = None
    channel_code: Optional[str] = None  # 渠道码（现在从任务本身获取，此参数保留兼容性）
    remark: Optional[str] = None  # 新增：备注

# 新增申请相关数据模型
class TaskApplicationRequest(BaseModel):
    application_reason: str = Field(..., min_length=10, max_length=500, description="申请理由")

# KOL Profile 相关接口
@router.post("/profile", response_model=KolProfileResponse)
async def upsert_kol_profile(
    profile: KolProfileUpsert,
    current_user: UserInfo = Depends(get_current_user),
    kol_profile_service: KolProfileService = Depends(get_kol_profile_service)
):
    """创建或更新KOL资料（upsert操作）"""
    logger.info(f'Upsert KOL profile for user: {current_user.id}')
    # 设置当前用户ID
    profile.user_id = current_user.id
    return kol_profile_service.upsert_kol_profile(profile)

@router.get("/profile", response_model=KolProfileResponse)
async def get_current_kol_profile(
    current_user: UserInfo = Depends(get_current_user),
    kol_profile_service: KolProfileService = Depends(get_kol_profile_service)
):
    """获取当前用户的KOL资料"""
    logger.info(f'Get KOL profile for user: {current_user.id}')
    profile = kol_profile_service.get_kol_profile_by_user_id(current_user.id)
    if not profile:
        raise HTTPException(status_code=404, detail="KOL资料不存在")
    return profile

@router.get("/profile/{user_id}", response_model=KolProfileResponse)
async def get_kol_profile_by_id(
    user_id: int,
    current_user: UserInfo = Depends(get_current_user),
    kol_profile_service: KolProfileService = Depends(get_kol_profile_service)
):
    """根据用户ID获取KOL资料"""
    # 🔧 安全修复：只能查看自己的KOL资料
    if user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此KOL资料")

    logger.info(f'Get KOL profile for user: {user_id}')
    profile = kol_profile_service.get_kol_profile_by_user_id(user_id)
    if not profile:
        raise HTTPException(status_code=404, detail="KOL资料不存在")
    return profile




@router.get("/kol_task")
async def kol_task(
    current_user: UserInfo = Depends(get_current_user),
    status: Optional[str] = None
):
    logger.info(f'get kol task for kol_id: {current_user.id}, status: {status}')
    response = get_kol_task(current_user.id, status)
    return response

# 任务管理相关接口
@router.post("/task/accept")
async def accept_kol_task(
    request: TaskAcceptRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """KOL接收任务"""
    # 🔧 安全修复：防止身份伪造，确保只能为自己接收任务
    if request.kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此任务")

    logger.info(f'accept task: {request.task_id} by kol: {request.kol_id}')
    try:
        response = accept_task(request)
        return response
    except Exception as e:
        logger.error(f'Error accepting task: {str(e)}')
        raise HTTPException(status_code=500, detail="接收任务失败")

@router.post("/task/submit_content")
async def submit_task_content(
    request: ContentSubmitRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """KOL提交任务内容"""
    # 🔧 安全修复：防止身份伪造，确保只能为自己提交内容
    if request.kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此任务")

    logger.info(f'submit content for task: {request.task_id} by kol: {request.kol_id}')
    try:
        response = submit_content(request)
        return response
    except Exception as e:
        logger.error(f'Error submitting content: {str(e)}')
        raise HTTPException(status_code=500, detail="提交内容失败")

@router.get("/task/{task_id}")
async def get_task_detail(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取任务详情"""
    # 🔧 安全修复：添加用户认证，确保只有登录用户可以查看任务详情
    logger.info(f'get task detail for task_id: {task_id}, user: {current_user.id}')
    try:
        response = get_task_by_id(task_id)
        return response
    except Exception as e:
        logger.error(f'Error getting task detail: {str(e)}')
        raise HTTPException(status_code=500, detail="获取任务详情失败")

@router.get("/task/{task_id}/stats")
async def get_task_stats(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL任务的统计数据"""
    logger.info(f'get task stats for task_id: {task_id}, kol_id: {current_user.id}')
    try:
        response = get_task_stats_by_kol(task_id, current_user.id)
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f'Error getting task stats: {str(e)}')
        raise HTTPException(status_code=500, detail="获取任务统计数据失败")

@router.post("/task/{task_id}/submit_draft")
async def submit_task_draft(
    task_id: int,
    request: DraftSubmitRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """KOL提交任务草稿"""
    logger.info(f'submit draft for task: {task_id} by kol: {current_user.id}')
    try:
        response = submit_draft(task_id, current_user.id, request)
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f'Error submitting draft: {str(e)}')
        raise HTTPException(status_code=500, detail="提交草稿失败")

@router.post("/task/{task_id}/submit_links")
async def submit_task_publish_links(
    task_id: int,
    request: PublishLinksRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """KOL提交正式发布链接"""
    logger.info(f'submit publish links for task: {task_id} by kol: {current_user.id}')
    try:
        response = submit_publish_links(task_id, current_user.id, request)

        # 🔧 新增：提交链接后立即触发一次 Twitter 指标同步
        if response.get('success') and request.platform == 'twitter':
            try:
                from jobs.twitter_metrics_sync import sync_twitter_metrics_job
                import asyncio
                # 异步触发指标同步（不等待结果，避免阻塞响应）
                asyncio.create_task(sync_twitter_metrics_job())
                logger.info(f"已触发任务 {task_id} 的 Twitter 指标同步")
            except Exception as sync_error:
                logger.error(f"触发 Twitter 指标同步失败: {str(sync_error)}")
                # 不影响主流程，继续返回成功响应

        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f'Error submitting publish links: {str(e)}')
        raise HTTPException(status_code=500, detail="提交发布链接失败")

# 资产与结算相关接口
@router.get("/settlement/summary")
async def settlement_summary(
    kol_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL结算汇总统计"""
    # 🔧 安全修复：只能查看自己的结算汇总
    if kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此结算汇总")

    logger.info(f'get settlement summary for kol_id: {kol_id}')
    try:
        response = KolSettlementService.get_settlement_summary(kol_id)
        return response
    except Exception as e:
        logger.error(f'Error getting settlement summary: {str(e)}')
        raise HTTPException(status_code=500, detail="获取结算汇总失败")

# 业绩中心相关接口
@router.get("/performance")
async def kol_performance(
    kol_id: int,
    month: Optional[str] = None,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL业绩数据"""
    # 🔧 安全修复：只能查看自己的业绩数据
    if kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此业绩数据")

    logger.info(f'get kol performance for kol_id: {kol_id}, month: {month}')
    try:
        response = get_kol_performance(kol_id, month)
        return response
    except Exception as e:
        logger.error(f'Error getting kol performance: {str(e)}')
        raise HTTPException(status_code=500, detail="获取业绩数据失败")

@router.get("/performance/filter-options")
async def get_performance_filter_options_api(
    kol_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取业绩中心筛选选项"""
    # 🔧 安全修复：只能查看自己的筛选选项
    if kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此筛选选项")

    logger.info(f'get performance filter options for kol_id: {kol_id}')
    try:
        response = get_performance_filter_options(kol_id)
        return response
    except Exception as e:
        logger.error(f'Error getting filter options: {str(e)}')
        raise HTTPException(status_code=500, detail="获取筛选选项失败")

@router.get("/performance/monthly")
async def kol_monthly_summary(
    kol_id: int,
    start_month: Optional[str] = None,
    end_month: Optional[str] = None,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL按月汇总数据 - 支持日期范围筛选"""
    # 🔧 安全修复：只能查看自己的月度汇总数据
    if kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此月度汇总数据")

    logger.info(f'get kol monthly summary for kol_id: {kol_id}, start_month: {start_month}, end_month: {end_month}')
    try:
        response = get_kol_monthly_summary_filtered(kol_id, start_month, end_month)
        return response
    except Exception as e:
        logger.error(f'Error getting monthly summary: {str(e)}')
        raise HTTPException(status_code=500, detail="获取月度汇总失败")

@router.get("/performance/content")
async def kol_content_summary(
    kol_id: int,
    month: Optional[str] = None,
    task_names: Optional[List[str]] = Query(None),
    channel_codes: Optional[List[str]] = Query(None),
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL按内容汇总数据 - 支持多维筛选"""
    # 🔧 安全修复：只能查看自己的内容汇总数据
    if kol_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限查看此内容汇总数据")

    logger.info(f'get kol content summary for kol_id: {kol_id}, month: {month}, task_names: {task_names}, channel_codes: {channel_codes}')
    try:
        response = get_kol_content_summary_filtered(kol_id, month, task_names, channel_codes)
        return response
    except Exception as e:
        logger.error(f'Error getting content summary: {str(e)}')
        raise HTTPException(status_code=500, detail="获取内容汇总失败")

# KOL任务数据统一获取接口
@router.get("/my-tasks")
async def get_my_tasks(
    current_user: UserInfo = Depends(get_current_user),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取KOL的所有任务数据（统一接口，支持分页）"""
    logger.info(f'Get all tasks for KOL: {current_user.id}, page: {page}, size: {size}')
    try:
        # 使用统一查询获取分页任务数据
        result = KolInvitationService.get_kol_all_tasks_paginated(current_user.id, page, size)
        return result
    except Exception as e:
        logger.error(f"获取KOL任务数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务数据失败")

# KOL邀请相关接口
@router.get("/invitations")
async def get_kol_invitations(
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL的邀请列表"""
    logger.info(f'Get invitations for KOL: {current_user.id}')
    try:
        # 获取邀请列表，包含任务信息
        invitations = KolInvitationService.get_kol_invitations(current_user.id)

        # 为每个邀请添加任务详情和来源标识
        for invitation in invitations:
            # 保存原始的邀请记录ID
            original_invitation_id = invitation['id']

            # 获取任务信息
            task_info = KolInvitationService.get_task_info(invitation['task_id'])

            # 合并任务信息
            invitation.update(task_info)

            # 重命名ID字段，明确区分
            invitation['invitation_id'] = original_invitation_id  # kol_invitation.id
            invitation['marketing_task_id'] = task_info['id']     # marketing_task.id

            # 删除混淆的id字段
            if 'id' in invitation:
                del invitation['id']

            invitation['source'] = 'invitation'  # 添加来源标识

        return invitations
    except Exception as e:
        logger.error(f"获取KOL邀请列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取邀请列表失败")

@router.post("/invitation/respond")
async def respond_to_invitation(
    request: InvitationResponseRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """KOL响应邀请"""
    logger.info(f'KOL {current_user.id} responding to invitation {request.invitation_id} with action: {request.action}')
    try:
        if request.action == 'accept':
            response = KolInvitationService.accept_invitation(
                request.invitation_id, 
                current_user.id, 
                request.channel_code,
                request.remark  # 传递备注参数
            )
        elif request.action == 'reject':
            response = KolInvitationService.reject_invitation(
                request.invitation_id, 
                current_user.id, 
                request.reason
            )
        else:
            raise HTTPException(status_code=400, detail="无效的操作，只支持 'accept' 或 'reject'")
        
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"响应邀请失败: {str(e)}")
        raise HTTPException(status_code=500, detail="响应邀请失败")

# === 新增申请相关API ===
@router.post("/task/{task_id}/apply")
async def apply_for_task(
    task_id: int,
    request: TaskApplicationRequest,
    current_user: Annotated[UserInfo, Depends(get_current_user)]
):
    """KOL申请任务"""
    logger.info(f'KOL {current_user.id} applying for task {task_id}')
    
    response = KolInvitationService.apply_for_task(
        task_id=task_id,
        kol_id=current_user.id,
        application_reason=request.application_reason
    )
    
    return response

@router.get("/my-applications")
async def get_my_applications(
    current_user: Annotated[UserInfo, Depends(get_current_user)]
):
    """获取我的申请列表"""
    logger.info(f'Get applications for KOL: {current_user.id}')

    applications = KolInvitationService.get_kol_applications(current_user.id)

    # 附加任务信息和来源标识
    for application in applications:
        # 保存原始的申请记录ID
        original_application_id = application['id']

        # 获取任务信息
        task_info = KolInvitationService.get_task_info(application['task_id'])

        # 合并任务信息
        application.update(task_info)

        # 重命名ID字段，明确区分
        application['invitation_id'] = original_application_id  # kol_invitation.id
        application['marketing_task_id'] = task_info['id']      # marketing_task.id

        # 删除混淆的id字段
        if 'id' in application:
            del application['id']

        application['source'] = 'application'  # 添加来源标识

    return applications

@router.get("/tasks/published")
async def get_published_tasks(
    current_user: Annotated[UserInfo, Depends(get_current_user)],
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    task_type: Optional[str] = None,
    sort: str = Query("create_time"),
    keyword: Optional[str] = None
):
    """获取所有已发布任务（任务广场使用）"""
    # 打印当前用户ID和所有接口参数
    logger.info(f'Get published tasks for KOL: {current_user.id}, page: {page}, size: {size}, task_type: {task_type}, sort: {sort}, keyword: {keyword}')

    try:
        with engine.connect() as conn:
            # 构建查询
            stmt = select(marketing_task).where(marketing_task.c.task_status == 'published')
            
            # 类型筛选
            if task_type:
                stmt = stmt.where(marketing_task.c.task_type == task_type)
            
            # 关键词搜索
            if keyword:
                stmt = stmt.where(
                    or_(
                        marketing_task.c.task_name.like(f'%{keyword}%'),
                        marketing_task.c.description.like(f'%{keyword}%')
                    )
                )
            
            # 排序
            if sort == 'base_reward':
                stmt = stmt.order_by(marketing_task.c.base_reward.desc())
            elif sort == 'end_date':
                stmt = stmt.order_by(marketing_task.c.end_date.asc())
            else:  # create_time
                stmt = stmt.order_by(marketing_task.c.create_time.desc())
            
            # 分页
            offset = (page - 1) * size
            stmt = stmt.offset(offset).limit(size)
            
            result: Result = conn.execute(stmt)
            tasks = [dict(row._mapping) for row in result.fetchall()]
            
            # 获取总数
            count_stmt = select(func.count(marketing_task.c.id)).where(marketing_task.c.task_status == 'published')
            if task_type:
                count_stmt = count_stmt.where(marketing_task.c.task_type == task_type)
            if keyword:
                count_stmt = count_stmt.where(
                    or_(
                        marketing_task.c.task_name.like(f'%{keyword}%'),
                        marketing_task.c.description.like(f'%{keyword}%')
                    )
                )
            
            total_result = conn.execute(count_stmt)
            total = total_result.scalar()
            
            return {
                "data": tasks,
                "total": total,
                "page": page,
                "size": size
            }
            
    except Exception as e:
        logger.error(f"获取已发布任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取已发布任务失败")
class UpdateInvitationStatusRequest(BaseModel):
    task_id: int
    kol_id: int
    status: str
@router.post("/update_invitation_status")
async def update_invitation_status(
    request: UpdateInvitationStatusRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """更新邀请状态"""
    return KolInvitationService.update_invitation_status(
        task_id=request.task_id,
        kol_id=request.kol_id,
        status=request.status
    )