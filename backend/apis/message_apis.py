from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from sqlalchemy.orm import Session

from services.message_service import MessageService
from models.user import UserInfo
from utils.jwt import get_current_user
from utils.logger import logger
from dependencies import get_database_session

router = APIRouter(prefix="/message", tags=["message"])

@router.get("/")
async def get_user_messages(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取用户消息列表"""
    try:
        service = MessageService(db)
        messages = service.get_user_messages(current_user.id, skip, limit)
        return {
            "success": True,
            "data": [msg.model_dump() for msg in messages],
            "total": len(messages)
        }
    except Exception as e:
        logger.error(f"获取用户消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取消息失败")

@router.get("/unread")
async def get_unread_messages(
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取用户未读消息"""
    try:
        service = MessageService(db)
        messages = service.get_unread_messages(current_user.id)
        return {
            "success": True,
            "data": [msg.model_dump() for msg in messages],
            "count": len(messages)
        }
    except Exception as e:
        logger.error(f"获取未读消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取未读消息失败")

@router.get("/unread/count")
async def get_unread_count(
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取用户未读消息数量"""
    try:
        service = MessageService(db)
        count = service.get_unread_count(current_user.id)
        return {
            "success": True,
            "count": count
        }
    except Exception as e:
        logger.error(f"获取未读消息数量失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取未读消息数量失败")

@router.post("/{message_id}/read")
async def mark_message_as_read(
    message_id: int,
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """标记消息为已读"""
    try:
        service = MessageService(db)
        message = service.mark_message_as_read(message_id)
        return {
            "success": True,
            "data": message.model_dump()
        }
    except Exception as e:
        logger.error(f"标记消息已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="标记消息已读失败")

@router.post("/read-all")
async def mark_all_messages_as_read(
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """标记用户所有消息为已读"""
    try:
        service = MessageService(db)
        result = service.mark_all_messages_as_read(current_user.id)
        return {
            "success": True,
            "updated_count": result["updated_count"]
        }
    except Exception as e:
        logger.error(f"标记所有消息已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="标记所有消息已读失败")

@router.get("/task/{task_id}")
async def get_task_messages(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取特定任务相关的消息"""
    try:
        service = MessageService(db)
        # 这里可以添加按任务ID筛选消息的逻辑
        messages = service.get_user_messages(current_user.id)
        # 简单过滤，实际应该根据消息内容或元数据筛选
        task_messages = [msg for msg in messages if f"任务" in msg.content]
        return {
            "success": True,
            "data": [msg.model_dump() for msg in task_messages],
            "task_id": task_id
        }
    except Exception as e:
        logger.error(f"获取任务消息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务消息失败")
