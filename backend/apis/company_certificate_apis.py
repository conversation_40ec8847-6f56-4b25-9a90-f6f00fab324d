from fastapi import APIRouter, Depends, Form, UploadFile, File, HTTPException
from typing import List
from services.company_certificate_service import CompanyCertificateService
from models.company_certificate import CompanyCertificateResponse, CompanyCertificateReviewRequest
from dependencies import get_company_certificate_service
from utils.jwt import get_current_user
from utils.logger import logger
from models.user import UserInfo

router = APIRouter(prefix="/company-certificate", tags=["company-certificate"])


@router.post("/verify")
async def submit_certificate_verification(
    company_name: str = Form(...),
    certificate_type: str = Form(...),
    certificate_file: UploadFile = File(...),
    current_user: UserInfo = Depends(get_current_user),
    company_cert_service: CompanyCertificateService = Depends(get_company_certificate_service)
):
    """提交公司资质认证"""
    user_id = current_user.id
    logger.info(f"公司资质认证提交: 用户 {user_id}, 公司 {company_name}")
    
    return company_cert_service.submit_certificate_verification(
        user_id, company_name, certificate_type, certificate_file
    )


@router.get("/status")
async def get_certificate_status(
    current_user: UserInfo = Depends(get_current_user),
    company_cert_service: CompanyCertificateService = Depends(get_company_certificate_service)
):
    """获取当前用户公司资质认证状态"""
    user_id = current_user.id
    return company_cert_service.get_certificate_status(user_id)


@router.post("/review", response_model=CompanyCertificateResponse)
async def review_certificate(
    req: CompanyCertificateReviewRequest,
    current_user: UserInfo = Depends(get_current_user),
    company_cert_service: CompanyCertificateService = Depends(get_company_certificate_service)
):
    """审核公司资质认证（管理员功能）"""
    # TODO: Add admin permission check here
    logger.info(f"管理员审核公司资质认证: {req.id} -> {req.verify_status}")
    return company_cert_service.verify_certificate(req)


@router.get("/list")
async def get_all_certificates(
    page: int = 1,
    page_size: int = 20,
    current_user: UserInfo = Depends(get_current_user),
    company_cert_service: CompanyCertificateService = Depends(get_company_certificate_service)
):
    """获取所有公司资质认证记录（管理员功能）"""
    # TODO: Add admin permission check here
    return company_cert_service.get_all_certificates(page, page_size)


@router.post("/withdraw")
async def withdraw_certificate(
    current_user: UserInfo = Depends(get_current_user),
    company_cert_service: CompanyCertificateService = Depends(get_company_certificate_service)
):
    """撤回公司资质认证申请"""
    user_id = current_user.id
    logger.info(f"撤回公司资质认证: 用户 {user_id}")
    
    return company_cert_service.withdraw_certificate(user_id) 