from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Session

from models.db import get_database
from services.kol_profile_service import KolProfileService, KolProfileResponse, KolProfileCreate, KolProfileDetailResponse
from utils.jwt import get_current_user
from models.user import UserInfo

router = APIRouter(prefix="/kol", tags=["KOL Profile"])

@router.get("/profiles", response_model=List[KolProfileResponse])
async def get_kol_profiles(
    tag: Optional[str] = None,
    platform: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_database)
):
    """
    Get KOL profiles with optional filtering
    """
    service = KolProfileService(db)
    profiles = service.get_kol_profiles(skip=skip, limit=limit, platform=platform, tag_name=tag)
    return profiles

@router.get("/profile/{kol_id}", response_model=KolProfileResponse)
async def get_kol_profile(kol_id: int, db: Session = Depends(get_database)):
    """
    Get specific KOL profile by ID
    """
    service = KolProfileService(db)
    profile = service.get_kol_profile_by_id(kol_id)
    if not profile:
        raise HTTPException(status_code=404, detail="KOL profile not found")

    return profile

@router.get("/profile/current/detail", response_model=KolProfileDetailResponse)
async def get_current_user_kol_profile_detail(
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    获取当前登录用户的KOL详细资料
    """
    service = KolProfileService(db)
    profile_detail = service.get_kol_profile_detail_by_user_id(current_user.id)
    if not profile_detail:
        raise HTTPException(status_code=404, detail="当前用户还没有创建KOL资料")

    return profile_detail

@router.get("/profile/{kol_id}/detail", response_model=KolProfileDetailResponse)
async def get_kol_profile_detail(kol_id: int, db: Session = Depends(get_database)):
    """
    Get detailed KOL profile information including statistics and history
    """
    service = KolProfileService(db)
    profile_detail = service.get_kol_profile_detail(kol_id)
    if not profile_detail:
        raise HTTPException(status_code=404, detail="KOL profile not found")

    return profile_detail

# 注意：POST /profile 接口已在 kol_apis.py 中实现，使用 upsert 操作
# 这里移除重复的路由定义以避免冲突

@router.get("/profile", response_model=KolProfileResponse)
async def get_current_kol_profile(
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """
    Get current user's KOL profile
    """
    service = KolProfileService(db)
    profile = service.get_kol_profile_by_user_id(current_user.id)
    if not profile:
        raise HTTPException(status_code=404, detail="KOL profile not found")
    return profile
