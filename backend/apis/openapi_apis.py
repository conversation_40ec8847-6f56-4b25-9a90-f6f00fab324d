from fastapi import API<PERSON><PERSON><PERSON>, Header, HTTPException, status
from services.stats_service import (
    add_kol_stats, KOLMarketingStatsIn
)
from services.api_key_service import check_api_key

router = APIRouter(prefix="/stats", tags=["openapi"])

@router.post('/add_kol_stats')
def kol_stats_write(
    data: KOLMarketingStatsIn,
    authorization: str = Header(..., alias="Authorization")
):
    # 校验Authorization格式 Bearer {API_KEY}
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or missing Authorization header")
    api_key = authorization[7:].strip()
    check_api_key(api_key)
    return add_kol_stats(data)