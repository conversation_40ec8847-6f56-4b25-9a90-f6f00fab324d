from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from services.stats_service import (
    get_summary, get_kol_top5, get_kol_performance,
    get_task_api_stats, get_task_channel_stats
)
from models.user import UserInfo
from utils.jwt import get_current_user
from dependencies import get_database_session

router = APIRouter(prefix="/stats", tags=["stats"])

@router.get('/dashboard/summary')
def dashboard_summary(user_id: int):
    return get_summary(user_id)

@router.get('/dashboard/kol-top5')
def kol_top5(user_id: int):
    return get_kol_top5(user_id)

@router.get('/dashboard/kol_performance')
def kol_performance(user_id: int):
    return get_kol_performance(user_id)

@router.get('/task/{task_id}/api-stats')
async def get_task_api_statistics(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取任务的API统计数据 (data_source为api)"""
    try:
        return get_task_api_stats(task_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务API统计数据失败: {str(e)}")

@router.get('/task/{task_id}/channel-stats')
async def get_task_channel_statistics(
    task_id: int,
    channel_code: str = None,
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取任务的渠道统计数据 (按channel_code查询)"""
    try:
        return get_task_channel_stats(task_id, channel_code)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务渠道统计数据失败: {str(e)}")