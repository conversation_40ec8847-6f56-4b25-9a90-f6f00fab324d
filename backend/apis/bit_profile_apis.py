from fastapi import APIRouter, Depends, HTTPException
from typing import List

from services.bit_profile_service import BitProfileService, BitProfileCreate, BitProfileUpdate, BitProfileResponse
from dependencies import get_bit_profile_service
from utils.jwt import get_current_user
from models.user import UserInfo
from utils.logger import logger

router = APIRouter(prefix="/bit/profile", tags=["BIT Profile"])

@router.post("/", response_model=BitProfileResponse)
async def create_bit_profile(
    profile: BitProfileCreate,
    current_user: UserInfo = Depends(get_current_user),
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """创建BIT资料"""
    logger.info(f'Create BIT profile for user: {current_user.id}')
    # 设置当前用户ID
    profile.user_id = current_user.id
    return bit_profile_service.create_bit_profile(profile)

@router.get("/", response_model=BitProfileResponse)
async def get_current_bit_profile(
    current_user: UserInfo = Depends(get_current_user),
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """获取当前用户的BIT资料"""
    logger.info(f'Get BIT profile for user: {current_user.id}')
    profile = bit_profile_service.get_bit_profile_by_user_id(current_user.id)
    if not profile:
        raise HTTPException(status_code=404, detail="BIT资料不存在")
    return profile

@router.get("/{user_id}", response_model=BitProfileResponse)
async def get_bit_profile_by_id(
    user_id: int,
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """根据用户ID获取BIT资料"""
    logger.info(f'Get BIT profile for user: {user_id}')
    profile = bit_profile_service.get_bit_profile_by_user_id(user_id)
    if not profile:
        raise HTTPException(status_code=404, detail="BIT资料不存在")
    return profile

@router.put("/", response_model=BitProfileResponse)
async def update_bit_profile(
    profile_update: BitProfileUpdate,
    current_user: UserInfo = Depends(get_current_user),
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """更新当前用户的BIT资料"""
    logger.info(f'Update BIT profile for user: {current_user.id}')
    # 先获取现有资料
    existing_profile = bit_profile_service.get_bit_profile_by_user_id(current_user.id)
    if not existing_profile:
        raise HTTPException(status_code=404, detail="BIT资料不存在")
    return bit_profile_service.update_bit_profile(existing_profile.id, profile_update)

@router.delete("/")
async def delete_bit_profile(
    current_user: UserInfo = Depends(get_current_user),
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """删除当前用户的BIT资料"""
    logger.info(f'Delete BIT profile for user: {current_user.id}')
    # 先获取现有资料
    existing_profile = bit_profile_service.get_bit_profile_by_user_id(current_user.id)
    if not existing_profile:
        raise HTTPException(status_code=404, detail="BIT资料不存在")
    
    success = bit_profile_service.delete_bit_profile(existing_profile.id)
    if success:
        return {"message": "BIT资料删除成功"}
    else:
        raise HTTPException(status_code=500, detail="删除失败")

@router.get("/list/", response_model=List[BitProfileResponse])
async def get_bit_profiles(
    skip: int = 0,
    limit: int = 100,
    bit_profile_service: BitProfileService = Depends(get_bit_profile_service)
):
    """获取BIT资料列表（管理员功能）"""
    logger.info(f'Get BIT profiles list, skip: {skip}, limit: {limit}')
    return bit_profile_service.get_bit_profiles(skip, limit) 