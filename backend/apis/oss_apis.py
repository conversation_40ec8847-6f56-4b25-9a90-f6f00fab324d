from fastapi import APIRouter, File, UploadFile, HTTPException, Depends
from storage import upload_to_oss, get_download_url, delete_file, file_exists, CloudStorageError
import uuid
import os
from utils.jwt import get_current_user
from models.user import UserInfo

router = APIRouter(prefix="/oss", tags=["oss"])

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: UserInfo = Depends(get_current_user)
):
    """上传文件到云存储"""
    # 🔧 安全修复：只有登录用户可以上传文件
    try:
        # 生成文件路径
        file_ext = file.filename.split('.')[-1].lower()
        unique_filename = f"{current_user.id}/{file.filename}"
        
        # 使用文件的content_type，如果没有则使用默认值
        content_type = file.content_type or 'application/octet-stream'

        # 上传文件（直接传递文件对象，让存储层处理）
        url = upload_to_oss(file, unique_filename, content_type)

        return {
            "success": True,
            "url": url,
            "filename": unique_filename,
            "original_filename": file.filename,
            "content_type": content_type,
            "uploaded_by": current_user.id
        }
    except CloudStorageError as e:
        raise HTTPException(status_code=500, detail=f"云存储操作失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.get("/download/{object_name:path}")
def get_file_download_url(
    object_name: str,
    expires_in: int = 3600,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取文件的预签名下载链接"""
    # 🔧 安全修复：只有登录用户可以下载文件
    try:
        download_url = get_download_url(object_name, expires_in)
        return {
            "success": True,
            "download_url": download_url,
            "expires_in": expires_in,
            "object_name": object_name,
            "requested_by": current_user.id
        }
    except CloudStorageError as e:
        raise HTTPException(status_code=500, detail=f"获取下载链接失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{object_name:path}")
def delete_file_endpoint(
    object_name: str,
    current_user: UserInfo = Depends(get_current_user)
):
    """删除云存储中的文件"""
    # 🔧 安全修复：只有登录用户可以删除文件
    try:
        success = delete_file(object_name)
        if success:
            return {
                "success": True,
                "message": f"文件 {object_name} 删除成功",
                "deleted_by": current_user.id
            }
        else:
            raise HTTPException(status_code=404, detail="文件不存在或删除失败")
    except CloudStorageError as e:
        raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/exists/{object_name:path}")
def check_file_exists(
    object_name: str,
    current_user: UserInfo = Depends(get_current_user)
):
    """检查文件是否存在"""
    # 🔧 安全修复：只有登录用户可以检查文件存在性
    try:
        exists = file_exists(object_name)
        return {
            "success": True,
            "exists": exists,
            "object_name": object_name,
            "checked_by": current_user.id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
