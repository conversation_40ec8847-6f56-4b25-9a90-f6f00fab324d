from sqlalchemy import Column, Integer, String, DECIMAL, Date, TIMESTAMP, text, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel
from typing import Optional
from datetime import date, datetime

from .db import Base

# SQLAlchemy Model for settlement_detail table
class KolSettlement(Base):
    __tablename__ = 'settlement_detail'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='结算详情ID主键')
    kol_id = Column(Integer, nullable=False, comment='KOL用户ID，外键关联user_info.id')
    bit_id = Column(Integer, nullable=False, comment='广告主用户ID')
    task_id = Column(Integer, nullable=False, comment='关联任务分配ID，外键关联marketing_task.id')

    # 结算信息
    settlement_month = Column(String(7), nullable=False, comment='结算月份（YYYY-MM格式，分区键）')
    settlement_date = Column(Date, comment='结算日期')

    # 费用明细
    marketing_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='营销次数')
    base_reward = Column(DECIMAL(12, 2), nullable=False, server_default=text("'0.00'"), comment='营销单价')
    base_total = Column(DECIMAL(14, 2), nullable=False, server_default=text("'0.00'"), comment='基础费用 = 营销单价 * 营销次数')
    performance_value = Column(DECIMAL(14, 2), nullable=False, server_default=text("'0.00'"), comment='效果值')
    performance_rate = Column(DECIMAL(12, 2), nullable=False, server_default=text("'0.00'"), comment='效果单价')
    performance_total = Column(DECIMAL(14, 2), nullable=False, server_default=text("'0.00'"), comment='效果佣金 = 效果值 * 效果单价')
    commission_value = Column(DECIMAL(14, 2), nullable=False, server_default=text("'0.00'"), comment='带单佣金值')
    commission_rate = Column(DECIMAL(12, 2), nullable=False, server_default=text("'0.00'"), comment='带单抽佣比例')
    commission_total = Column(DECIMAL(14, 2), nullable=False, server_default=text("'0.00'"), comment='带单佣金 = 带单佣金值 * 带单抽佣比例')
    total_fee = Column(DECIMAL(16, 2), nullable=False, server_default=text("'0.00'"), comment='费用总计')

    # 支付信息
    status = Column(String(20), nullable=False, server_default=text("'pending'"), comment='结算状态：pending(待支付)、paid(已支付)、failed(支付失败)')
    payment_method = Column(String(20), server_default=text("'crypto_wallet'"), comment='支付方式：crypto_wallet(加密钱包)、bank_transfer(银行转账)')
    wallet_address = Column(String(100), comment='收款钱包地址')
    transaction_hash = Column(String(100), comment='交易哈希')
    payment_network = Column(String(50), comment='支付网络：ethereum、bsc、polygon等')
    paid_time = Column(DateTime, comment='支付时间')
    payment_note = Column(Text, comment='支付备注')

    create_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

# Pydantic Schema for API responses
class KolSettlementSchema(BaseModel):
    id: int
    kol_id: int
    bit_id: int
    task_id: int
    kol_name: Optional[str] = None  # KOL名称
    task_name: Optional[str] = None  # 任务名称
    settlement_month: str
    settlement_date: Optional[date] = None
    marketing_count: int
    base_reward: float
    base_total: float
    performance_value: float
    performance_rate: float
    performance_total: float
    commission_value: float
    commission_rate: float
    commission_total: float  # 带单佣金
    total_fee: float
    status: str
    payment_method: Optional[str] = None
    wallet_address: Optional[str] = None
    transaction_hash: Optional[str] = None
    tx_hash: Optional[str] = None  # 交易哈希（字段名映射）
    payment_network: Optional[str] = None
    paid_time: Optional[datetime] = None
    payment_note: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    created_at: Optional[datetime] = None  # 创建时间（字段名映射）
    updated_at: Optional[datetime] = None  # 更新时间（字段名映射）

    class Config:
        from_attributes = True 