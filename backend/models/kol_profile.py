from sqlalchemy import Column, BigInteger, String, Text, Boolean, DateTime, DECIMAL, func
from .db import Base

class KolProfile(Base):
    __tablename__ = 'kol_profile'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID主键')
    user_id = Column(BigInteger, nullable=False, comment='关联用户ID，外键关联user_info.id')
    platform = Column(String(30), comment='平台信息')
    profile_url = Column(String(255), comment='主页/资料链接')
    platform_id = Column(String(50), nullable=False, unique=True, comment='platform 官方用户ID，全局唯一')
    platform_username = Column(String(100), nullable=False, comment='platform 用户名（@后面的部分）')
    platform_name = Column(String(100), comment='platform 显示名称')
    description = Column(Text, comment='个人简介')
    location = Column(String(100), comment='地理位置信息')
    profile_image_url = Column(String(255), comment='platform 头像URL')
    verified = Column(Boolean, default=False, comment='是否为认证账号')
    verified_type = Column(String(20), comment='认证类型：blue(蓝V)、business(企业)、government(政府)')
    account_created_at = Column(DateTime, comment='社交平台账号创建时间')
    
    # 公开指标
    followers_count = Column(BigInteger, default=0, comment='粉丝数量')
    following_count = Column(BigInteger, default=0, comment='关注数量')
    tweet_count = Column(BigInteger, default=0, comment='推文总数')
    listed_count = Column(BigInteger, default=0, comment='列表数量')
    like_count = Column(BigInteger, default=0, comment='获赞总数')
    tag_name = Column(String(255), nullable=False, comment='标签名称，如：DeFi、NFT、GameFi、技术分析等')

    # 🆕 报价信息字段
    single_tweet_price = Column(DECIMAL(10, 2), comment='单条X推文报价（USD）')
    single_ftt_price = Column(DECIMAL(10, 2), comment='单个FTT报价（USD，可选）')
    commission_rate = Column(DECIMAL(5, 2), comment='带单返佣比例（百分比，如 5.00 表示 5%）')

    # 数据更新时间
    last_synced_at = Column(DateTime, default=func.now(), comment='最后同步时间')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')