from sqlalchemy import Column, BigInteger, String, DateTime, Integer, ForeignKey, func
from .db import Base

class UserApiKey(Base):
    __tablename__ = 'user_api_key'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey('user_info.id'), nullable=False)
    api_key = Column(String(64), unique=True, nullable=False)
    name = Column(String(64))
    status = Column(Integer, default=1, nullable=False)  # 1=启用, 0=禁用, 2=已删除
    permissions = Column(String(255))
    remark = Column(String(128))
    create_time = Column(DateTime, server_default=func.now())
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())
