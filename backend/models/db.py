import os
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager

Base = declarative_base()

# 数据库连接配置（请根据实际情况修改）
MYSQL_USER = os.getenv('MYSQL_USER', 'mufool123456')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', 'VB1x6HG2kIzmOOwQ')
MYSQL_HOST = os.getenv('MYSQL_HOST', 'mysql5.sqlpub.com')
MYSQL_PORT = os.getenv('MYSQL_PORT', '3310')
MYSQL_DB = os.getenv('MYSQL_DB', 'mufool_db')

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}"

# 数据库引擎配置，添加连接池和超时设置
engine = create_engine(
    DATABASE_URL,
    # 连接池配置
    pool_size=10,                    # 连接池大小
    max_overflow=20,                 # 超出连接池大小的最大连接数
    pool_timeout=30,                 # 获取连接的超时时间（秒）
    pool_recycle=3600,               # 连接回收时间（秒），1小时
    pool_pre_ping=True,              # 连接前检查连接是否有效

    # 连接参数
    connect_args={
        "connect_timeout": 10,       # 连接超时（秒）
        "read_timeout": 30,          # 读取超时（秒）
        "write_timeout": 30,         # 写入超时（秒）
        "charset": "utf8mb4",        # 字符集
        "autocommit": False,         # 禁用自动提交
    },

    # 其他配置
    echo=False,                      # 是否打印SQL语句（生产环境建议关闭）
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
metadata = MetaData()
Base.metadata.create_all(bind=engine)

@contextmanager
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# FastAPI 依赖注入版本
def get_database():
    """FastAPI 依赖注入使用的数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def check_database_connection():
    """检查数据库连接是否正常"""
    try:
        with get_db() as db:
            # 执行简单查询测试连接
            result = db.execute(text("SELECT 1 as test")).fetchone()
            return result is not None
    except Exception as e:
        print(f"数据库连接检查失败: {e}")
        return False


def get_database_info():
    """获取数据库连接信息（用于调试）"""
    try:
        with get_db() as db:
            # 获取数据库版本和连接信息
            version_result = db.execute(text("SELECT VERSION() as version")).fetchone()
            connection_id_result = db.execute(text("SELECT CONNECTION_ID() as conn_id")).fetchone()

            return {
                "database_version": version_result[0] if version_result else "Unknown",
                "connection_id": connection_id_result[0] if connection_id_result else "Unknown",
                "database_url": f"{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}",
                "pool_size": engine.pool.size(),
                "checked_out_connections": engine.pool.checkedout(),
            }
    except Exception as e:
        return {"error": str(e)}