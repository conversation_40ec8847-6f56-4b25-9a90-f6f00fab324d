from sqlalchemy import Column, BigInteger, String, Date, Integer, DECIMAL, TIMESTAMP, Text
from .db import Base

class KOLMarketingStats(Base):
    __tablename__ = 'kol_marketing_stats'
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='效果数据ID主键')
    user_id = Column(BigInteger, nullable=False)
    kol_id = Column(BigInteger, nullable=False)
    task_id = Column(BigInteger, nullable=False, comment='关联任务分配ID，外键关联marketing_task.id')
    task_name = Column(String(255))
    stat_date = Column(Date, nullable=False, comment='统计日期（数据按天存储）')
    stat_month = Column(String(20), nullable=False, comment='统计月份（如2024-06，便于按月聚合）')
    click_count = Column(Integer, default=0, comment='点击量')
    register_count = Column(Integer, default=0, comment='注册量')
    ftt = Column(DECIMAL(18,2), default=0, comment='FTT值')
    deposit_amount = Column(DECIMAL(18,2), default=0, comment='入金金额')
    order_amount = Column(DECIMAL(18,2), default=0, comment='带单金额')
    data_source = Column(String(50), default='api', comment='数据来源：api(自动获取)、manual(手动录入)')
    channel_code = Column(String(32), comment='渠道码')
    create_time = Column(TIMESTAMP, comment='创建时间')
    update_time = Column(TIMESTAMP, comment='更新时间')