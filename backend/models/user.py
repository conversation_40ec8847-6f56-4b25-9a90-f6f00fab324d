from sqlalchemy import Column, Integer, String, DateTime, Enum, func, Boolean
from .db import Base

class UserInfo(Base):
    __tablename__ = 'user_info'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(64), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    email = Column(String(128), unique=True, nullable=False)
    phone = Column(String(32))
    nickname = Column(String(64))
    avatar = Column(String(255))
    wallet_address = Column(String(100), comment='收款钱包地址（ERC20等）')
    user_type = Column(Enum('merchant', 'kol'), nullable=False, default='kol', server_default='kol', comment='用户类型：merchant=商户，kol=KOL主播')
    status = Column(Enum('active', 'inactive', 'banned'), default='active')
    create_time = Column(DateTime, server_default=func.now())
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now())
    real_name = Column(String(64), comment='实名姓名')
    id_type = Column(String(32), default='id_card', comment='证件类型：id_card=身份证，passport=护照，military_id=军官证')
    id_number = Column(String(32), comment='证件号')
    id_validity = Column(String(128), comment='证件有效期')
    is_long_term = Column(Boolean, default=False, comment='是否长期有效')
    verify_status = Column(Enum('unverified', 'pending', 'approved', 'rejected'), default='unverified', server_default='unverified', comment='认证状态')
    verify_image_url = Column(String(255), comment='认证图片')
    verify_remark = Column(String(255), comment='审核备注')
    id_card_front_url = Column(String(255), comment='身份证正面图片')
    id_card_back_url = Column(String(255), comment='身份证反面图片')
