from sqlalchemy import Column, BigInteger, String, Text, DateTime, func
from .db import Base

class BitProfile(Base):
    __tablename__ = 'bit_profile'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID主键')
    user_id = Column(BigInteger, nullable=False, comment='关联用户ID，外键关联user_info.id')
    description = Column(Text, comment='简介')
    image_urls = Column(Text, comment='认证材料')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间') 