from sqlalchemy import Column, Integer, String, Boolean, DateTime
from datetime import datetime
from .db import Base

class Message(Base):
    __tablename__ = "message"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False)
    content = Column(String(1024), nullable=False)
    message_type = Column(String(20), default='system', comment='消息类型：system(系统消息)、task(任务消息)、settlement(结算消息)')
    is_read = Column(Boolean, default=False)
    create_time = Column(DateTime, default=datetime.now)