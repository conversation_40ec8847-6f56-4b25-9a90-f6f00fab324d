from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, DateTime, func
from .db import Base


class CompanyCertificateVerify(Base):
    """公司资质认证SQLAlchemy模型"""
    __tablename__ = 'company_certificate_verify'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='认证ID主键')
    user_id = Column(Integer, nullable=False, comment='关联用户ID，外键关联user_info.id')
    company_name = Column(String(255), nullable=False, comment='公司名称')
    certificate_type = Column(String(50), nullable=False, comment='证书类型：business_license(营业执照)、org_code(组织机构代码证)、tax_registration(税务登记证)、other(其他证书)')
    certificate_url = Column(String(255), nullable=False, comment='证书图片URL')
    verify_status = Column(String(20), default='pending', server_default='pending', comment='认证状态：pending(待审核)、approved(已通过)、rejected(已拒绝)')
    verify_remark = Column(String(255), comment='审核备注')
    create_time = Column(DateTime, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment='更新时间')


class CompanyCertificateSubmitRequest(BaseModel):
    """提交公司资质认证请求"""
    company_name: str = Field(..., max_length=255, description="公司名称")
    certificate_type: str = Field(..., description="证书类型")


class CompanyCertificateResponse(BaseModel):
    """公司资质认证响应"""
    id: int
    user_id: int
    company_name: str
    certificate_type: str
    certificate_url: str
    verify_status: str
    verify_remark: Optional[str] = None
    create_time: datetime
    update_time: datetime

    model_config = {'from_attributes': True}


class CompanyCertificateReviewRequest(BaseModel):
    """审核公司资质认证请求"""
    id: int = Field(..., description="认证记录ID")
    verify_status: str = Field(..., description="审核状态：approved/rejected")
    verify_remark: Optional[str] = Field(None, max_length=255, description="审核备注") 