from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date, datetime
from decimal import Decimal
from models.MarketingTask import Material

class MarketingTaskCreate(BaseModel):
    task_name: str = Field(..., description="任务名称")
    kol_id: Optional[int] = Field(None, description="关联KOL用户ID，创建时可选")
    task_status: str = Field(default='draft', description="任务状态：draft/published/assigned/unapproved/approved/rejected/completed/cancelled")
    task_type: str = Field(default='post', description="任务类型：post/video/article/live_stream/ama_activity")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="截止日期")
    description: Optional[str] = Field(None, description="任务描述、包含任务发布要求")
    official_materials: Optional[List[Material]] = Field(None, description="官方素材JSON格式，包含图片、视频和链接")
    
    # 新增优化字段
    creator: int = Field(..., description="项目方创建人ID")
    
    # 奖励类型和配置
    reward_type: str = Field(default='branding', description="奖励类型：branding(品牌推广)、commission(带单返佣)、branding_plus_conversion(品牌推广+按转化付费)")
    base_reward: Decimal = Field(..., description="基础奖励金额")
    performance_rate: Optional[Decimal] = Field(default=0, description="按转化付费FTT单价（$/个）")
    commission_rate: Optional[Decimal] = Field(default=0, description="带单返佣比例（%）")
    conversion_reward_per_ftt: Optional[Decimal] = Field(default=0, description="每个FTT的转化奖励金额")
    enable_conversion_reward: Optional[bool] = Field(default=False, description="是否启用按转化付费")
    
    target_kol_count: Optional[int] = Field(default=1, description="目标KOL数量")
    review_feedback: Optional[str] = Field(None, description="审核反馈意见")
    published_links: Optional[str] = Field(None, description="正式发布链接（JSON数组）")
    draft_content: Optional[str] = Field(None, description="待发布内容文本、待发布内容截图、创作说明")
    draft_submit_time: Optional[datetime] = Field(None, description="草稿提交时间")
    draft_reviewed_at: Optional[datetime] = Field(None, description="草稿审核时间")
    assigned_time: Optional[datetime] = Field(None, description="任务分配时间")
    channel_code: Optional[str] = Field(None, description="渠道码")