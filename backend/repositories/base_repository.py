from abc import ABC, abstractmethod
from typing import Generic, TypeVar, List, Optional, Any, Dict
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from models.db import Base

T = TypeVar('T', bound=Base)

class BaseRepository(Generic[T], ABC):
    """基础仓储类，提供通用的CRUD操作"""
    
    def __init__(self, db: Session, model_class: type[T]):
        self.db = db
        self.model_class = model_class
    
    def create(self, obj_data: Dict[str, Any]) -> T:
        """创建新记录"""
        try:
            db_obj = self.model_class(**obj_data)
            self.db.add(db_obj)
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            raise e
    
    def get_by_id(self, obj_id: int) -> Optional[T]:
        """根据ID获取记录"""
        return self.db.query(self.model_class).filter(self.model_class.id == obj_id).first()
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """获取所有记录（分页）"""
        return self.db.query(self.model_class).offset(skip).limit(limit).all()
    
    def update(self, obj_id: int, update_data: Dict[str, Any]) -> Optional[T]:
        """更新记录"""
        try:
            db_obj = self.get_by_id(obj_id)
            if not db_obj:
                return None
            
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            self.db.commit()
            self.db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            self.db.rollback()
            raise e
    
    def delete(self, obj_id: int) -> bool:
        """删除记录"""
        try:
            db_obj = self.get_by_id(obj_id)
            if not db_obj:
                return False
            
            self.db.delete(db_obj)
            self.db.commit()
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            raise e
    
    def exists(self, **filters) -> bool:
        """检查记录是否存在"""
        query = self.db.query(self.model_class)
        for field, value in filters.items():
            if hasattr(self.model_class, field):
                query = query.filter(getattr(self.model_class, field) == value)
        return query.first() is not None
    
    def count(self, **filters) -> int:
        """统计记录数量"""
        query = self.db.query(self.model_class)
        for field, value in filters.items():
            if hasattr(self.model_class, field):
                query = query.filter(getattr(self.model_class, field) == value)
        return query.count()
