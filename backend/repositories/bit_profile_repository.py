from typing import Optional, List
from sqlalchemy.orm import Session

from models.bit_profile import BitProfile
from utils.logger import logger

class BitProfileRepository:
    """BIT Profile数据访问层"""

    def __init__(self, db: Session):
        self.db = db

    def create(self, profile_data: dict) -> BitProfile:
        """创建BIT资料"""
        profile = BitProfile(**profile_data)
        self.db.add(profile)
        self.db.commit()
        self.db.refresh(profile)
        return profile

    def get_by_id(self, profile_id: int) -> Optional[BitProfile]:
        """根据ID获取BIT资料"""
        return self.db.query(BitProfile).filter(BitProfile.id == profile_id).first()

    def get_by_user_id(self, user_id: int) -> Optional[BitProfile]:
        """根据用户ID获取BIT资料"""
        return self.db.query(BitProfile).filter(BitProfile.user_id == user_id).first()

    def update(self, profile_id: int, update_data: dict) -> Optional[BitProfile]:
        """更新BIT资料"""
        profile = self.get_by_id(profile_id)
        if not profile:
            return None

        for key, value in update_data.items():
            if hasattr(profile, key):
                setattr(profile, key, value)

        self.db.commit()
        self.db.refresh(profile)
        return profile

    def delete(self, profile_id: int) -> bool:
        """删除BIT资料"""
        profile = self.get_by_id(profile_id)
        if not profile:
            return False

        self.db.delete(profile)
        self.db.commit()
        return True

    def get_profiles(self, skip: int = 0, limit: int = 100) -> List[BitProfile]:
        """获取BIT资料列表"""
        return self.db.query(BitProfile).offset(skip).limit(limit).all()

    def count_profiles(self) -> int:
        """统计BIT资料数量"""
        return self.db.query(BitProfile).count() 