from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc
from models.Message import Message
from .base_repository import BaseRepository

class MessageRepository(BaseRepository[Message]):
    """消息数据访问层"""
    
    def __init__(self, db: Session):
        super().__init__(db, Message)
    
    def get_messages_by_user(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Message]:
        """根据用户ID获取消息列表"""
        return self.db.query(Message).filter(
            Message.user_id == user_id
        ).order_by(desc(Message.create_time)).offset(skip).limit(limit).all()
    
    def get_unread_messages_by_user(self, user_id: int) -> List[Message]:
        """获取用户未读消息"""
        return self.db.query(Message).filter(
            Message.user_id == user_id,
            Message.is_read == False
        ).order_by(desc(Message.create_time)).all()
    
    def mark_as_read(self, message_id: int) -> Optional[Message]:
        """标记消息为已读"""
        return self.update(message_id, {"is_read": True})
    
    def mark_all_as_read(self, user_id: int) -> int:
        """标记用户所有消息为已读"""
        count = self.db.query(Message).filter(
            Message.user_id == user_id,
            Message.is_read == False
        ).update({"is_read": True})
        self.db.commit()
        return count
    
    def count_unread_messages(self, user_id: int) -> int:
        """统计用户未读消息数量"""
        return self.db.query(Message).filter(
            Message.user_id == user_id,
            Message.is_read == False
        ).count()
