from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, date
from models.MarketingTask import MarketingTask
from .base_repository import BaseRepository

class MarketingTaskRepository(BaseRepository[MarketingTask]):
    """营销任务数据访问层"""
    
    def __init__(self, db: Session):
        super().__init__(db, MarketingTask)
    
    def get_tasks_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据状态获取任务列表"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.task_status == status
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def get_tasks_by_user(self, user_id: int, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据用户ID获取任务列表"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.bit_user_id == user_id
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def get_tasks_by_kol(self, kol_user_id: int, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据KOL用户ID获取任务列表"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.kol_user_id == kol_user_id
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def get_tasks_by_type(self, task_type: str, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据任务类型获取任务列表"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.task_type == task_type
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def get_tasks_by_date_range(self, start_date: date, end_date: date, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据日期范围获取任务列表"""
        return self.db.query(MarketingTask).filter(
            and_(
                MarketingTask.start_date >= start_date,
                MarketingTask.end_date <= end_date
            )
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def search_tasks(self, keyword: str, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """搜索任务（根据任务名称、KOL名称、描述）"""
        return self.db.query(MarketingTask).filter(
            or_(
                MarketingTask.task_name.contains(keyword),
                MarketingTask.kol_name.contains(keyword),
                MarketingTask.description.contains(keyword)
            )
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def get_tasks_with_filters(self, 
                              status: Optional[str] = None,
                              task_type: Optional[str] = None,
                              user_id: Optional[int] = None,
                              kol_user_id: Optional[int] = None,
                              channel_code: Optional[str] = None,
                              skip: int = 0, 
                              limit: int = 100) -> List[MarketingTask]:
        """根据多个条件筛选任务"""
        query = self.db.query(MarketingTask)
        
        if status:
            query = query.filter(MarketingTask.task_status == status)
        if task_type:
            query = query.filter(MarketingTask.task_type == task_type)
        if user_id:
            query = query.filter(MarketingTask.bit_user_id == user_id)
        if kol_user_id:
            query = query.filter(MarketingTask.kol_user_id == kol_user_id)
        if channel_code:
            query = query.filter(MarketingTask.channel_code == channel_code)
        
        return query.order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def update_task_status(self, task_id: int, new_status: str) -> Optional[MarketingTask]:
        """更新任务状态"""
        return self.update(task_id, {
            "task_status": new_status,
            "update_date": datetime.now()
        })
    
    def count_tasks_by_status(self, status: str) -> int:
        """统计指定状态的任务数量"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.task_status == status
        ).count()
    
    def count_tasks_by_user(self, user_id: int) -> int:
        """统计用户的任务数量"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.bit_user_id == user_id
        ).count()
    
    def get_task_by_channel_code(self, channel_code: str) -> Optional[MarketingTask]:
        """根据渠道码获取任务"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.channel_code == channel_code
        ).first()
    
    def get_tasks_by_channel_code(self, channel_code: str, skip: int = 0, limit: int = 100) -> List[MarketingTask]:
        """根据渠道码获取任务列表"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.channel_code == channel_code
        ).order_by(desc(MarketingTask.create_date)).offset(skip).limit(limit).all()
    
    def search_tasks_by_channel_code(self, channel_code: str) -> List[MarketingTask]:
        """模糊搜索渠道码"""
        return self.db.query(MarketingTask).filter(
            MarketingTask.channel_code.contains(channel_code)
        ).order_by(desc(MarketingTask.create_date)).all()
