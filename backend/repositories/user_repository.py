from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from models.user import UserInfo
from .base_repository import BaseRepository

class UserRepository(BaseRepository[UserInfo]):
    """用户数据访问层"""
    
    def __init__(self, db: Session):
        super().__init__(db, UserInfo)
    
    def get_by_username(self, username: str) -> Optional[UserInfo]:
        """根据用户名获取用户"""
        return self.db.query(UserInfo).filter(UserInfo.username == username).first()
    
    def get_by_email(self, email: str) -> Optional[UserInfo]:
        """根据邮箱获取用户"""
        return self.db.query(UserInfo).filter(UserInfo.email == email).first()
    
    def get_by_username_and_type(self, username: str, user_type: str) -> Optional[UserInfo]:
        """根据用户名和用户类型获取用户"""
        return self.db.query(UserInfo).filter(
            and_(UserInfo.username == username, UserInfo.user_type == user_type)
        ).first()
    
    def check_username_exists(self, username: str) -> bool:
        """检查用户名是否存在"""
        return self.exists(username=username)
    
    def check_email_exists(self, email: str) -> bool:
        """检查邮箱是否存在"""
        return self.exists(email=email)
    
    def get_users_by_type(self, user_type: str, skip: int = 0, limit: int = 100) -> List[UserInfo]:
        """根据用户类型获取用户列表"""
        return self.db.query(UserInfo).filter(
            UserInfo.user_type == user_type
        ).offset(skip).limit(limit).all()
    
    def get_users_by_status(self, status: str, skip: int = 0, limit: int = 100) -> List[UserInfo]:
        """根据状态获取用户列表"""
        return self.db.query(UserInfo).filter(
            UserInfo.status == status
        ).offset(skip).limit(limit).all()
    
    def get_users_by_verify_status(self, verify_status: str, skip: int = 0, limit: int = 100) -> List[UserInfo]:
        """根据认证状态获取用户列表"""
        return self.db.query(UserInfo).filter(
            UserInfo.verify_status == verify_status
        ).offset(skip).limit(limit).all()
    
    def search_users(self, keyword: str, user_type: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[UserInfo]:
        """搜索用户（根据用户名、昵称、邮箱）"""
        query = self.db.query(UserInfo).filter(
            or_(
                UserInfo.username.contains(keyword),
                UserInfo.nickname.contains(keyword),
                UserInfo.email.contains(keyword)
            )
        )
        
        if user_type:
            query = query.filter(UserInfo.user_type == user_type)
        
        return query.offset(skip).limit(limit).all()
    
    def update_verify_status(self, user_id: int, verify_status: str, verify_remark: Optional[str] = None) -> Optional[UserInfo]:
        """更新用户认证状态"""
        update_data = {"verify_status": verify_status}
        if verify_remark:
            update_data["verify_remark"] = verify_remark
        
        return self.update(user_id, update_data)
