from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from models.kol_profile import KolProfile
from utils.logger import logger

class KolProfileRepository:
    """KOL Profile数据访问层"""

    def __init__(self, db: Session):
        self.db = db

    def create(self, profile_data: dict) -> KolProfile:
        """创建KOL资料"""
        profile = KolProfile(**profile_data)
        self.db.add(profile)
        self.db.commit()
        self.db.refresh(profile)
        return profile

    def get_by_id(self, profile_id: int) -> Optional[KolProfile]:
        """根据ID获取KOL资料"""
        return self.db.query(KolProfile).filter(KolProfile.id == profile_id).first()

    def get_by_user_id(self, user_id: int) -> Optional[KolProfile]:
        """根据用户ID获取KOL资料"""
        return self.db.query(KolProfile).filter(KolProfile.user_id == user_id).first()

    def get_by_platform_id(self, platform_id: str) -> Optional[KolProfile]:
        """根据平台ID获取KOL资料"""
        return self.db.query(KolProfile).filter(KolProfile.platform_id == platform_id).first()

    def update(self, profile_id: int, update_data: dict) -> Optional[KolProfile]:
        """更新KOL资料"""
        profile = self.get_by_id(profile_id)
        if not profile:
            return None

        for key, value in update_data.items():
            if hasattr(profile, key):
                setattr(profile, key, value)

        self.db.commit()
        self.db.refresh(profile)
        return profile

    def delete(self, profile_id: int) -> bool:
        """删除KOL资料"""
        profile = self.get_by_id(profile_id)
        if not profile:
            return False

        self.db.delete(profile)
        self.db.commit()
        return True

    def get_profiles(self, skip: int = 0, limit: int = 100, 
                    platform: Optional[str] = None, 
                    tag_name: Optional[str] = None) -> List[KolProfile]:
        """获取KOL资料列表（支持筛选）"""
        query = self.db.query(KolProfile)

        if platform:
            query = query.filter(KolProfile.platform == platform)
        
        if tag_name:
            query = query.filter(KolProfile.tag_name.contains(tag_name))

        return query.offset(skip).limit(limit).all()

    def count_profiles(self, platform: Optional[str] = None, 
                      tag_name: Optional[str] = None) -> int:
        """统计KOL资料数量"""
        query = self.db.query(KolProfile)

        if platform:
            query = query.filter(KolProfile.platform == platform)
        
        if tag_name:
            query = query.filter(KolProfile.tag_name.contains(tag_name))

        return query.count() 