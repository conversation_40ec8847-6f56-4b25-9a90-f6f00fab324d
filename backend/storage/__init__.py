"""
云存储模块

提供统一的云存储接口，支持阿里云OSS和AWS S3。
通过环境变量 CLOUD_STORAGE_PROVIDER 配置使用哪种存储服务。

主要接口：
- upload_to_oss: 上传文件（兼容性接口）
- get_download_url: 获取预签名下载链接
- delete_file: 删除文件
- file_exists: 检查文件是否存在
- get_file_info: 获取文件信息
- get_storage_provider: 获取存储提供商实例
- CloudStorageError: 云存储异常类

使用示例：
    from storage import upload_to_oss, get_download_url, CloudStorageError
    
    try:
        # 上传文件
        url = upload_to_oss(file_obj, "path/to/file.txt", "text/plain")
        
        # 获取下载链接
        download_url = get_download_url("path/to/file.txt", expires_in=3600)
        
    except CloudStorageError as e:
        print(f"云存储操作失败: {e}")
"""

from .service import (
    upload_to_oss,
    get_download_url,
    delete_file,
    file_exists,
    get_file_info,
    get_storage_provider,
    upload_file_with_unique_name,
    batch_delete_files,
    get_files_info,
)
from .exceptions import CloudStorageError
from .providers.base import CloudStorageProvider

__all__ = [
    'upload_to_oss',
    'get_download_url', 
    'delete_file',
    'file_exists',
    'get_file_info',
    'get_storage_provider',
    'upload_file_with_unique_name',
    'batch_delete_files',
    'get_files_info',
    'CloudStorageError',
    'CloudStorageProvider',
] 