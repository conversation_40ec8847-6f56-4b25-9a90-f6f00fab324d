# Storage 模块说明

## 简介

Storage 模块提供统一的云存储接口，支持阿里云OSS和AWS S3，可通过配置灵活切换存储服务。

## 目录结构

```
storage/
├── __init__.py              # 模块接口导出
├── __main__.py              # 命令行入口
├── cli.py                   # 命令行工具
├── exceptions.py            # 异常定义
├── factory.py              # 提供商工厂
├── service.py              # 统一服务接口
└── providers/              # 存储提供商实现
    ├── __init__.py         # 提供商导出
    ├── base.py            # 抽象基类
    ├── oss_provider.py    # 阿里云OSS实现
    └── s3_provider.py     # AWS S3实现
```

## 配置说明

通过环境变量配置存储服务：

```bash
# 阿里云OSS配置
CLOUD_STORAGE_PROVIDER=oss
OSS_BUCKET_NAME=your-bucket-name
OSS_REGION=ap-southeast-1
OSS_ACCESS_KEY_ID=your_key_id
OSS_ACCESS_KEY_SECRET=your_key_secret

# 或AWS S3配置
CLOUD_STORAGE_PROVIDER=s3
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_key_id
AWS_SECRET_ACCESS_KEY=your_key_secret
```

## 使用方式

### 基础用法
```python
from storage import upload_to_oss, get_download_url

# 上传文件
url = upload_to_oss(file_obj, "path/to/file.txt")

# 获取下载链接
download_url = get_download_url("path/to/file.txt")
```

### 高级用法
```python
from storage import (
    delete_file,
    file_exists,
    get_file_info,
    upload_file_with_unique_name,
    batch_delete_files,
    CloudStorageError
)

try:
    # 删除文件
    success = delete_file("path/to/file.txt")
    
    # 检查文件是否存在
    exists = file_exists("path/to/file.txt")
    
    # 获取文件信息
    info = get_file_info("path/to/file.txt")
    
    # 使用唯一文件名上传
    url, object_name = upload_file_with_unique_name(
        file_obj, 
        directory="uploads", 
        file_extension=".jpg"
    )
    
    # 批量删除文件
    results = batch_delete_files(["file1.txt", "file2.txt"])
    
except CloudStorageError as e:
    print(f"操作失败: {e}")
```

### 命令行工具

命令行工具提供了便捷的文件操作方式，支持上传、下载、删除等操作。

#### 基本用法
```bash
python -m storage <本地文件路径> <远程文件路径> [操作类型] [参数]
```

#### 支持的操作类型
- `upload`: 上传文件（默认操作）
- `download_url`: 获取下载链接
- `delete`: 删除文件
- `exists`: 检查文件是否存在
- `info`: 获取文件信息

#### 使用示例

1. **上传文件**
```bash
# 基本上传
python -m storage test.jpg images/test.jpg upload

# 自动检测文件类型
python -m storage document.pdf docs/report.pdf upload
```

2. **获取下载链接**
```bash
# 默认1小时有效期
python -m storage test.jpg images/test.jpg download_url

# 指定链接有效期（秒）
python -m storage test.jpg images/test.jpg download_url 7200  # 2小时
```

3. **删除文件**
```bash
python -m storage test.jpg images/test.jpg delete
```

4. **检查文件是否存在**
```bash
python -m storage test.jpg images/test.jpg exists
```

5. **获取文件信息**
```bash
python -m storage test.jpg images/test.jpg info
```

#### 输出说明

- 上传成功：显示文件URL和内容类型
- 下载链接：显示预签名URL和过期时间
- 删除操作：显示成功/失败状态
- 存在检查：显示文件是否存在
- 文件信息：显示大小、类型、修改时间等

#### 错误处理

- 本地文件不存在：显示错误信息并退出
- 远程操作失败：显示具体错误原因
- 配置错误：提示检查环境变量设置
- 网络错误：显示连接问题详情

#### 环境变量

命令行工具会自动读取 `.env` 文件中的配置，也可以直接设置环境变量：
```bash
# 设置存储提供商
export CLOUD_STORAGE_PROVIDER=oss  # 或 s3

# 设置凭证（根据提供商选择）
export OSS_ACCESS_KEY_ID=your_key_id
export OSS_ACCESS_KEY_SECRET=your_key_secret
# 或
export AWS_ACCESS_KEY_ID=your_key_id
export AWS_SECRET_ACCESS_KEY=your_key_secret
```

## API说明

### 主要接口
- `upload_to_oss(obj, object_name, content_type)`: 上传文件
- `get_download_url(object_name, expires_in)`: 获取下载链接
- `delete_file(object_name)`: 删除文件
- `file_exists(object_name)`: 检查文件是否存在
- `get_file_info(object_name)`: 获取文件信息
- `upload_file_with_unique_name(file_obj, directory, file_extension)`: 使用唯一文件名上传
- `batch_delete_files(object_names)`: 批量删除文件
- `get_files_info(object_names)`: 批量获取文件信息

### 异常类型
- `CloudStorageError`: 基础异常类
- `CloudStorageConfigError`: 配置错误
- `CloudStorageAuthError`: 认证错误
- `CloudStorageNetworkError`: 网络错误
- `CloudStorageFileNotFoundError`: 文件不存在错误

## 依赖说明

- 阿里云OSS: `alibabacloud-oss-v2>=1.1.2`
- AWS S3: `boto3>=1.34.0`
- 其他: `python-multipart>=0.0.20` 