"""
云存储提供商抽象基类

定义云存储提供商必须实现的接口。
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any


class CloudStorageProvider(ABC):
    """云存储提供商抽象基类"""
    
    @abstractmethod
    def upload_file(self, file_obj, object_name: str, content_type: str = 'application/octet-stream') -> str:
        """
        上传文件到云存储
        
        Args:
            file_obj: 文件对象或字节数据
            object_name: 存储的对象名称/路径
            content_type: 文件内容类型
            
        Returns:
            str: 文件的公网访问URL
            
        Raises:
            CloudStorageError: 上传失败时抛出异常
        """
        pass
    
    @abstractmethod
    def get_download_url(self, object_name: str, expires_in: int = 3600) -> str:
        """
        获取文件的预签名下载URL
        
        Args:
            object_name: 存储的对象名称/路径
            expires_in: URL过期时间（秒），默认1小时
            
        Returns:
            str: 预签名的下载URL
            
        Raises:
            CloudStorageError: 获取URL失败时抛出异常
        """
        pass
    
    @abstractmethod
    def delete_file(self, object_name: str) -> bool:
        """
        删除云存储中的文件
        
        Args:
            object_name: 存储的对象名称/路径
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            CloudStorageError: 删除失败时抛出异常
        """
        pass
    
    @abstractmethod
    def file_exists(self, object_name: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            object_name: 存储的对象名称/路径
            
        Returns:
            bool: 文件是否存在
        """
        pass
    
    @abstractmethod
    def get_file_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            object_name: 存储的对象名称/路径
            
        Returns:
            Dict[str, Any]: 文件信息，包含大小、最后修改时间等，如果文件不存在返回None
        """
        pass 
