"""
AWS S3存储提供商实现

基于AWS SDK (boto3) 实现云存储接口。
"""

import os
from typing import Optional, Dict, Any

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from utils.logger import logger

from .base import CloudStorageProvider
from ..exceptions import (
    CloudStorageError,
    CloudStorageConfigError,
    CloudStorageAuthError,
    CloudStorageNetworkError,
    CloudStorageFileNotFoundError,
)


class AWSS3Provider(CloudStorageProvider):
    """AWS S3存储提供商"""
    
    def __init__(self):
        """初始化AWS S3客户端"""
        self.bucket_name = os.getenv('AWS_S3_BUCKET_NAME')
        self.region = os.getenv('AWS_REGION', 'us-east-1')
        
        if not self.bucket_name:
            raise CloudStorageConfigError("AWS S3存储桶名称未配置，请设置AWS_S3_BUCKET_NAME环境变量")
        
        # 初始化S3客户端
        try:
            session = boto3.Session(
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                region_name=self.region
            )
            self.s3_client = session.client('s3')
            self.s3_resource = session.resource('s3')
            
            # 验证凭证
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"AWS S3客户端初始化成功，区域: {self.region}, 存储桶: {self.bucket_name}")
            
        except NoCredentialsError:
            logger.error("AWS凭证未配置")
            raise CloudStorageAuthError("AWS凭证未配置，请设置AWS_ACCESS_KEY_ID和AWS_SECRET_ACCESS_KEY环境变量")
        except ClientError as e:
            logger.error(f"AWS S3客户端初始化失败: {e}")
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code in ['403', 'Forbidden']:
                raise CloudStorageAuthError(f"AWS S3权限不足: {e}")
            elif error_code in ['404', 'NoSuchBucket']:
                raise CloudStorageConfigError(f"AWS S3存储桶不存在: {e}")
            else:
                raise CloudStorageConfigError(f"AWS S3初始化失败: {e}")
        except Exception as e:
            logger.error(f"AWS S3客户端初始化失败: {e}")
            raise CloudStorageConfigError(f"AWS S3初始化失败: {e}")
    
    def upload_file(self, file_obj, object_name: str, content_type: str = 'application/octet-stream') -> str:
        """上传文件到AWS S3"""
        try:
            # 处理不同类型的文件对象
            if isinstance(file_obj, str):
                # 如果file_obj是文件路径字符串，读取文件内容
                with open(file_obj, 'rb') as f:
                    file_data = f.read()
            elif hasattr(file_obj, 'file') and hasattr(file_obj, 'read'):
                # 处理 FastAPI UploadFile 对象
                file_data = file_obj.file.read()
            elif hasattr(file_obj, 'read'):
                # 如果是普通文件对象，读取内容
                file_data = file_obj.read()
            else:
                # 其他情况，直接使用
                file_data = file_obj
            
            # 验证文件大小（50MB）
            max_size = 50 * 1024 * 1024
            if len(file_data) > max_size:
                raise CloudStorageError(f"文件大小不能超过50MB，当前文件大小: {len(file_data)} 字节")
            
            # 上传文件
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=object_name,
                Body=file_data,
                ContentType=content_type
            )
            
            logger.info(f'文件上传成功到AWS S3，对象名: {object_name}')
            
            # 返回公网访问链接
            public_url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{object_name}"
            return public_url
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            error_msg = f"AWS S3文件上传失败: {e}"
            logger.error(error_msg)
            
            if error_code in ['403', 'Forbidden']:
                raise CloudStorageAuthError(f"权限不足: {e}")
            elif error_code in ['NoSuchBucket']:
                raise CloudStorageConfigError(f"存储桶不存在: {e}")
            else:
                raise CloudStorageError(f"文件上传失败: {e}")
        except Exception as e:
            error_msg = f"AWS S3文件上传失败: {e}"
            logger.error(error_msg)
            raise CloudStorageError(f"文件上传失败: {e}")
    
    def get_download_url(self, object_name: str, expires_in: int = 3600) -> str:
        """获取AWS S3文件的预签名下载URL"""
        try:
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': object_name},
                ExpiresIn=expires_in
            )
            
            logger.info(f'获取AWS S3预签名URL成功: {object_name}，过期时间: {expires_in}秒')
            return presigned_url
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            error_msg = f"获取AWS S3预签名URL失败: {e}"
            logger.error(error_msg)
            
            if error_code in ['403', 'Forbidden']:
                raise CloudStorageAuthError(f"权限不足: {e}")
            elif error_code in ['404', 'NoSuchKey']:
                raise CloudStorageFileNotFoundError(f"文件不存在: {e}")
            else:
                raise CloudStorageError(f"获取下载链接失败: {e}")
        except Exception as e:
            error_msg = f"获取AWS S3预签名URL失败: {e}"
            logger.error(error_msg)
            raise CloudStorageError(f"获取下载链接失败: {e}")
    
    def delete_file(self, object_name: str) -> bool:
        """删除AWS S3中的文件"""
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=object_name
            )
            
            logger.info(f'AWS S3文件删除成功: {object_name}')
            return True
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            error_msg = f"AWS S3文件删除失败: {e}"
            logger.error(error_msg)
            
            if error_code in ['403', 'Forbidden']:
                raise CloudStorageAuthError(f"权限不足: {e}")
            else:
                raise CloudStorageError(f"文件删除失败: {e}")
        except Exception as e:
            error_msg = f"AWS S3文件删除失败: {e}"
            logger.error(error_msg)
            raise CloudStorageError(f"文件删除失败: {e}")
    
    def file_exists(self, object_name: str) -> bool:
        """检查AWS S3中文件是否存在"""
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=object_name)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            raise
        except Exception:
            return False
    
    def get_file_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """获取AWS S3文件信息"""
        try:
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=object_name
            )
            
            return {
                'size': response['ContentLength'],
                'last_modified': response['LastModified'],
                'content_type': response.get('ContentType', 'application/octet-stream'),
                'etag': response['ETag'].strip('"'),
                'provider': 's3',
                'bucket': self.bucket_name,
                'region': self.region,
            }
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            logger.error(f"获取AWS S3文件信息失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取AWS S3文件信息失败: {e}")
            return None 