"""
阿里云OSS存储提供商实现

基于阿里云OSS SDK实现云存储接口。
"""

import os
from typing import Optional, Dict, Any

import alibabacloud_oss_v2 as oss
from utils.logger import logger

from .base import CloudStorageProvider
from ..exceptions import (
    CloudStorageError,
    CloudStorageConfigError,
    CloudStorageAuthError,
    CloudStorageNetworkError,
)


class AlibabaCloudOSSProvider(CloudStorageProvider):
    """阿里云OSS存储提供商"""
    
    def __init__(self):
        """初始化阿里云OSS客户端"""
        self.bucket_name = os.getenv('OSS_BUCKET_NAME', 'kol-hub-test')
        self.region = os.getenv('OSS_REGION', 'ap-southeast-1')
        self.endpoint = f"https://oss-{self.region}.aliyuncs.com"
        
        # 初始化OSS客户端
        try:
            credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()
            cfg = oss.config.load_default()
            cfg.credentials_provider = credentials_provider
            cfg.region = self.region
            self.client = oss.Client(cfg)
            logger.info(f"阿里云OSS客户端初始化成功，区域: {self.region}, 存储桶: {self.bucket_name}")
        except Exception as e:
            logger.error(f"阿里云OSS客户端初始化失败: {e}")
            raise CloudStorageConfigError(f"阿里云OSS初始化失败: {e}")
    
    def upload_file(self, file_obj, object_name: str, content_type: str = 'application/octet-stream') -> str:
        """上传文件到阿里云OSS"""
        try:
            result = self.client.put_object(oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=object_name,
                body=file_obj,
                content_type=content_type,
            ))
            
            logger.info(f'文件上传成功到阿里云OSS，ETag: {result.etag}，对象名: {object_name}')
            
            # 返回公网访问链接
            public_url = f"https://{self.bucket_name}.oss-{self.region}.aliyuncs.com/{object_name}"
            return public_url
            
        except Exception as e:
            error_msg = f"阿里云OSS文件上传失败: {e}"
            logger.error(error_msg)
            
            # 根据错误类型抛出相应异常
            if "auth" in str(e).lower() or "credential" in str(e).lower():
                raise CloudStorageAuthError(f"认证失败: {e}")
            elif "network" in str(e).lower() or "timeout" in str(e).lower():
                raise CloudStorageNetworkError(f"网络错误: {e}")
            else:
                raise CloudStorageError(f"文件上传失败: {e}")
    
    def get_download_url(self, object_name: str, expires_in: int = 3600) -> str:
        """获取阿里云OSS文件的预签名下载URL"""
        try:
            from datetime import timedelta
            
            # 使用timedelta对象表示过期时间间隔
            expires_delta = timedelta(seconds=expires_in)
            
            pre_result = self.client.presign(
                oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key=object_name,
                ),
                expires=expires_delta
            )
            
            logger.info(f'获取阿里云OSS预签名URL成功: {object_name}，过期时间: {pre_result.expiration}')
            return pre_result.url
            
        except Exception as e:
            error_msg = f"获取阿里云OSS预签名URL失败: {e}"
            logger.error(error_msg)
            
            if "auth" in str(e).lower():
                raise CloudStorageAuthError(f"认证失败: {e}")
            elif "not found" in str(e).lower() or "404" in str(e):
                raise CloudStorageError(f"文件不存在: {e}")
            else:
                raise CloudStorageError(f"获取下载链接失败: {e}")
    
    def delete_file(self, object_name: str) -> bool:
        """删除阿里云OSS中的文件"""
        try:
            self.client.delete_object(oss.DeleteObjectRequest(
                bucket=self.bucket_name,
                key=object_name,
            ))
            
            logger.info(f'阿里云OSS文件删除成功: {object_name}')
            return True
            
        except Exception as e:
            error_msg = f"阿里云OSS文件删除失败: {e}"
            logger.error(error_msg)
            
            if "auth" in str(e).lower():
                raise CloudStorageAuthError(f"认证失败: {e}")
            else:
                raise CloudStorageError(f"文件删除失败: {e}")
    
    def file_exists(self, object_name: str) -> bool:
        """检查阿里云OSS中文件是否存在"""
        try:
            self.client.head_object(oss.HeadObjectRequest(
                bucket=self.bucket_name,
                key=object_name,
            ))
            return True
        except Exception:
            return False
    
    def get_file_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """获取阿里云OSS文件信息"""
        try:
            result = self.client.head_object(oss.HeadObjectRequest(
                bucket=self.bucket_name,
                key=object_name,
            ))
            
            return {
                'size': result.content_length,
                'last_modified': result.last_modified,
                'content_type': result.content_type,
                'etag': result.etag,
                'provider': 'oss',
                'bucket': self.bucket_name,
                'region': self.region,
            }
        except Exception as e:
            logger.error(f"获取阿里云OSS文件信息失败: {e}")
            return None 