#!/usr/bin/env python3
"""
云存储命令行工具

提供命令行接口来测试和操作云存储功能。

用法:
    python -m storage.cli <local_file_path> <remote_filename> [action]
    
支持的操作:
    upload      - 上传文件（默认）
    download_url - 获取下载链接
    delete      - 删除文件
    exists      - 检查文件是否存在
    info        - 获取文件信息
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from storage import (
    get_storage_provider,
    upload_to_oss,
    get_download_url,
    delete_file,
    file_exists,
    get_file_info,
    CloudStorageError,
)


def print_usage():
    """打印使用说明"""
    print('用法: python -m storage.cli <local_file_path> <remote_filename> [action]')
    print('支持的操作:')
    print('  upload      - 上传文件（默认）')
    print('  download_url - 获取下载链接')
    print('  delete      - 删除文件')
    print('  exists      - 检查文件是否存在')
    print('  info        - 获取文件信息')
    print('')
    print('示例:')
    print('  python -m storage.cli test.txt remote/test.txt upload')
    print('  python -m storage.cli test.txt remote/test.txt download_url')
    print('  python -m storage.cli test.txt remote/test.txt delete')


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()
    
    if len(sys.argv) < 3:
        print_usage()
        sys.exit(1)
    
    local_file = sys.argv[1]
    remote_file = sys.argv[2]
    action = sys.argv[3] if len(sys.argv) > 3 else 'upload'
    
    try:
        provider = get_storage_provider()
        print(f"使用存储提供商: {provider.__class__.__name__}")
        
        if action == 'upload':
            # 上传文件
            if not os.path.exists(local_file):
                print(f"错误: 本地文件 {local_file} 不存在")
                sys.exit(1)
                
            print(f"正在上传文件: {local_file} -> {remote_file}")
            
            with open(local_file, 'rb') as f:
                # 尝试检测文件类型
                import mimetypes
                content_type, _ = mimetypes.guess_type(local_file)
                if not content_type:
                    content_type = 'application/octet-stream'
                
                result = upload_to_oss(f, remote_file, content_type)
                print(f'✅ 上传成功!')
                print(f'📁 文件URL: {result}')
                print(f'🏷️  内容类型: {content_type}')
        
        elif action == 'download_url':
            # 获取下载链接
            print(f"正在获取下载链接: {remote_file}")
            
            expires_in = 3600  # 默认1小时
            if len(sys.argv) > 4:
                try:
                    expires_in = int(sys.argv[4])
                except ValueError:
                    print("警告: 过期时间格式错误，使用默认值3600秒")
            
            result = get_download_url(remote_file, expires_in)
            print(f'✅ 获取成功!')
            print(f'🔗 下载链接: {result}')
            print(f'⏰ 过期时间: {expires_in}秒')
        
        elif action == 'delete':
            # 删除文件
            print(f"正在删除文件: {remote_file}")
            
            result = delete_file(remote_file)
            if result:
                print(f'✅ 删除成功!')
            else:
                print(f'❌ 删除失败')
                sys.exit(1)
        
        elif action == 'exists':
            # 检查文件是否存在
            print(f"正在检查文件: {remote_file}")
            
            result = file_exists(remote_file)
            if result:
                print(f'✅ 文件存在')
            else:
                print(f'❌ 文件不存在')
        
        elif action == 'info':
            # 获取文件信息
            print(f"正在获取文件信息: {remote_file}")
            
            result = get_file_info(remote_file)
            if result:
                print(f'✅ 文件信息:')
                print(f'📏 大小: {result.get("size", "未知")} 字节')
                print(f'🏷️  类型: {result.get("content_type", "未知")}')
                print(f'📅 修改时间: {result.get("last_modified", "未知")}')
                print(f'🏷️  ETag: {result.get("etag", "未知")}')
                print(f'☁️  提供商: {result.get("provider", "未知")}')
                print(f'🗂️  存储桶: {result.get("bucket", "未知")}')
                print(f'🌍 区域: {result.get("region", "未知")}')
            else:
                print(f'❌ 无法获取文件信息，文件可能不存在')
                sys.exit(1)
        
        else:
            print(f'❌ 不支持的操作: {action}')
            print_usage()
            sys.exit(1)
            
    except CloudStorageError as e:
        print(f'❌ 云存储操作失败: {e}')
        sys.exit(1)
    except FileNotFoundError:
        print(f'❌ 本地文件不存在: {local_file}')
        sys.exit(1)
    except KeyboardInterrupt:
        print('\n⚠️  操作被用户取消')
        sys.exit(1)
    except Exception as e:
        print(f'❌ 发生错误: {e}')
        sys.exit(1)


if __name__ == '__main__':
    main() 