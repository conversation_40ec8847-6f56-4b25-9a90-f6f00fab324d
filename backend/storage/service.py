"""
云存储统一服务接口

提供统一的云存储服务接口，包括兼容性函数和新功能。
"""

from typing import Optional, Dict, Any

from .factory import get_storage_provider as _get_storage_provider
from .providers.base import CloudStorageProvider


# 兼容性接口 - 保持与原有代码的兼容性
def upload_to_oss(obj, object_name: str, content_type: str = 'text/plain') -> str:
    """
    上传文件到云存储（兼容性接口）
    
    这是为了保持与原有代码的兼容性而提供的接口。
    函数名保持不变，但实际会根据配置使用阿里云OSS或AWS S3。
    
    Args:
        obj: 文件对象或字节数据
        object_name: 存储的对象名称/路径
        content_type: 文件内容类型，默认为 'text/plain'
        
    Returns:
        str: 文件的公网访问URL
        
    Raises:
        CloudStorageError: 上传失败时抛出异常
    """
    provider = _get_storage_provider()
    return provider.upload_file(obj, object_name, content_type)


def get_download_url(object_name: str, expires_in: int = 3600) -> str:
    """
    获取文件的预签名下载URL（兼容性接口）
    
    Args:
        object_name: 存储的对象名称/路径
        expires_in: URL过期时间（秒），默认1小时
        
    Returns:
        str: 预签名的下载URL
        
    Raises:
        CloudStorageError: 获取URL失败时抛出异常
    """
    provider = _get_storage_provider()
    return provider.get_download_url(object_name, expires_in)


# 新增功能接口
def delete_file(object_name: str) -> bool:
    """
    删除云存储中的文件
    
    Args:
        object_name: 存储的对象名称/路径
        
    Returns:
        bool: 删除是否成功
        
    Raises:
        CloudStorageError: 删除失败时抛出异常
    """
    provider = _get_storage_provider()
    return provider.delete_file(object_name)


def file_exists(object_name: str) -> bool:
    """
    检查文件是否存在
    
    Args:
        object_name: 存储的对象名称/路径
        
    Returns:
        bool: 文件是否存在
    """
    provider = _get_storage_provider()
    return provider.file_exists(object_name)


def get_file_info(object_name: str) -> Optional[Dict[str, Any]]:
    """
    获取文件信息
    
    Args:
        object_name: 存储的对象名称/路径
        
    Returns:
        Dict[str, Any]: 文件信息，包含大小、最后修改时间等，如果文件不存在返回None
        
    文件信息包含以下字段：
    - size: 文件大小（字节）
    - last_modified: 最后修改时间
    - content_type: 文件内容类型
    - etag: 文件ETag
    - provider: 存储提供商（'oss' 或 's3'）
    - bucket: 存储桶名称
    - region: 区域
    """
    provider = _get_storage_provider()
    return provider.get_file_info(object_name)


# 直接访问提供商实例的接口
def get_storage_provider() -> CloudStorageProvider:
    """
    获取云存储提供商实例
    
    Returns:
        CloudStorageProvider: 云存储提供商实例
        
    使用示例:
        provider = get_storage_provider()
        url = provider.upload_file(file_obj, "path/to/file.txt")
    """
    return _get_storage_provider()


# 便捷功能函数
def upload_file_with_unique_name(file_obj, directory: str = "", 
                                file_extension: str = "", 
                                content_type: str = 'application/octet-stream') -> tuple[str, str]:
    """
    使用唯一文件名上传文件
    
    Args:
        file_obj: 文件对象或字节数据
        directory: 目录前缀，默认为空
        file_extension: 文件扩展名（包含点），如 '.jpg'
        content_type: 文件内容类型
        
    Returns:
        tuple[str, str]: (文件URL, 对象名称)
    """
    import uuid
    
    # 生成唯一文件名
    unique_id = str(uuid.uuid4())
    object_name = f"{directory.rstrip('/')}/{unique_id}{file_extension}" if directory else f"{unique_id}{file_extension}"
    
    # 上传文件
    url = upload_to_oss(file_obj, object_name, content_type)
    
    return url, object_name


def batch_delete_files(object_names: list[str]) -> Dict[str, bool]:
    """
    批量删除文件
    
    Args:
        object_names: 要删除的对象名称列表
        
    Returns:
        Dict[str, bool]: 每个文件的删除结果，键为对象名称，值为是否成功
    """
    results = {}
    provider = _get_storage_provider()
    
    for object_name in object_names:
        try:
            results[object_name] = provider.delete_file(object_name)
        except Exception:
            results[object_name] = False
    
    return results


def get_files_info(object_names: list[str]) -> Dict[str, Optional[Dict[str, Any]]]:
    """
    批量获取文件信息
    
    Args:
        object_names: 要查询的对象名称列表
        
    Returns:
        Dict[str, Optional[Dict[str, Any]]]: 每个文件的信息，键为对象名称，值为文件信息或None
    """
    results = {}
    provider = _get_storage_provider()
    
    for object_name in object_names:
        results[object_name] = provider.get_file_info(object_name)
    
    return results 
