"""
云存储提供商工厂

根据配置创建相应的云存储提供商实例。
"""

import os
from typing import Optional

from .providers import CloudStorageProvider, AlibabaCloudOSSProvider, AWSS3Provider
from .exceptions import CloudStorageConfigError


def get_cloud_storage_provider() -> CloudStorageProvider:
    """
    根据配置获取云存储提供商实例
    
    通过环境变量 CLOUD_STORAGE_PROVIDER 确定使用哪种云存储服务：
    - 'oss': 阿里云OSS
    - 's3': AWS S3
    
    Returns:
        CloudStorageProvider: 云存储提供商实例
        
    Raises:
        CloudStorageConfigError: 不支持的提供商或初始化失败
    """
    provider = os.getenv('CLOUD_STORAGE_PROVIDER', 'oss').lower()
    
    if provider == 'oss':
        return AlibabaCloudOSSProvider()
    elif provider == 's3':
        return AWSS3Provider()
    else:
        supported_providers = ['oss', 's3']
        raise CloudStorageConfigError(
            f"不支持的云存储提供商: {provider}，"
            f"支持的提供商: {', '.join(supported_providers)}"
        )


# 全局实例，延迟初始化
_storage_provider: Optional[CloudStorageProvider] = None


def get_storage_provider() -> CloudStorageProvider:
    """
    获取云存储提供商实例（单例模式）
    
    使用单例模式确保在整个应用生命周期中只创建一个提供商实例，
    提高性能并避免重复初始化。
    
    Returns:
        CloudStorageProvider: 云存储提供商实例
    """
    global _storage_provider
    if _storage_provider is None:
        _storage_provider = get_cloud_storage_provider()
    return _storage_provider


def reset_storage_provider():
    """
    重置全局存储提供商实例
    
    主要用于测试场景，强制重新创建提供商实例。
    在生产环境中通常不需要调用此函数。
    """
    global _storage_provider
    _storage_provider = None 
