from typing import Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from pydantic import BaseModel
from fastapi import HTTPException

from models.bit_profile import BitProfile
from repositories.bit_profile_repository import BitProfileRepository
from utils.logger import logger
from utils.exceptions import DatabaseException

# Pydantic Models (DTOs)
class BitProfileCreate(BaseModel):
    user_id: int
    description: Optional[str] = None
    image_urls: Optional[str] = None

class BitProfileUpdate(BaseModel):
    description: Optional[str] = None
    image_urls: Optional[str] = None

class BitProfileResponse(BaseModel):
    id: int
    user_id: int
    description: Optional[str] = None
    image_urls: Optional[str] = None
    create_time: datetime
    update_time: datetime
    
    model_config = {'from_attributes': True}

class BitProfileService:
    """BIT Profile业务逻辑层"""

    def __init__(self, db: Session):
        self.db = db
        self.bit_profile_repo = BitProfileRepository(db)

    def create_bit_profile(self, profile_data: BitProfileCreate) -> BitProfileResponse:
        """创建BIT资料"""
        try:
            # 检查用户是否已有BIT资料
            existing_profile = self.bit_profile_repo.get_by_user_id(profile_data.user_id)
            if existing_profile:
                raise HTTPException(status_code=400, detail="用户已存在BIT资料")

            profile_dict = profile_data.model_dump()
            profile = self.bit_profile_repo.create(profile_dict)
            logger.info(f"BIT profile created for user: {profile_data.user_id}")
            return BitProfileResponse.model_validate(profile)
        except Exception as e:
            logger.error(f"Failed to create BIT profile: {str(e)}")
            raise DatabaseException("创建BIT资料失败")

    def get_bit_profile_by_id(self, profile_id: int) -> Optional[BitProfileResponse]:
        """根据ID获取BIT资料"""
        profile = self.bit_profile_repo.get_by_id(profile_id)
        if not profile:
            return None
        return BitProfileResponse.model_validate(profile)

    def get_bit_profile_by_user_id(self, user_id: int) -> Optional[BitProfileResponse]:
        """根据用户ID获取BIT资料"""
        profile = self.bit_profile_repo.get_by_user_id(user_id)
        if not profile:
            return None
        return BitProfileResponse.model_validate(profile)

    def update_bit_profile(self, profile_id: int, update_data: BitProfileUpdate) -> BitProfileResponse:
        """更新BIT资料"""
        profile = self.bit_profile_repo.get_by_id(profile_id)
        if not profile:
            raise HTTPException(status_code=404, detail="BIT资料不存在")

        # 准备更新数据（只更新非空字段）
        update_dict = {}
        for field, value in update_data.model_dump(exclude_unset=True).items():
            if value is not None:
                update_dict[field] = value

        if not update_dict:
            return BitProfileResponse.model_validate(profile)

        updated_profile = self.bit_profile_repo.update(profile_id, update_dict)
        logger.info(f"BIT profile updated: {profile_id}")
        return BitProfileResponse.model_validate(updated_profile)

    def get_bit_profiles(self, skip: int = 0, limit: int = 100) -> List[BitProfileResponse]:
        """获取BIT资料列表"""
        profiles = self.bit_profile_repo.get_profiles(skip, limit)
        return [BitProfileResponse.model_validate(profile) for profile in profiles]

    def delete_bit_profile(self, profile_id: int) -> bool:
        """删除BIT资料"""
        profile = self.bit_profile_repo.get_by_id(profile_id)
        if not profile:
            raise HTTPException(status_code=404, detail="BIT资料不存在")

        success = self.bit_profile_repo.delete(profile_id)
        if success:
            logger.info(f"BIT profile deleted: {profile_id}")
        return success 