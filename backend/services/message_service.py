from typing import List, Optional
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

from models.Message import Message
from repositories.message_repository import MessageRepository
from utils.logger import logger
from utils.exceptions import MessageNotFoundException, DatabaseException

# Pydantic models (DTOs)
class MessageCreate(BaseModel):
    user_id: int
    content: str
    message_type: str = 'system'  # 默认为系统消息

class MessageResponse(BaseModel):
    id: int
    user_id: int
    content: str
    message_type: str
    is_read: bool
    create_time: datetime

    model_config = {'from_attributes': True}

class MessageService:
    """消息业务逻辑层"""

    def __init__(self, db: Session):
        self.db = db
        self.message_repo = MessageRepository(db)

    def create_message(self, message_data: MessageCreate) -> MessageResponse:
        """创建消息"""
        try:
            message_dict = message_data.model_dump()
            message = self.message_repo.create(message_dict)
            logger.info(f"Message created for user: {message_data.user_id}, type: {message_data.message_type}")
            return MessageResponse.model_validate(message)
        except Exception as e:
            logger.error(f"Failed to create message: {str(e)}")
            raise DatabaseException("创建消息失败")

    def get_user_messages(self, user_id: int, skip: int = 0, limit: int = 100) -> List[MessageResponse]:
        """获取用户消息列表"""
        messages = self.message_repo.get_messages_by_user(user_id, skip, limit)
        return [MessageResponse.model_validate(msg) for msg in messages]

    def get_unread_messages(self, user_id: int) -> List[MessageResponse]:
        """获取用户未读消息"""
        messages = self.message_repo.get_unread_messages_by_user(user_id)
        return [MessageResponse.model_validate(msg) for msg in messages]

    def mark_message_as_read(self, message_id: int) -> MessageResponse:
        """标记消息为已读"""
        message = self.message_repo.mark_as_read(message_id)
        if not message:
            raise MessageNotFoundException(message_id)

        logger.info(f"Message marked as read: {message_id}")
        return MessageResponse.model_validate(message)

    def mark_all_messages_as_read(self, user_id: int) -> dict:
        """标记用户所有消息为已读"""
        count = self.message_repo.mark_all_as_read(user_id)
        logger.info(f"Marked {count} messages as read for user: {user_id}")
        return {"success": True, "updated_count": count}

    def get_unread_count(self, user_id: int) -> int:
        """获取用户未读消息数量"""
        return self.message_repo.count_unread_messages(user_id)

# 兼容旧接口的函数（逐步废弃）
def add_message(msg: Message):
    """废弃：请使用 MessageService.create_message"""
    logger.warning("add_message function is deprecated, use MessageService.create_message instead")
    from models.db import get_db
    with get_db() as db:
        service = MessageService(db)
        message_data = MessageCreate(user_id=msg.user_id, content=msg.content)
        return service.create_message(message_data)

def get_message_by_userId(user_id: int):
    """废弃：请使用 MessageService.get_user_messages"""
    logger.warning("get_message_by_userId function is deprecated, use MessageService.get_user_messages instead")
    from models.db import get_db
    with get_db() as db:
        service = MessageService(db)
        messages = service.get_user_messages(user_id)
        return [msg.model_dump() for msg in messages]

def change_message_stauts(msg_id: int):
    """废弃：请使用 MessageService.mark_message_as_read"""
    logger.warning("change_message_stauts function is deprecated, use MessageService.mark_message_as_read instead")
    from models.db import get_db
    with get_db() as db:
        service = MessageService(db)
        service.mark_message_as_read(msg_id)
        return {"success": True}