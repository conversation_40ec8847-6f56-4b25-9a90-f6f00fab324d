import os
from typing import Optional, Literal
from datetime import datetime
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from fastapi import HTTPException, UploadFile
from sqlalchemy.exc import IntegrityError

from models.user import UserInfo
from repositories.user_repository import UserRepository
from utils.security import hash_password, verify_password
from utils.jwt import create_access_token
from utils.logger import logger
from utils.exceptions import UserNotFoundException, UserAlreadyExistsException, DatabaseException
from storage import upload_to_oss, get_download_url, CloudStorageError

class RegisterRequest(BaseModel):
    username: str
    password: str
    email: EmailStr
    user_type: str  # 新增，必填
    phone: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    wallet_address: Optional[str] = None 

class LoginRequest(BaseModel):
    username: str
    password: str
    user_type: str

class UpdateProfileRequest(BaseModel):
    id: int  # 或 username: str，建议用 id
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    wallet_address: Optional[str] = None
    # 其他可更新字段

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    user_type: str
    phone: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    status: str  # 支持 active/inactive/banned
    wallet_address: Optional[str] = None
    real_name: Optional[str] = None
    id_type: Optional[str] = None
    id_number: Optional[str] = None
    id_validity: Optional[str] = None
    is_long_term: Optional[bool] = None
    verify_status: Optional[str] = None
    verify_image_url: Optional[str] = None
    id_card_front_url: Optional[str] = None
    id_card_back_url: Optional[str] = None
    verify_remark: Optional[str] = None
    create_time: datetime
    update_time: datetime
    model_config = {'from_attributes': True}

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse

class UserVerifyRequest(BaseModel):
    real_name: str
    id_number: str

class UserVerifyResponse(BaseModel):
    real_name: Optional[str]
    id_type: Optional[str]
    id_number: Optional[str]
    id_validity: Optional[str]
    is_long_term: Optional[bool]
    verify_status: Optional[str]
    verify_image_url: Optional[str]
    id_card_front_url: Optional[str] = None
    id_card_back_url: Optional[str] = None
    verify_remark: Optional[str]

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse

class UserService:
    """用户业务逻辑层"""

    def __init__(self, db: Session):
        self.db = db
        self.user_repo = UserRepository(db)

    def register_user(self, req: RegisterRequest) -> LoginResponse:
        """用户注册"""
        try:
            # 检查用户名是否存在
            if self.user_repo.check_username_exists(req.username):
                raise UserAlreadyExistsException("用户名", req.username)

            # 检查邮箱是否存在
            if self.user_repo.check_email_exists(req.email):
                raise UserAlreadyExistsException("邮箱", req.email)

            # 创建用户数据
            user_data = {
                "username": req.username,
                "password_hash": hash_password(req.password),
                "email": req.email,
                "user_type": req.user_type,
                "phone": req.phone,
                "nickname": req.nickname,
                "avatar": req.avatar,
                "wallet_address": req.wallet_address,
                "status": "active"
            }

            # 创建用户
            user = self.user_repo.create(user_data)
            logger.info(f"User registered successfully: {user.username}")

            # 🔧 注册成功后自动生成 Token
            token = create_access_token({
                "user_id": user.id,
                "username": user.username,
                "user_type": user.user_type
            })

            logger.info(f"User registered and auto-logged in: {user.username}")

            return LoginResponse(
                access_token=token,
                token_type="bearer",
                user=UserResponse.model_validate(user)
            )

        except IntegrityError as e:
            logger.error(f"User registration failed: {str(e)}")
            if 'email' in str(e.orig):
                raise UserAlreadyExistsException("邮箱", req.email)
            else:
                raise DatabaseException("注册失败，数据约束冲突")

    def login_user(self, req: LoginRequest) -> LoginResponse:
        """用户登录"""
        user = self.user_repo.get_by_username_and_type(req.username, req.user_type)

        if not user or not verify_password(req.password, user.password_hash):
            raise HTTPException(status_code=401, detail="用户名、密码或用户类型错误")

        if user.status != 'active':
            raise HTTPException(status_code=403, detail="账户已被禁用")

        # 生成JWT
        token = create_access_token({
            "user_id": user.id,
            "username": user.username,
            "user_type": user.user_type
        })

        logger.info(f"User logged in successfully: {user.username}")

        return LoginResponse(
            access_token=token,
            token_type="bearer",
            user=UserResponse.model_validate(user)
        )

    def update_user_profile(self, req: UpdateProfileRequest) -> UserResponse:
        """更新用户资料"""
        user = self.user_repo.get_by_id(req.id)
        if not user:
            raise UserNotFoundException(req.id)

        # 准备更新数据（只更新非空字段）
        update_data = {}
        for field in ['phone', 'nickname', 'avatar', 'wallet_address']:
            value = getattr(req, field)
            if value is not None:
                update_data[field] = value

        if not update_data:
            return UserResponse.model_validate(user)

        updated_user = self.user_repo.update(req.id, update_data)
        logger.info(f"User profile updated: {user.username}")

        return UserResponse.model_validate(updated_user)

    def get_user_by_id(self, user_id: int) -> Optional[UserResponse]:
        """根据ID获取用户"""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            return None
        return UserResponse.model_validate(user)

    def verify_user(self, user_id: int, verify_status: str, verify_remark: Optional[str] = None) -> UserResponse:
        """审核用户认证"""
        user = self.user_repo.update_verify_status(user_id, verify_status, verify_remark)
        if not user:
            raise UserNotFoundException(user_id)

        logger.info(f"User verification updated: {user.username} -> {verify_status}")
        return UserResponse.model_validate(user)

    def submit_verification(self, user_id: int, real_name: str, id_type: str,
                          id_number: str, id_validity: str, is_long_term: bool,
                          id_card_front: UploadFile, id_card_back: UploadFile) -> dict:
        """提交用户认证"""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)

        # 上传身份证图片
        try:
            front_ext = os.path.splitext(id_card_front.filename)[-1] or '.jpg'
            back_ext = os.path.splitext(id_card_back.filename)[-1] or '.jpg'

            front_name = f"verify/front_{user_id}{front_ext}"
            back_name = f"verify/back_{user_id}{back_ext}"

            # 确定文件类型
            front_content_type = id_card_front.content_type or 'image/jpeg'
            back_content_type = id_card_back.content_type or 'image/jpeg'

            # 上传文件到云存储
            front_url = upload_to_oss(id_card_front.file, front_name, front_content_type)
            back_url = upload_to_oss(id_card_back.file, back_name, back_content_type)
            
            logger.info(f"用户 {user_id} 身份证图片上传成功: {front_name}, {back_name}")
            
        except CloudStorageError as e:
            logger.error(f"用户 {user_id} 身份证图片上传失败: {e}")
            raise DatabaseException(f"身份证图片上传失败: {e}")
        except Exception as e:
            logger.error(f"用户 {user_id} 身份证图片上传异常: {e}")
            raise DatabaseException(f"身份证图片上传异常: {e}")

        # 更新用户认证信息
        update_data = {
            "real_name": real_name,
            "id_type": id_type,
            "id_number": id_number,
            "id_validity": id_validity,
            "is_long_term": is_long_term,
            "id_card_front_url": front_url,
            "id_card_back_url": back_url,
            "verify_status": "pending",
            "verify_remark": None
        }

        self.user_repo.update(user_id, update_data)
        logger.info(f"User verification submitted: {user.username}")

        return {"msg": "认证信息已提交，等待审核"}

    def _extract_object_name_from_url(self, url: str) -> Optional[str]:
        """从云存储URL中提取object_name"""
        if not url:
            return None
            
        try:
            # 支持阿里云OSS和AWS S3的URL格式
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            
            # 阿里云OSS格式: https://bucket-name.oss-region.aliyuncs.com/path/to/file
            # AWS S3格式: https://bucket-name.s3.region.amazonaws.com/path/to/file
            # 或者: https://s3.region.amazonaws.com/bucket-name/path/to/file
            
            path = parsed_url.path.lstrip('/')
            
            # 如果是S3的路径包含bucket名称的格式，需要移除bucket名称
            if 's3' in parsed_url.netloc and '/' in path:
                # 检查是否是 s3.region.amazonaws.com/bucket-name/object-name 格式
                if parsed_url.netloc.startswith('s3.') and '.' in parsed_url.netloc:
                    # 这种情况下path的第一部分是bucket名称，需要移除
                    parts = path.split('/', 1)
                    if len(parts) > 1:
                        path = parts[1]
            
            return path if path else None
            
        except Exception as e:
            logger.warning(f"Failed to extract object name from URL {url}: {e}")
            return None

    def get_verification_status(self, user_id: int) -> dict:
        """获取用户认证状态"""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)

        # 为身份证照片生成下载URL
        id_card_front_url = None
        id_card_back_url = None
        
        try:
            if user.id_card_front_url:
                # 从URL中提取object_name
                object_name = self._extract_object_name_from_url(user.id_card_front_url)
                if object_name:
                    id_card_front_url = get_download_url(object_name)
                else:
                    id_card_front_url = user.id_card_front_url  # fallback到原始值
        except CloudStorageError as e:
            logger.warning(f"Failed to generate download URL for id_card_front: {e}")
            id_card_front_url = user.id_card_front_url  # fallback到原始值
            
        try:
            if user.id_card_back_url:
                # 从URL中提取object_name
                object_name = self._extract_object_name_from_url(user.id_card_back_url)
                if object_name:
                    id_card_back_url = get_download_url(object_name)
                else:
                    id_card_back_url = user.id_card_back_url  # fallback到原始值
        except CloudStorageError as e:
            logger.warning(f"Failed to generate download URL for id_card_back: {e}")
            id_card_back_url = user.id_card_back_url  # fallback到原始值

        return {
            "verify_status": user.verify_status,
            "verify_remark": user.verify_remark,
            "real_name": user.real_name,
            "id_type": user.id_type,
            "id_number": user.id_number,
            "id_validity": user.id_validity,
            "is_long_term": user.is_long_term,
            "id_card_front_url": id_card_front_url,
            "id_card_back_url": id_card_back_url
        }

class UserVerifyRequest(BaseModel):
    real_name: str
    id_number: str
    id_type: str = "id_card"
    id_validity: str
    is_long_term: bool = False

