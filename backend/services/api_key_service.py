import os
from pydantic import BaseModel
from typing import Optional
from models import UserApi<PERSON>ey
from models.db import get_db
from fastapi import HTTPException
import secrets
from datetime import datetime

class ApiKeyCreateRequest(BaseModel):
    user_id: int
    name: Optional[str] = None
    permissions: Optional[str] = None
    remark: Optional[str] = None

class ApiKeyUpdateRequest(BaseModel):
    id: int
    api_key: str
    name: Optional[str] = None
    status: Optional[int] = None
    permissions: Optional[str] = None
    remark: Optional[str] = None

class ApiKeyResponse(BaseModel):
    id: int
    user_id: int
    api_key: str
    name: Optional[str] = None
    status: int
    permissions: Optional[str]
    remark: Optional[str]
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

    model_config = {'from_attributes': True}

def create_api_key(req: ApiKeyCreateRequest):
    with get_db() as db:
        api_key = secrets.token_urlsafe(32)
        new_key = UserApiKey(
            user_id=req.user_id,
            api_key=api_key,
            name=req.name,
            permissions=req.permissions,
            remark=req.remark,
            status=1
        )
        db.add(new_key)
        db.commit()
        db.refresh(new_key)
        return ApiKeyResponse.model_validate(new_key)

def update_api_key(req: ApiKeyUpdateRequest):
    with get_db() as db:
        key = db.query(UserApiKey).filter_by(id=req.id).first()
        if not key:
            raise HTTPException(status_code=404, detail="API Key不存在")
        if req.api_key is not None:
            key.api_key = req.api_key
        if req.name is not None:
            key.name = req.name
        if req.status is not None:
            key.status = req.status
        if req.permissions is not None:
            key.permissions = req.permissions
        if req.remark is not None:
            key.remark = req.remark
        db.commit()
        db.refresh(key)
        return ApiKeyResponse.model_validate(key)

def check_api_key(api_key: str):
    with get_db() as db:
        key = db.query(UserApiKey).filter_by(api_key=api_key, status=1).first()
        if not key:
            raise HTTPException(status_code=401, detail="无效的API Key")
        return key

def get_api_keys(user_id: int):
    with get_db() as db:
        keys = db.query(UserApiKey).filter_by(user_id=user_id).filter(UserApiKey.status != 2).all()
        return [ApiKeyResponse.model_validate(k) for k in keys]