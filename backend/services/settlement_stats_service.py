from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
import json

from models.settlement import KolSettlement
from models.stats import KOLMarketingStats
from utils.logger import logger
from utils.exceptions import DatabaseException
from services.marketing_task_service import marketing_task_table


def _link_count(published_links: str) -> int:
    """
    计算发布链接数组的长度
    
    Args:
        published_links: JSON字符串，可能是数组格式或包含links字段的对象格式
        
    Returns:
        数组长度，如果解析失败返回0
    """
    if not published_links:
        return 0
    
    try:
        data = json.loads(published_links)
        
        # 如果是数组格式（旧格式）
        if isinstance(data, list):
            return len(data)
        
        # 如果是对象格式（新格式），尝试获取links字段
        elif isinstance(data, dict):
            if 'links' in data and isinstance(data['links'], list):
                return len(data['links'])
            else:
                logger.warning(f"published_links 对象格式中没有links数组: {published_links}")
                return 0
        
        else:
            logger.warning(f"published_links 格式不支持: {published_links}")
            return 0
            
    except json.JSONDecodeError as e:
        logger.warning(f"解析 published_links JSON 失败: {e}, 原始数据: {published_links}")
        return 0


class SettlementStatsService:
    """结算统计数据生成服务，系统管理后台使用，不对用户"""
    
    def __init__(self, db: Session):
        self.db = db

    def generate_daily_settlement_stats(self, target_date: str = None) -> Dict[str, Any]:
        """
        生成日度结算统计数据
        
        Args:
            target_date: 目标日期，格式为YYYY-MM-DD，默认为昨天
            
        Returns:
            生成结果统计
        """
        if not target_date:
            # 默认统计昨天
            yesterday = datetime.now() - timedelta(days=1)
            target_date = yesterday.strftime('%Y-%m-%d')
        
        logger.info(f"开始生成 {target_date} 日度结算统计数据")
        
        try:
            # 获取有统计数据的任务列表
            task_stats = self._get_tasks_with_stats_by_date(target_date)

            if not task_stats:
                logger.info(f"{target_date} 日期没有统计数据")
                return {
                    'target_date': target_date,
                    'processed_tasks': 0,
                    'generated_records': 0,
                    'message': '没有需要统计的数据'
                }
            
            generated_records = 0
            
            for task_id, kol_ids in task_stats.items():
                try:
                    # 获取任务信息
                    task = self.db.query(
                        marketing_task_table.c.id,
                        marketing_task_table.c.published_links,
                        marketing_task_table.c.creator,
                        marketing_task_table.c.base_reward,
                        marketing_task_table.c.performance_rate,
                        marketing_task_table.c.commission_rate,
                        marketing_task_table.c.reward_type
                    ).filter(marketing_task_table.c.id == task_id).first()
                    logger.info(f'task+++++++++++++: {task_id}')
                    if not task:
                        logger.warning(f"任务 {task_id} 不存在，跳过")
                        continue
                    
                    # 为每个KOL生成统计记录
                    for kol_id in kol_ids:
                        logger.info(f'kol_id+++++++++++++: {kol_id}')
                        if self._generate_settlement_record(task, kol_id, target_date):
                            generated_records += 1
                    
                except Exception as e:
                    logger.error(f"处理任务 {task_id} 统计失败: {e}")
                    continue
            
            result = {
                'target_date': target_date,
                'processed_tasks': len(task_stats),
                'generated_records': generated_records,
                'message': f'成功处理 {len(task_stats)} 个任务，生成 {generated_records} 条统计记录'
            }
            
            logger.info(f"{target_date} 日度结算统计完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"日度结算统计失败: {e}")
            raise DatabaseException(f"日度结算统计处理失败: {e}")
    
    def _get_tasks_with_stats_by_date(self, stat_date: str) -> Dict[int, List[int]]:
        """
        获取指定日期有统计数据的任务和KOL列表
        
        Args:
            stat_date: 统计日期，格式为YYYY-MM-DD
        
        Returns:
            Dict[task_id, List[kol_id]]
        """
        result = self.db.query(
            KOLMarketingStats.task_id,
            KOLMarketingStats.kol_id
        ).filter(
            func.date(KOLMarketingStats.stat_date) == stat_date
        ).distinct().all()
        
        # 按任务ID分组KOL列表
        task_stats = {}
        for task_id, kol_id in result:
            if task_id not in task_stats:
                task_stats[task_id] = []
            task_stats[task_id].append(kol_id)
        
        return task_stats
    
    def _generate_settlement_record(self, task, kol_id: int, settlement_date: str) -> bool:
        """
        为指定KOL和任务生成结算统计记录
        
        Args:
            task: 任务对象
            kol_id: KOL用户ID
            settlement_date: 结算日期，格式为YYYY-MM-DD
            
        Returns:
            是否成功生成记录
        """
        try:
            # 检查是否已存在记录
            existing = self.db.query(KolSettlement).filter(
                and_(
                    KolSettlement.kol_id == kol_id,
                    KolSettlement.task_id == task.id,
                    func.date(KolSettlement.settlement_date) == settlement_date
                )
            ).first()
            
            if existing:
                logger.debug(f"KOL {kol_id} 任务 {task.id} {settlement_date} 统计记录已存在")
                return False
            
            # 获取该KOL在该任务下的统计数据汇总
            stats_summary = self._get_kol_task_stats_summary(kol_id, task.id, settlement_date)
            # 取任务发布的 links 数
            stats_summary['marketing_count'] = _link_count(task.published_links)
            logger.info(f'stats info: {stats_summary}')
            if not stats_summary or stats_summary['marketing_count'] == 0:
                logger.debug(f"KOL {kol_id} 任务 {task.id} {settlement_date} 没有统计数据")
                return False
            
            # 计算结算数据
            settlement_data = self._calculate_settlement_data(task, stats_summary, settlement_date)
            settlement_data.update({
                'kol_id': kol_id,
                'bit_id': task.creator,
                'task_id': task.id
            })
            
            # 创建记录
            settlement_record = KolSettlement(**settlement_data)
            self.db.add(settlement_record)
            self.db.commit()
            
            logger.info(f"生成结算统计记录: KOL {kol_id}, 任务 {task.id}, 日期 {settlement_date}, 总金额 {settlement_data['total_fee']}")
            return True
            
        except Exception as e:
            logger.error(f"生成结算记录失败: {e}")
            self.db.rollback()
            return False
    
    def _get_kol_task_stats_summary(self, kol_id: int, task_id: int, stat_date: str) -> Dict[str, Any]:
        """
        获取KOL在指定任务和日期的统计数据汇总
        
        Args:
            kol_id: KOL用户ID
            task_id: 任务ID
            stat_date: 统计日期，格式为YYYY-MM-DD
        """
        result = self.db.query(
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_deposit'),
            func.sum(KOLMarketingStats.order_amount).label('total_amount'),
            func.count(KOLMarketingStats.id).label('marketing_count')
        ).filter(
            and_(
                KOLMarketingStats.kol_id == kol_id,
                KOLMarketingStats.task_id == task_id,
                func.date(KOLMarketingStats.stat_date) == stat_date
            )
        ).first()
        
        if not result:
            return {}
        
        return {
            'total_clicks': int(result.total_clicks) if result.total_clicks else 0,
            'total_registers': int(result.total_registers) if result.total_registers else 0,
            'total_ftt': float(result.total_ftt) if result.total_ftt else 0.0,
            'total_deposit': float(result.total_deposit) if result.total_deposit else 0.0,
            'total_amount': float(result.total_amount) if result.total_amount else 0.0,
            'marketing_count': int(result.marketing_count) if result.marketing_count else 0
        }
    
    def _calculate_settlement_data(self, task, stats: Dict[str, Any], settlement_date: str) -> Dict[str, Any]:
        """
        根据任务信息和统计数据计算结算数据
        
        Args:
            task: 任务对象
            stats: 统计数据汇总
            settlement_date: 结算日期，格式为YYYY-MM-DD
        """
        # 获取任务的费率信息
        base_reward = float(task.base_reward) if task.base_reward else 0.0
        performance_rate = float(task.performance_rate) if task.performance_rate else 0.0
        commission_rate = float(task.commission_rate) if task.commission_rate else 0.0

        # 营销次数（统计数据的条数）
        marketing_count = stats['marketing_count']
        
        # 计算基础费用
        base_total = base_reward * marketing_count
        
        # 计算效果值（可根据业务需求调整权重）
        performance_value = (
            stats['total_registers'] * 0 +      # 注册量权重: 0
            stats['total_ftt'] * 1 +            # FTT权重: 1
            stats['total_deposit'] * 0          # 入金金额权重: 0
        )
        
        # 计算效果佣金
        performance_total = performance_value * performance_rate

        # 带单佣金
        commission_value = stats['total_amount'] * 1
        commission_total = commission_value * commission_rate
        
        # 计算总费用
        total_fee = 0.0
        if task.reward_type == 'branding':
            total_fee = base_total
        elif task.reward_type == 'commission':
            total_fee = commission_total
        elif task.reward_type == 'branding_plus_conversion':
            total_fee = base_total + performance_total
        else:
            logger.error(f'任务类型错误, {task.reward_type}')
            total_fee = 0

        # 将结算日期字符串转换为date对象
        settlement_date_obj = datetime.strptime(settlement_date, '%Y-%m-%d').date()
        
        return {
            'settlement_month': settlement_date[:7],  # 从日期提取月份，如 "2024-01-15" -> "2024-01"
            'settlement_date': settlement_date_obj,
            'marketing_count': marketing_count,
            'base_reward': Decimal(str(base_reward)),
            'base_total': Decimal(str(round(base_total, 2))),
            'performance_value': Decimal(str(round(performance_value, 2))),
            'performance_rate': Decimal(str(performance_rate)),
            'performance_total': Decimal(str(round(performance_total, 2))),
            'commission_value': Decimal(str(round(commission_value, 2))),
            'commission_rate': Decimal(str(commission_rate)),
            'commission_total': Decimal(str(round(commission_total, 2))),
            'total_fee': Decimal(str(round(total_fee, 2))),
            'status': 'pending',  # 默认状态
            'payment_method': 'crypto_wallet'  # 默认支付方式
        }
    
    def get_settlement_stats_summary(self, settlement_date: str) -> Dict[str, Any]:
        """
        获取指定日期的结算统计汇总
        
        Args:
            settlement_date: 结算日期，格式为YYYY-MM-DD
        """
        try:
            settlements = self.db.query(KolSettlement).filter(
                func.date(KolSettlement.settlement_date) == settlement_date
            ).all()
            
            if not settlements:
                return {
                    'settlement_date': settlement_date,
                    'total_count': 0,
                    'total_amount': 0.0,
                    'kol_count': 0,
                    'task_count': 0
                }
            
            total_amount = sum(float(s.total_fee) for s in settlements)
            kol_ids = set(s.kol_id for s in settlements)
            task_ids = set(s.task_id for s in settlements)
            
            return {
                'settlement_date': settlement_date,
                'total_count': len(settlements),
                'total_amount': round(total_amount, 2),
                'kol_count': len(kol_ids),
                'task_count': len(task_ids)
            }
            
        except Exception as e:
            logger.error(f"获取结算统计汇总失败: {e}")
            raise DatabaseException(f"获取结算统计汇总失败: {e}")
    
    def get_settlement_stats_summary_by_month(self, settlement_month: str) -> Dict[str, Any]:
        """
        获取指定月份的结算统计汇总
        
        Args:
            settlement_month: 结算月份，格式为YYYY-MM
        """
        try:
            settlements = self.db.query(KolSettlement).filter(
                KolSettlement.settlement_month == settlement_month
            ).all()
            
            if not settlements:
                return {
                    'settlement_month': settlement_month,
                    'total_count': 0,
                    'total_amount': 0.0,
                    'kol_count': 0,
                    'task_count': 0
                }
            
            total_amount = sum(float(s.total_fee) for s in settlements)
            kol_ids = set(s.kol_id for s in settlements)
            task_ids = set(s.task_id for s in settlements)
            
            return {
                'settlement_month': settlement_month,
                'total_count': len(settlements),
                'total_amount': round(total_amount, 2),
                'kol_count': len(kol_ids),
                'task_count': len(task_ids)
            }
            
        except Exception as e:
            logger.error(f"获取月度结算统计汇总失败: {e}")
            raise DatabaseException(f"获取月度结算统计汇总失败: {e}")
    
    def manual_generate_stats(self, kol_id: int, task_id: int, settlement_date: str) -> bool:
        """
        手动生成指定KOL和任务的结算统计
        
        Args:
            kol_id: KOL用户ID
            task_id: 任务ID
            settlement_date: 结算日期，格式为YYYY-MM-DD
        """
        try:
            task = self.db.query(
                marketing_task_table.c.id,
                marketing_task_table.c.published_links,
                marketing_task_table.c.creator,
                marketing_task_table.c.base_reward,
                marketing_task_table.c.performance_rate,
                marketing_task_table.c.commission_rate,
                marketing_task_table.c.reward_type
            ).filter(marketing_task_table.c.id == task_id).first()
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            success = self._generate_settlement_record(task, kol_id, settlement_date)
            
            if success:
                logger.info(f"手动生成结算统计成功: KOL {kol_id}, 任务 {task_id}, 日期 {settlement_date}")
            
            return success
            
        except Exception as e:
            logger.error(f"手动生成结算统计失败: {e}")
            raise DatabaseException(f"手动生成结算统计失败: {e}") 