from typing import Dict, List, Optional
from datetime import datetime, timedelta
from sqlalchemy import Table, select, update, and_, or_, func, case
from sqlalchemy.engine import Result
from models import metadata, engine
from fastapi import HTTPException
from utils.logger import logger
from services.task_notification_service import TaskNotificationService
from services.kol_profile_service import KolProfileService
from dependencies import get_kol_profile_service

# 数据库表
marketing_task = Table('marketing_task', metadata, autoload_with=engine)
kol_profile = Table('kol_profile', metadata, autoload_with=engine)
kol_invitation = Table('kol_invitation', metadata, autoload_with=engine)
user_info = Table('user_info', metadata, autoload_with=engine)

class KolInvitationService:
    """KOL邀请服务 - 支持邀请和申请双机制"""
    
    # 邀请状态
    INVITATION_STATUS_PENDING = "pending"      # 待响应
    INVITATION_STATUS_ACCEPTED = "accepted"    # 已接受
    INVITATION_STATUS_REJECTED = "rejected"    # 已拒绝
    INVITATION_STATUS_EXPIRED = "expired"      # 已过期
    
    # 邀请类型
    INVITATION_TYPE_INVITATION = "invitation"  # 邀请类型
    INVITATION_TYPE_APPLICATION = "application"  # 申请类型

    @classmethod
    def get_kol_profiles_with_status(cls, bit_id: int = None) -> List[Dict]:
        """获取KOL资料列表，包含任务状态和邀请状态"""
        try:
            with engine.connect() as conn:
                # 获取所有KOL资料
                stmt = select(kol_profile)
                result: Result = conn.execute(stmt)
                kol_profiles = [dict(row._mapping) for row in result.fetchall()]
                
                # 获取所有任务
                stmt = select(marketing_task)
                result: Result = conn.execute(stmt)
                tasks = [dict(row._mapping) for row in result.fetchall()]
                
                # 获取邀请记录（如果提供了bit_id）
                invitations = []
                if bit_id:
                    stmt = select(kol_invitation).where(kol_invitation.c.bit_id == bit_id)
                    result: Result = conn.execute(stmt)
                    invitations = [dict(row._mapping) for row in result.fetchall()]

                # 为每个KOL计算状态
                for kol in kol_profiles:
                    kol['cooperation_status'] = cls._calculate_kol_status(kol['user_id'], tasks)
                    kol['current_task_count'] = cls._get_kol_active_task_count(kol['user_id'], tasks)

                    # 添加邀请状态信息
                    if bit_id:
                        kol_invitations = [inv for inv in invitations if inv['kol_id'] == kol['user_id']]
                        # 检查是否有待处理的邀请
                        pending_invitations = [inv for inv in kol_invitations if inv['status'] == cls.INVITATION_STATUS_PENDING]
                        # 检查是否有已接受的邀请
                        accepted_invitations = [inv for inv in kol_invitations if inv['status'] == cls.INVITATION_STATUS_ACCEPTED]

                        if pending_invitations:
                            kol['invitation_status'] = 'pending'
                            kol['invitation_count'] = len(pending_invitations)
                        elif accepted_invitations:
                            kol['invitation_status'] = 'accepted'
                            kol['invitation_count'] = len(accepted_invitations)
                        else:
                            kol['invitation_status'] = 'none'
                            kol['invitation_count'] = 0
                    else:
                        kol['invitation_status'] = 'none'
                        kol['invitation_count'] = 0

                return kol_profiles
                
        except Exception as e:
            logger.error(f"获取KOL资料失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取KOL资料失败")
    
    @classmethod
    def _calculate_kol_status(cls, kol_user_id: int, tasks: List[Dict]) -> str:
        """计算KOL的合作状态"""
        # 检查KOL是否有活跃任务
        active_tasks = [
            task for task in tasks 
            if task['kol_id'] == kol_user_id and 
            task['task_status'] in ['assigned', 'unapproved', 'approved']
        ]
        
        # 添加调试日志
        logger.info(f"计算KOL {kol_user_id} 状态: 找到 {len(active_tasks)} 个活跃任务")
        if active_tasks:
            for task in active_tasks:
                logger.info(f"  - 任务 {task['id']}: {task['task_name']} ({task['task_status']})")
        
        if active_tasks:
            return "繁忙"
        else:
            return "可邀请"
    
    @classmethod
    def _get_kol_active_task_count(cls, kol_user_id: int, tasks: List[Dict]) -> int:
        """获取KOL的活跃任务数量"""
        active_tasks = [
            task for task in tasks 
            if task['kol_id'] == kol_user_id and 
            task['task_status'] in ['assigned', 'unapproved', 'approved']
        ]
        return len(active_tasks)
    
    @classmethod
    def invite_kol(cls, task_id: int, kol_id: int, bit_id: int, message: Optional[str] = None, channel_code: str = None) -> Dict:
        """邀请KOL接受任务"""
        try:
            with engine.connect() as conn:
                # 检查任务是否存在且状态为已发布
                stmt = select(marketing_task).where(marketing_task.c.id == task_id)
                result: Result = conn.execute(stmt)
                task = result.first()
                
                if not task:
                    raise HTTPException(status_code=404, detail="任务不存在")
                
                task_dict = dict(task._mapping)
                if task_dict['task_status'] != 'published':
                    raise HTTPException(status_code=400, detail="只能邀请KOL接受已发布状态的任务")
                
                # 检查任务是否由该Bit创建
                if task_dict['creator'] != bit_id:
                    raise HTTPException(status_code=403, detail="只能邀请参与自己创建的任务")
                
                # 验证渠道码
                if not channel_code or channel_code.strip() == '':
                    raise HTTPException(status_code=400, detail="渠道码不能为空")
                
                # 检查KOL是否已有该任务的邀请
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.task_id == task_id,
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.status == cls.INVITATION_STATUS_PENDING
                    )
                )
                result: Result = conn.execute(stmt)
                existing_invitation = result.first()
                
                if existing_invitation:
                    raise HTTPException(status_code=400, detail="该KOL已收到此任务的邀请")
                
                # 更新marketing_task表的kol_id字段，保持任务状态为已发布
                stmt = update(marketing_task).where(
                    marketing_task.c.id == task_id
                ).values(
                    kol_id=kol_id,
                    update_time=datetime.now()
                )
                conn.execute(stmt)
                
                # 创建邀请记录
                invitation_data = {
                    'task_id': task_id,
                    'kol_id': kol_id,  # 这里是KOL的user_id
                    'bit_id': bit_id,
                    'status': cls.INVITATION_STATUS_PENDING,
                    'message': message,
                    'invitation_type': cls.INVITATION_TYPE_INVITATION,  # 邀请类型
                    'channel_code': channel_code,  # 🔧 修改：将渠道码保存到kol_invitation表
                    'create_time': datetime.now(),
                    'expire_time': datetime.now() + timedelta(hours=24)  # 24小时后过期
                }
                
                stmt = kol_invitation.insert().values(**invitation_data)
                result = conn.execute(stmt)
                invitation_id = result.inserted_primary_key[0]
                conn.commit()
                
                # 查询Bit用户名
                stmt = select(user_info).where(user_info.c.id == bit_id)
                result: Result = conn.execute(stmt)
                bit_user = result.first()
                bit_username = bit_user.username if bit_user else f"用户#{bit_id}"

                # 发送通知给KOL
                notification_content = f"🎯 您收到了来自{bit_username}的任务邀请！\n\n任务名称：{task_dict['task_name']}\n任务类型：{task_dict['task_type']}\n基础奖励：${task_dict['base_reward']}\n\n请及时查看并响应邀请。"
                if message:
                    notification_content += f"\n\n{bit_username}留言：{message}"
                
                TaskNotificationService._send_notification(
                    kol_id, 
                    notification_content, 
                    "invitation", 
                    task_dict
                )
                
                logger.info(f"KOL邀请已创建: task_id={task_id}, kol_id={kol_id}, invitation_id={invitation_id}")
                
                return {
                    "success": True,
                    "invitation_id": invitation_id,
                    "task_id": task_id,
                    "kol_id": kol_id,
                    "status": cls.INVITATION_STATUS_PENDING,
                    "message": "邀请已发送，任务已分配给该KOL，等待KOL响应"
                }
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"邀请KOL失败: {str(e)}")
            raise HTTPException(status_code=500, detail="邀请KOL失败")
    
    @classmethod
    def accept_invitation(cls, invitation_id: int, kol_id: int, channel_code: Optional[str] = None, remark: Optional[str] = None) -> Dict:
        """KOL接受邀请"""
        try:
            with engine.connect() as conn:
                # 检查邀请是否存在且有效
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.id == invitation_id,
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.status == cls.INVITATION_STATUS_PENDING
                    )
                )
                result: Result = conn.execute(stmt)
                invitation = result.first()
                
                if not invitation:
                    raise HTTPException(status_code=404, detail="邀请不存在或已处理")
                
                invitation_dict = dict(invitation._mapping)
                
                # 检查邀请是否过期
                if invitation_dict['expire_time'] < datetime.now():
                    # 更新邀请状态为过期
                    stmt = update(kol_invitation).where(
                        kol_invitation.c.id == invitation_id
                    ).values(status=cls.INVITATION_STATUS_EXPIRED)
                    conn.execute(stmt)
                    conn.commit()
                    raise HTTPException(status_code=400, detail="邀请已过期")
                
                # 获取任务信息
                stmt = select(marketing_task).where(marketing_task.c.id == invitation_dict['task_id'])
                result: Result = conn.execute(stmt)
                task = result.first()
                
                if not task:
                    raise HTTPException(status_code=404, detail="任务不存在")
                
                task_dict = dict(task._mapping)
                
                # 检查任务状态
                if task_dict['task_status'] != 'published':
                    raise HTTPException(status_code=400, detail="任务状态已变更，无法接受邀请")
                
                # 更新邀请状态为已接受
                stmt = update(kol_invitation).where(
                    kol_invitation.c.id == invitation_id
                ).values(
                    status=cls.INVITATION_STATUS_ACCEPTED,
                    response_time=datetime.now()
                )
                conn.execute(stmt)
                
                # 更新任务状态为已分配
                update_data = {
                    'kol_id': kol_id,
                    'task_status': 'assigned',
                    'assigned_time': datetime.now(),
                    'update_time': datetime.now()
                }
                
                # 如果提供了备注，则保存到review_feedback字段
                if remark:
                    update_data['review_feedback'] = f"KOL接受邀请备注: {remark}"
                
                stmt = update(marketing_task).where(
                    marketing_task.c.id == invitation_dict['task_id']
                ).values(**update_data)
                conn.execute(stmt)
                
                # 🔧 修改：将渠道码保存到kol_invitation表中，而不是marketing_task表
                if channel_code:
                    stmt = update(kol_invitation).where(
                        kol_invitation.c.id == invitation_id
                    ).values(
                        channel_code=channel_code,
                        update_time=datetime.now()
                    )
                    conn.execute(stmt)
                
                conn.commit()
                
                # 获取KOL信息
                kol_info = cls._get_kol_info(kol_id)
                kol_display_name = kol_info.get('platform_username', f'KOL ID: {kol_id}') if kol_info else f'KOL ID: {kol_id}'
                
                # 发送通知给Bit
                notification_content = f"✅ KOL已接受您的任务邀请！\n\n任务名称：{task_dict['task_name']}\nKOL：{kol_display_name}"
                if channel_code:
                    notification_content += f"\n渠道码：{channel_code}"
                notification_content += "\n\n任务状态已更新为已分配，KOL可以开始执行任务了。"
                
                TaskNotificationService._send_notification(
                    invitation_dict['bit_id'], 
                    notification_content, 
                    "invitation_accepted", 
                    task_dict
                )
                
                logger.info(f"KOL已接受邀请: invitation_id={invitation_id}, task_id={invitation_dict['task_id']}, channel_code={channel_code}")
                
                return {
                    "success": True,
                    "invitation_id": invitation_id,
                    "task_id": invitation_dict['task_id'],
                    "status": cls.INVITATION_STATUS_ACCEPTED,
                    "channel_code": channel_code,
                    "remark": remark,
                    "message": "邀请已接受，任务状态已更新为已分配"
                }
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"接受邀请失败: {str(e)}")
            raise HTTPException(status_code=500, detail="接受邀请失败")
    
    @classmethod
    def reject_invitation(cls, invitation_id: int, kol_id: int, reason: Optional[str] = None) -> Dict:
        """KOL拒绝邀请"""
        try:
            with engine.connect() as conn:
                # 检查邀请是否存在且有效
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.id == invitation_id,
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.status == cls.INVITATION_STATUS_PENDING
                    )
                )
                result: Result = conn.execute(stmt)
                invitation = result.first()
                
                if not invitation:
                    raise HTTPException(status_code=404, detail="邀请不存在或已处理")
                
                invitation_dict = dict(invitation._mapping)
                
                # 获取任务信息
                stmt = select(marketing_task).where(marketing_task.c.id == invitation_dict['task_id'])
                result: Result = conn.execute(stmt)
                task = result.first()
                task_dict = dict(task._mapping) if task else {}
                
                # 更新邀请状态为已拒绝
                stmt = update(kol_invitation).where(
                    kol_invitation.c.id == invitation_id
                ).values(
                    status=cls.INVITATION_STATUS_REJECTED,
                    response_time=datetime.now(),
                    reject_reason=reason
                )
                conn.execute(stmt)
                
                # 清空marketing_task表的kol_id字段，恢复任务为可邀请状态
                if task:
                    stmt = update(marketing_task).where(
                        marketing_task.c.id == invitation_dict['task_id']
                    ).values(
                        kol_id=None,  # 清空kol_id，表示任务未分配
                        update_time=datetime.now()
                    )
                    conn.execute(stmt)
                
                conn.commit()
                
                # 获取KOL信息
                kol_info = cls._get_kol_info(kol_id)
                kol_display_name = kol_info.get('platform_username', f'KOL ID: {kol_id}') if kol_info else f'KOL ID: {kol_id}'
                
                # 发送通知给Bit
                notification_content = f"❌ KOL已拒绝您的任务邀请\n\n任务名称：{task_dict.get('task_name', '未知任务')}\nKOL：{kol_display_name}"
                if reason:
                    notification_content += f"\n拒绝原因：{reason}"
                notification_content += "\n\n任务状态保持为已发布，您可以邀请其他KOL。"
                
                TaskNotificationService._send_notification(
                    invitation_dict['bit_id'], 
                    notification_content, 
                    "invitation_rejected", 
                    task_dict
                )
                
                logger.info(f"KOL已拒绝邀请: invitation_id={invitation_id}, task_id={invitation_dict['task_id']}")
                
                return {
                    "success": True,
                    "invitation_id": invitation_id,
                    "task_id": invitation_dict['task_id'],
                    "status": cls.INVITATION_STATUS_REJECTED,
                    "message": "邀请已拒绝，任务已恢复为可邀请状态"
                }
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"拒绝邀请失败: {str(e)}")
            raise HTTPException(status_code=500, detail="拒绝邀请失败")
    
    @classmethod
    def get_task_invitations(cls, task_id: int) -> List[Dict]:
        """获取任务的邀请列表"""
        try:
            with engine.connect() as conn:
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.task_id == task_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_INVITATION
                    )
                )
                result: Result = conn.execute(stmt)
                invitations = [dict(row._mapping) for row in result.fetchall()]
                
                # 为每个邀请添加KOL和Bit用户信息
                for invitation in invitations:
                    # 获取KOL信息
                    stmt = select(user_info).where(user_info.c.id == invitation['kol_id'])
                    result: Result = conn.execute(stmt)
                    kol_user = result.first()
                    invitation['kol_username'] = kol_user.username if kol_user else f"用户#{invitation['kol_id']}"
                    
                    # 获取Bit用户信息
                    stmt = select(user_info).where(user_info.c.id == invitation['bit_id'])
                    result: Result = conn.execute(stmt)
                    bit_user = result.first()
                    invitation['bit_username'] = bit_user.username if bit_user else f"用户#{invitation['bit_id']}"
                
                return invitations
        except Exception as e:
            logger.error(f"获取任务邀请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取任务邀请列表失败")

    @classmethod
    def get_task_applications(cls, task_id: int) -> List[Dict]:
        """获取任务的申请列表"""
        try:
            with engine.connect() as conn:
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.task_id == task_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION
                    )
                )
                result: Result = conn.execute(stmt)
                applications = [dict(row._mapping) for row in result.fetchall()]
                
                # 为每个申请添加KOL和Bit用户信息
                for application in applications:
                    # 获取KOL信息
                    stmt = select(user_info).where(user_info.c.id == application['kol_id'])
                    result: Result = conn.execute(stmt)
                    kol_user = result.first()
                    application['kol_username'] = kol_user.username if kol_user else f"用户#{application['kol_id']}"
                    
                    # 获取Bit用户信息
                    stmt = select(user_info).where(user_info.c.id == application['bit_id'])
                    result: Result = conn.execute(stmt)
                    bit_user = result.first()
                    application['bit_username'] = bit_user.username if bit_user else f"用户#{application['bit_id']}"
                
                return applications
        except Exception as e:
            logger.error(f"获取任务申请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取任务申请列表失败")
    
    @classmethod
    def get_kol_invitations(cls, kol_id: int) -> List[Dict]:
        """获取KOL的邀请列表，包含Bit用户名信息"""
        try:
            with engine.connect() as conn:
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_INVITATION,  # 只返回邀请类型
                        or_(
                            kol_invitation.c.status == cls.INVITATION_STATUS_PENDING,
                            kol_invitation.c.status == cls.INVITATION_STATUS_ACCEPTED
                        )
                    )
                ).order_by(kol_invitation.c.create_time.desc())
                result: Result = conn.execute(stmt)
                invitations = [dict(row._mapping) for row in result.fetchall()]

                # 为每个邀请添加Bit用户名信息
                for invitation in invitations:
                    bit_id = invitation['bit_id']
                    stmt = select(user_info).where(user_info.c.id == bit_id)
                    result: Result = conn.execute(stmt)
                    bit_user = result.first()
                    invitation['bit_username'] = bit_user.username if bit_user else f"用户#{bit_id}"

                return invitations
        except Exception as e:
            logger.error(f"获取KOL邀请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取KOL邀请列表失败")
    
    @classmethod
    def get_kol_all_tasks(cls, kol_id: int) -> List[Dict]:
        """获取KOL的所有相关任务数据（统一查询）"""
        try:
            with engine.connect() as conn:
                # 统一查询：JOIN kol_invitation 和 marketing_task 表
                stmt = select(
                    # 邀请信息
                    kol_invitation.c.id.label('invitation_id'),
                    kol_invitation.c.status.label('invitation_status'),
                    kol_invitation.c.invitation_type.label('source'),
                    kol_invitation.c.application_reason,
                    kol_invitation.c.message,
                    kol_invitation.c.create_time.label('invitation_time'),
                    kol_invitation.c.expire_time,
                    kol_invitation.c.response_time,
                    kol_invitation.c.reject_reason,
                    
                    # 任务基本信息
                    marketing_task.c.id.label('task_id'),
                    marketing_task.c.task_name,
                    marketing_task.c.task_type,
                    marketing_task.c.task_status,
                    marketing_task.c.description,
                    marketing_task.c.start_date,
                    marketing_task.c.end_date,
                    marketing_task.c.channel_code,
                    marketing_task.c.base_reward,
                    marketing_task.c.performance_rate,
                    marketing_task.c.reward_type,
                    marketing_task.c.commission_rate,
                    marketing_task.c.conversion_reward_per_ftt,
                    marketing_task.c.enable_conversion_reward,
                    marketing_task.c.official_materials,
                    marketing_task.c.review_feedback,
                    marketing_task.c.published_links,
                    marketing_task.c.draft_content,
                    marketing_task.c.draft_submit_time,
                    marketing_task.c.draft_reviewed_at,
                    marketing_task.c.kol_id.label('assigned_kol_id'),
                    marketing_task.c.create_time,
                    marketing_task.c.update_time,  # 🔧 添加 update_time 字段

                    # 项目方信息
                    user_info.c.nickname.label('bit_name'),
                    user_info.c.avatar.label('bit_avatar')

                ).select_from(
                    kol_invitation
                    .join(marketing_task, kol_invitation.c.task_id == marketing_task.c.id)
                    .join(user_info, kol_invitation.c.bit_id == user_info.c.id)
                ).where(
                    kol_invitation.c.kol_id == kol_id
                ).order_by(
                    # 🔧 修复：按 marketing_task 的 update_time 倒序排序，更新时间最近的排在最前面
                    marketing_task.c.update_time.desc()
                )
                
                result: Result = conn.execute(stmt)
                tasks = [dict(row._mapping) for row in result.fetchall()]
                
                logger.info(f"获取KOL {kol_id} 的任务数据: {len(tasks)} 条记录")
                return tasks
                
        except Exception as e:
            logger.error(f"获取KOL所有任务数据失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取任务数据失败")

    @classmethod
    def get_kol_all_tasks_paginated(cls, kol_id: int, page: int = 1, size: int = 20) -> Dict:
        """获取KOL的所有相关任务数据（分页版本）"""
        try:
            with engine.connect() as conn:
                # 构建基础查询
                base_stmt = select(
                    # 邀请信息
                    kol_invitation.c.id.label('invitation_id'),
                    kol_invitation.c.status.label('invitation_status'),
                    kol_invitation.c.invitation_type.label('source'),
                    kol_invitation.c.application_reason,
                    kol_invitation.c.message,
                    kol_invitation.c.create_time.label('invitation_time'),
                    kol_invitation.c.expire_time,
                    kol_invitation.c.response_time,
                    kol_invitation.c.reject_reason,

                    # 任务基本信息
                    marketing_task.c.id.label('task_id'),
                    marketing_task.c.task_name,
                    marketing_task.c.task_type,
                    marketing_task.c.task_status,
                    marketing_task.c.description,
                    marketing_task.c.start_date,
                    marketing_task.c.end_date,
                    marketing_task.c.channel_code,
                    marketing_task.c.base_reward,
                    marketing_task.c.performance_rate,
                    marketing_task.c.reward_type,
                    marketing_task.c.commission_rate,
                    marketing_task.c.conversion_reward_per_ftt,
                    marketing_task.c.enable_conversion_reward,
                    marketing_task.c.official_materials,
                    marketing_task.c.review_feedback,
                    marketing_task.c.published_links,
                    marketing_task.c.draft_content,
                    marketing_task.c.draft_submit_time,
                    marketing_task.c.draft_reviewed_at,
                    marketing_task.c.kol_id.label('assigned_kol_id'),
                    marketing_task.c.create_time,
                    marketing_task.c.update_time,  # 🔧 添加 update_time 字段

                    # 项目方信息
                    user_info.c.nickname.label('bit_name'),
                    user_info.c.avatar.label('bit_avatar')

                ).select_from(
                    kol_invitation
                    .join(marketing_task, kol_invitation.c.task_id == marketing_task.c.id)
                    .join(user_info, kol_invitation.c.bit_id == user_info.c.id)
                ).where(
                    kol_invitation.c.kol_id == kol_id
                ).order_by(
                    # 🔧 修复：按 marketing_task 的 update_time 倒序排序，更新时间最近的排在最前面
                    marketing_task.c.update_time.desc()
                )

                # 获取总数
                count_stmt = select(func.count(kol_invitation.c.id)).select_from(
                    kol_invitation
                    .join(marketing_task, kol_invitation.c.task_id == marketing_task.c.id)
                ).where(
                    kol_invitation.c.kol_id == kol_id
                )

                total_result = conn.execute(count_stmt)
                total = total_result.scalar()

                # 分页查询
                offset = (page - 1) * size
                paginated_stmt = base_stmt.offset(offset).limit(size)

                result: Result = conn.execute(paginated_stmt)
                tasks = [dict(row._mapping) for row in result.fetchall()]

                logger.info(f"获取KOL {kol_id} 的分页任务数据: 第{page}页, 每页{size}条, 共{total}条, 返回{len(tasks)}条")

                return {
                    "data": tasks,
                    "total": total,
                    "page": page,
                    "size": size
                }

        except Exception as e:
            logger.error(f"获取KOL分页任务数据失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取任务数据失败")

    @classmethod
    def get_task_info(cls, task_id: int) -> Dict:
        """获取任务详情信息"""
        try:
            with engine.connect() as conn:
                stmt = select(marketing_task).where(marketing_task.c.id == task_id)
                result: Result = conn.execute(stmt)
                task = result.first()

                if task:
                    task_dict = dict(task._mapping)
                    return {
                        'id': task_dict.get('id'),
                        'task_name': task_dict.get('task_name'),
                        'task_type': task_dict.get('task_type'),
                        'task_status': task_dict.get('task_status'),
                        'base_reward': task_dict.get('base_reward'),
                        'performance_rate': task_dict.get('performance_rate'),
                        'description': task_dict.get('description'),
                        'start_date': task_dict.get('start_date'),
                        'end_date': task_dict.get('end_date'),
                        'create_time': task_dict.get('create_time'),
                        'official_materials': task_dict.get('official_materials')
                    }
                else:
                    return {
                        'task_name': '未知任务',
                        'task_type': 'unknown',
                        'task_status': 'unknown',
                        'base_reward': 0,
                        'performance_rate': 0,
                        'description': '',
                        'start_date': None,
                        'end_date': None,
                        'create_time': None,
                        'official_materials': None
                    }
        except Exception as e:
            logger.error(f"获取任务信息失败: {str(e)}")
            return {
                'task_name': '未知任务',
                'task_type': 'unknown',
                'task_status': 'unknown',
                'base_reward': 0,
                'performance_rate': 0,
                'description': '',
                'start_date': None,
                'end_date': None,
                'create_time': None,
                                        'official_materials': None
            }

    # === 新增申请相关方法 ===
    @classmethod
    def apply_for_task(cls, task_id: int, kol_id: int, application_reason: str) -> Dict:
        """KOL申请任务"""
        try:
            with engine.connect() as conn:
                # 检查任务是否存在且状态为已发布
                stmt = select(marketing_task).where(marketing_task.c.id == task_id)
                result: Result = conn.execute(stmt)
                task = result.first()

                if not task:
                    raise HTTPException(status_code=404, detail="任务不存在")

                task_dict = dict(task._mapping)
                if task_dict['task_status'] != 'published':
                    raise HTTPException(status_code=400, detail="只能申请已发布状态的任务")

                # 检查KOL是否已申请过该任务
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.task_id == task_id,
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION,
                        kol_invitation.c.status == cls.INVITATION_STATUS_PENDING
                    )
                )
                result: Result = conn.execute(stmt)
                existing_application = result.first()

                if existing_application:
                    raise HTTPException(status_code=400, detail="您已申请过此任务，请等待审核结果")

                # 创建申请记录
                application_data = {
                    'task_id': task_id,
                    'kol_id': kol_id,
                    'bit_id': task_dict['creator'],  # 申请发送给任务创建者
                    'status': cls.INVITATION_STATUS_PENDING,
                    'invitation_type': cls.INVITATION_TYPE_APPLICATION,
                    'application_reason': application_reason,
                    'create_time': datetime.now(),
                    'expire_time': datetime.now() + timedelta(days=7)  # 申请7天有效期
                }

                stmt = kol_invitation.insert().values(**application_data)
                result = conn.execute(stmt)
                application_id = result.inserted_primary_key[0]
                conn.commit()

                # 获取KOL信息
                kol_info = cls._get_kol_info(kol_id)
                kol_display_name = kol_info.get('platform_username', f'KOL ID: {kol_id}') if kol_info else f'KOL ID: {kol_id}'
                
                # 发送通知给项目方
                notification_content = f"📋 收到新的任务申请！\n\n任务名称：{task_dict['task_name']}\nKOL：{kol_display_name}\n\n请在任务中心查看详情。"

                TaskNotificationService._send_notification(
                    task_dict['creator'],
                    notification_content,
                    "application",
                    task_dict
                )

                logger.info(f"KOL申请已创建: task_id={task_id}, kol_id={kol_id}, application_id={application_id}")

                return {
                    "success": True,
                    "application_id": application_id,
                    "task_id": task_id,
                    "status": cls.INVITATION_STATUS_PENDING,
                    "message": "申请已提交，等待项目方审核"
                }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"申请任务失败: {str(e)}")
            raise HTTPException(status_code=500, detail="申请任务失败")

    @classmethod
    def get_kol_applications(cls, kol_id: int) -> List[Dict]:
        """获取KOL的申请列表"""
        try:
            with engine.connect() as conn:
                stmt = select(kol_invitation).where(
                    and_(
                        kol_invitation.c.kol_id == kol_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION
                    )
                ).order_by(kol_invitation.c.create_time.desc())
                result: Result = conn.execute(stmt)
                applications = [dict(row._mapping) for row in result.fetchall()]
                return applications
        except Exception as e:
            logger.error(f"获取KOL申请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取KOL申请列表失败")

    @classmethod
    def get_bit_applications(cls, bit_id: int) -> List[Dict]:
        """获取项目方收到的申请列表"""
        try:
            with engine.connect() as conn:
                # 查询申请记录并关联任务和KOL信息
                stmt = select(
                    kol_invitation,
                    marketing_task.c.task_name,
                    marketing_task.c.base_reward,
                    marketing_task.c.performance_rate,
                    kol_profile.c.platform_username.label('kol_platform_username'),
                    kol_profile.c.followers_count.label('kol_followers_count'),
                    kol_profile.c.tweet_count.label('kol_tweet_count'),
                    kol_profile.c.tag_name.label('kol_tag_name')
                ).select_from(
                    kol_invitation
                    .join(marketing_task, kol_invitation.c.task_id == marketing_task.c.id)
                    .join(kol_profile, kol_invitation.c.kol_id == kol_profile.c.user_id)
                ).where(
                    and_(
                        kol_invitation.c.bit_id == bit_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION
                    )
                ).order_by(
                    kol_invitation.c.create_time.desc()
                )

                result: Result = conn.execute(stmt)
                applications = [dict(row._mapping) for row in result.fetchall()]
                return applications

        except Exception as e:
            logger.error(f"获取申请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取申请列表失败")

    @classmethod
    def get_bit_applications_count(cls, bit_id: int) -> Dict:
        """获取项目方的申请数量统计"""
        try:
            with engine.connect() as conn:
                stmt = select(func.count(kol_invitation.c.id)).where(
                    and_(
                        kol_invitation.c.bit_id == bit_id,
                        kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION,
                        kol_invitation.c.status == cls.INVITATION_STATUS_PENDING
                    )
                )
                result = conn.execute(stmt)
                pending_count = result.scalar()

                return {
                    "pending_count": pending_count or 0
                }

        except Exception as e:
            logger.error(f"获取申请数量失败: {str(e)}")
            return {"pending_count": 0}

    @classmethod
    def approve_application(cls, task_id: int, kol_id: int, application_id: int, bit_id: int) -> Dict:
        """批准KOL申请"""
        logger.info(f"批准申请: task_id={task_id}, kol_id={kol_id}, application_id={application_id}")
        try:
            with engine.connect() as conn:
                # 1. 更新marketing_task表，设置kol_id和状态为assigned
                stmt = (
                    update(marketing_task)
                    .where(marketing_task.c.id == task_id)
                    .values(
                        kol_id=kol_id,
                        task_status='assigned',
                        update_time=datetime.now()
                    )
                )
                conn.execute(stmt)
                
                # 2. 更新kol_invitation表，将申请状态改为accepted
                stmt = (
                    update(kol_invitation)
                    .where(
                        and_(
                            kol_invitation.c.task_id == task_id,
                            kol_invitation.c.kol_id == kol_id,
                            kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION
                        )
                    )
                    .values(
                        status='accepted',
                        response_time=datetime.now()
                    )
                )
                conn.execute(stmt)
                
                conn.commit()
                
                # 3. 发送消息通知给KOL
                try:
                    from services.message_service import MessageService, MessageCreate
                    from models.db import get_db
                    
                    # 获取任务信息
                    task_info = cls.get_task_info(task_id)
                    
                    notification_content = f"🎉 恭喜！您的任务申请已被批准！\n\n任务名称: {task_info.get('task_name', '未知任务')}\n任务ID: {task_id}\n\n任务已分配给您，请及时查看任务详情并开始执行。"
                    
                    message_data = MessageCreate(
                        user_id=kol_id,
                        content=notification_content,
                        message_type='task'
                    )
                    
                    # 使用正确的数据库会话
                    with get_db() as db:
                        service = MessageService(db)
                        service.create_message(message_data)
                    
                    logger.info(f"批准通知发送成功: KOL ID={kol_id}")
                except Exception as notify_error:
                    logger.error(f"发送批准通知失败: {str(notify_error)}")
                    # 通知失败不影响主要操作
                
                logger.info("申请批准成功")
                return {"success": True, "message": "申请已批准，任务状态已更新为已分配"}
        except Exception as e:
            logger.error(f"批准申请失败: {str(e)}")
            raise HTTPException(status_code=500, detail="批准申请失败")

    @classmethod
    def reject_application(cls, task_id: int, kol_id: int, application_id: int, reject_reason: str, bit_id: int) -> Dict:
        """拒绝KOL申请"""
        logger.info(f"拒绝申请: task_id={task_id}, kol_id={kol_id}, application_id={application_id}, reason={reject_reason}")
        try:
            with engine.connect() as conn:
                # 更新kol_invitation表，将申请状态改为rejected，并记录拒绝原因
                stmt = (
                    update(kol_invitation)
                    .where(
                        and_(
                            kol_invitation.c.task_id == task_id,
                            kol_invitation.c.kol_id == kol_id,
                            kol_invitation.c.invitation_type == cls.INVITATION_TYPE_APPLICATION
                        )
                    )
                    .values(
                        status='rejected',
                        response_time=datetime.now(),
                        reject_reason=reject_reason  # 将拒绝原因存储到reject_reason字段
                    )
                )
                conn.execute(stmt)
                
                conn.commit()
                
                # 发送消息通知给KOL
                try:
                    from services.message_service import MessageService, MessageCreate
                    from models.db import get_db
                    
                    # 获取任务信息
                    task_info = cls.get_task_info(task_id)
                    
                    notification_content = f"❌ 很抱歉，您的任务申请已被拒绝。\n\n任务名称: {task_info.get('task_name', '未知任务')}\n任务ID: {task_id}\n拒绝原因: {reject_reason}\n\n您可以查看其他适合的任务，或联系项目方了解更多信息。"
                    
                    message_data = MessageCreate(
                        user_id=kol_id,
                        content=notification_content,
                        message_type='task'
                    )
                    
                    # 使用正确的数据库会话
                    with get_db() as db:
                        service = MessageService(db)
                        service.create_message(message_data)
                    
                    logger.info(f"拒绝通知发送成功: KOL ID={kol_id}")
                except Exception as notify_error:
                    logger.error(f"发送拒绝通知失败: {str(notify_error)}")
                    # 通知失败不影响主要操作
                
                logger.info("申请拒绝成功")
                return {"success": True, "message": "申请已拒绝"}
        except Exception as e:
            logger.error(f"拒绝申请失败: {str(e)}")
            raise HTTPException(status_code=500, detail="拒绝申请失败")

    @classmethod
    def update_invitation_status(cls, task_id: int, kol_id: int, status: str) -> Dict:
        """更新邀请状态"""
        logger.info(f"更新邀请状态: task_id={task_id}, kol_id={kol_id}, status={status}")
        try:
            with engine.connect() as conn:
                stmt = (
                    update(kol_invitation)
                    .where(kol_invitation.c.task_id == task_id, kol_invitation.c.kol_id == kol_id)
                    .values(status=status)
                )
                conn.execute(stmt)
                conn.commit()
                logger.info("邀请状态更新成功")
                return {"success": True, "message": "邀请状态更新成功"}
        except Exception as e:
            logger.error(f"更新邀请状态失败: {str(e)}")
            raise HTTPException(status_code=500, detail="更新邀请状态失败")  # 新增方法

    @classmethod
    def get_bit_invitations(cls, bit_id: int) -> List[Dict]:
        """获取项目方发出的邀请列表和收到的申请列表"""
        try:
            with engine.connect() as conn:
                # 查询邀请记录和申请记录，关联任务和KOL信息
                stmt = select(
                    kol_invitation,
                    marketing_task.c.task_name,
                    marketing_task.c.base_reward,
                    marketing_task.c.performance_rate,
                    kol_profile.c.platform_username.label('kol_platform_username'),
                    kol_profile.c.followers_count.label('kol_followers_count'),
                    kol_profile.c.tweet_count.label('kol_tweet_count'),
                    kol_profile.c.tag_name.label('kol_tag_name'),
                    user_info.c.username.label('kol_username')
                ).select_from(
                    kol_invitation
                    .join(marketing_task, kol_invitation.c.task_id == marketing_task.c.id)
                    .join(kol_profile, kol_invitation.c.kol_id == kol_profile.c.user_id)
                    .join(user_info, kol_invitation.c.kol_id == user_info.c.id)
                ).where(
                    kol_invitation.c.bit_id == bit_id  # 移除invitation_type限制，返回所有记录
                ).order_by(kol_invitation.c.create_time.desc())
                
                result: Result = conn.execute(stmt)
                records = []
                
                for row in result.fetchall():
                    row_dict = dict(row._mapping)
                    record_data = {
                        'id': row_dict['id'],
                        'task_id': row_dict['task_id'],
                        'kol_id': row_dict['kol_id'],
                        'bit_id': row_dict['bit_id'],
                        'status': row_dict['status'],
                        'message': row_dict['message'],
                        'invitation_type': row_dict['invitation_type'],
                        'application_reason': row_dict['application_reason'],
                        'create_time': row_dict['create_time'].isoformat() if row_dict['create_time'] else None,
                        'expire_time': row_dict['expire_time'].isoformat() if row_dict['expire_time'] else None,
                        'response_time': row_dict['response_time'].isoformat() if row_dict['response_time'] else None,
                        'reject_reason': row_dict['reject_reason'],
                        'channel_code': row_dict['channel_code'],
                        # 任务信息
                        'task_name': row_dict['task_name'],
                        'base_reward': float(row_dict['base_reward']) if row_dict['base_reward'] else 0,
                        'performance_rate': float(row_dict['performance_rate']) if row_dict['performance_rate'] else 0,
                        # KOL信息
                        'kol_username': row_dict['kol_username'],
                        'kol_platform_username': row_dict['kol_platform_username'],
                        'kol_followers_count': row_dict['kol_followers_count'],
                        'kol_tweet_count': row_dict['kol_tweet_count'],
                        'kol_tag_name': row_dict['kol_tag_name']
                    }
                    records.append(record_data)
                
                return records
                
        except Exception as e:
            logger.error(f"获取项目方邀请和申请列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取项目方邀请和申请列表失败")

    @classmethod
    def _get_kol_info(cls, kol_id: int) -> Optional[Dict]:
        """获取KOL信息"""
        try:
            with engine.connect() as conn:
                stmt = select(
                    kol_profile.c.platform_username,
                    kol_profile.c.platform_name,
                    kol_profile.c.followers_count
                ).where(kol_profile.c.user_id == kol_id)
                
                result: Result = conn.execute(stmt)
                kol_info = result.first()
                
                if kol_info:
                    return dict(kol_info._mapping)
                return None
        except Exception as e:
            logger.error(f"获取KOL信息失败: {str(e)}")
            return None
