import os
from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException, UploadFile
from sqlalchemy.exc import IntegrityError

from models.company_certificate import (
    CompanyCertificateVerify, 
    CompanyCertificateSubmitRequest,
    CompanyCertificateResponse,
    CompanyCertificateReviewRequest
)
from utils.logger import logger
from utils.exceptions import DatabaseException
from storage import upload_to_oss, get_download_url, CloudStorageError


class CompanyCertificateService:
    """公司资质认证业务逻辑层"""

    def __init__(self, db: Session):
        self.db = db

    def submit_certificate_verification(
        self, 
        user_id: int, 
        company_name: str, 
        certificate_type: str,
        certificate_file: UploadFile
    ) -> dict:
        """提交公司资质认证"""
        try:
            # 检查是否已存在认证记录
            existing = self.db.query(CompanyCertificateVerify).filter(
                CompanyCertificateVerify.user_id == user_id
            ).first()
            
            # 如果存在记录，检查状态
            if existing:
                if existing.verify_status == 'pending':
                    raise HTTPException(status_code=400, detail="已存在待审核的认证记录，请等待审核或联系管理员")
                elif existing.verify_status == 'approved':
                    raise HTTPException(status_code=400, detail="认证已通过，无需重新提交")
                elif existing.verify_status == 'rejected':
                    # 被拒绝的认证可以重新提交，先删除旧记录
                    self.db.delete(existing)
                    self.db.commit()
                    logger.info(f"删除被拒绝的认证记录，准备重新提交: 用户 {user_id}")

            # 上传证书图片
            try:
                file_ext = os.path.splitext(certificate_file.filename)[-1] or '.jpg'
                file_name = f"company_cert/{user_id}_{certificate_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}{file_ext}"
                
                # 确定文件类型
                content_type = certificate_file.content_type or 'image/jpeg'
                
                # 上传文件到云存储
                certificate_url = upload_to_oss(certificate_file.file, file_name, content_type)
                
                logger.info(f"用户 {user_id} 公司证书上传成功: {file_name}")
                
            except CloudStorageError as e:
                logger.error(f"用户 {user_id} 公司证书上传失败: {e}")
                raise DatabaseException(f"证书图片上传失败: {e}")
            except Exception as e:
                logger.error(f"用户 {user_id} 公司证书上传异常: {e}")
                raise DatabaseException(f"证书图片上传异常: {e}")

            # 创建认证记录
            certificate_data = {
                "user_id": user_id,
                "company_name": company_name,
                "certificate_type": certificate_type,
                "certificate_url": certificate_url,
                "verify_status": "pending",
                "verify_remark": None
            }

            # 使用SQLAlchemy ORM插入数据
            certificate_record = CompanyCertificateVerify(**certificate_data)
            self.db.add(certificate_record)
            self.db.commit()
            
            logger.info(f"公司资质认证提交成功: 用户 {user_id}, 公司 {company_name}")
            
            return {"msg": "认证信息已提交，等待审核"}

        except IntegrityError as e:
            logger.error(f"公司资质认证提交失败: {str(e)}")
            raise DatabaseException("认证提交失败，数据约束冲突")
        except Exception as e:
            logger.error(f"公司资质认证提交异常: {e}")
            self.db.rollback()
            raise DatabaseException(f"认证提交失败: {e}")

    def get_certificate_status(self, user_id: int) -> Optional[CompanyCertificateResponse]:
        """获取用户公司资质认证状态"""
        try:
            certificate = self.db.query(CompanyCertificateVerify).filter(
                CompanyCertificateVerify.user_id == user_id
            ).first()
            
            if not certificate:
                return None

            # 为证书图片生成下载URL
            certificate_url = None
            try:
                if certificate.certificate_url:
                    # 从URL中提取object_name
                    object_name = self._extract_object_name_from_url(certificate.certificate_url)
                    if object_name:
                        certificate_url = get_download_url(object_name)
                    else:
                        certificate_url = certificate.certificate_url  # fallback到原始值
            except CloudStorageError as e:
                logger.warning(f"Failed to generate download URL for certificate: {e}")
                certificate_url = certificate.certificate_url  # fallback到原始值

            # 创建响应对象 - 使用SQLAlchemy ORM对象属性访问
            response_data = {
                "id": certificate.id,
                "user_id": certificate.user_id,
                "company_name": certificate.company_name,
                "certificate_type": certificate.certificate_type,
                "certificate_url": certificate_url,
                "verify_status": certificate.verify_status,
                "verify_remark": certificate.verify_remark,
                "create_time": certificate.create_time,
                "update_time": certificate.update_time
            }
            
            return CompanyCertificateResponse(**response_data)

        except Exception as e:
            logger.error(f"获取公司资质认证状态失败: {e}")
            raise DatabaseException(f"获取认证状态失败: {e}")

    def verify_certificate(self, req: CompanyCertificateReviewRequest) -> CompanyCertificateResponse:
        """审核公司资质认证（管理员功能）"""
        try:
            certificate = self.db.query(CompanyCertificateVerify).filter(
                CompanyCertificateVerify.id == req.id
            ).first()
            
            if not certificate:
                raise HTTPException(status_code=404, detail="认证记录不存在")

            # 更新认证状态
            certificate.verify_status = req.verify_status
            certificate.verify_remark = req.verify_remark
            certificate.update_time = datetime.now()
            
            self.db.commit()
            
            logger.info(f"公司资质认证审核完成: ID {req.id} -> {req.verify_status}")
            
            return CompanyCertificateResponse.model_validate(certificate)

        except Exception as e:
            logger.error(f"公司资质认证审核失败: {e}")
            self.db.rollback()
            raise DatabaseException(f"认证审核失败: {e}")

    def get_all_certificates(self, page: int = 1, page_size: int = 20) -> dict:
        """获取所有公司资质认证记录（管理员功能）"""
        try:
            # 计算总数
            total = self.db.query(CompanyCertificateVerify).count()
            
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 获取分页数据
            certificates = self.db.query(CompanyCertificateVerify).order_by(
                CompanyCertificateVerify.create_time.desc()
            ).offset(offset).limit(page_size).all()
            
            # 转换为响应格式
            items = []
            for cert in certificates:
                # 为证书图片生成下载URL
                certificate_url = None
                try:
                    if cert.certificate_url:
                        object_name = self._extract_object_name_from_url(cert.certificate_url)
                        if object_name:
                            certificate_url = get_download_url(object_name)
                        else:
                            certificate_url = cert.certificate_url
                except CloudStorageError as e:
                    logger.warning(f"Failed to generate download URL for certificate {cert.id}: {e}")
                    certificate_url = cert.certificate_url

                items.append(CompanyCertificateResponse(
                    id=cert.id,
                    user_id=cert.user_id,
                    company_name=cert.company_name,
                    certificate_type=cert.certificate_type,
                    certificate_url=certificate_url,
                    verify_status=cert.verify_status,
                    verify_remark=cert.verify_remark,
                    create_time=cert.create_time,
                    update_time=cert.update_time
                ))
            
            return {
                "items": items,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }

        except Exception as e:
            logger.error(f"获取所有公司资质认证记录失败: {e}")
            raise DatabaseException(f"获取认证记录失败: {e}")

    def withdraw_certificate(self, user_id: int) -> dict:
        """撤回公司资质认证申请"""
        try:
            certificate = self.db.query(CompanyCertificateVerify).filter(
                CompanyCertificateVerify.user_id == user_id
            ).first()
            
            if not certificate:
                raise HTTPException(status_code=404, detail="未找到认证记录")
            
            if certificate.verify_status != 'pending':
                raise HTTPException(status_code=400, detail="只能撤回待审核状态的认证申请")
            
            # 删除认证记录
            self.db.delete(certificate)
            self.db.commit()
            
            logger.info(f"公司资质认证撤回成功: 用户 {user_id}")
            
            return {"msg": "认证申请已撤回"}

        except Exception as e:
            logger.error(f"撤回公司资质认证失败: {e}")
            self.db.rollback()
            raise DatabaseException(f"撤回认证失败: {e}")

    def _extract_object_name_from_url(self, url: str) -> Optional[str]:
        """从云存储URL中提取object_name"""
        if not url:
            return None
            
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            path = parsed_url.path.lstrip('/')
            
            # 如果是S3的路径包含bucket名称的格式，需要移除bucket名称
            if 's3' in parsed_url.netloc and '/' in path:
                if parsed_url.netloc.startswith('s3.') and '.' in parsed_url.netloc:
                    parts = path.split('/', 1)
                    if len(parts) > 1:
                        path = parts[1]
            
            return path if path else None
            
        except Exception as e:
            logger.warning(f"Failed to extract object name from URL {url}: {e}")
            return None 