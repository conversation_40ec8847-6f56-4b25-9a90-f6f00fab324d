#!/usr/bin/env python3
"""
测试消息发送功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.message_service import MessageService, MessageCreate
from models.db import get_db

def test_message_creation():
    """测试消息创建"""
    try:
        # 获取数据库会话
        with get_db() as db:
            service = MessageService(db)
            
            # 创建测试消息
            message_data = MessageCreate(
                user_id=1,  # 测试用户ID
                content="这是一条测试消息",
                message_type='task'
            )
            
            # 创建消息
            result = service.create_message(message_data)
            
            print(f"✅ 消息创建成功!")
            print(f"消息ID: {result.id}")
            print(f"用户ID: {result.user_id}")
            print(f"内容: {result.content}")
            print(f"类型: {result.message_type}")
            print(f"创建时间: {result.create_time}")
            
            return True
        
    except Exception as e:
        print(f"❌ 消息创建失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试消息发送功能...")
    success = test_message_creation()
    
    if success:
        print("✅ 测试通过！消息功能正常工作")
    else:
        print("❌ 测试失败！请检查错误信息") 