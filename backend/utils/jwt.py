from datetime import datetime, timedelta, timezone
from jose import jwt, J<PERSON><PERSON>rror
from fastapi import Depends, HTTPException, status, Request, Header
from pydantic import BaseModel
from typing import Optional
from models import get_db, UserInfo
from sqlalchemy.orm import Session
import os

# 建议放到 .env 或 config
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "kolhub-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7天

class TokenData(BaseModel):
    user_id: int
    username: str
    user_type: str
    exp: int

class TokenOut(BaseModel):
    access_token: str
    token_type: str = "bearer"


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": int(expire.timestamp())})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def get_current_user(authorization: str = Header(None)) -> UserInfo:
    """从Authorization header中获取当前用户"""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="未提供有效的JWT Token")

    token = authorization.split(" ", 1)[1]
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        token_data = TokenData(**payload)
    except JWTError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token无效或已过期")

    with get_db() as db:
        user = db.query(UserInfo).filter_by(id=token_data.user_id).first()
        if not user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在")
        return user