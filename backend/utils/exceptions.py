"""
统一异常处理
定义业务异常类型和全局异常处理器
"""
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
from utils.logger import logger

class BusinessException(Exception):
    """业务异常基类"""
    def __init__(self, message: str, code: int = 400, details: dict = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)

class UserNotFoundException(BusinessException):
    """用户不存在异常"""
    def __init__(self, user_id: int = None, username: str = None):
        if user_id:
            message = f"用户不存在: ID={user_id}"
        elif username:
            message = f"用户不存在: username={username}"
        else:
            message = "用户不存在"
        super().__init__(message, 404)

class UserAlreadyExistsException(BusinessException):
    """用户已存在异常"""
    def __init__(self, field: str, value: str):
        message = f"{field}已存在: {value}"
        super().__init__(message, 409)

class TaskNotFoundException(BusinessException):
    """任务不存在异常"""
    def __init__(self, task_id: int = None):
        message = f"任务不存在: ID={task_id}" if task_id else "任务不存在"
        super().__init__(message, 404)

class MessageNotFoundException(BusinessException):
    """消息不存在异常"""
    def __init__(self, message_id: int = None):
        message = f"消息不存在: ID={message_id}" if message_id else "消息不存在"
        super().__init__(message, 404)

class AuthenticationException(BusinessException):
    """认证异常"""
    def __init__(self, message: str = "认证失败"):
        super().__init__(message, 401)

class AuthorizationException(BusinessException):
    """授权异常"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, 403)

class ValidationException(BusinessException):
    """数据验证异常"""
    def __init__(self, message: str, details: dict = None):
        super().__init__(message, 422, details)

class DatabaseException(BusinessException):
    """数据库操作异常"""
    def __init__(self, message: str = "数据库操作失败"):
        super().__init__(message, 500)

# 全局异常处理器
async def business_exception_handler(request: Request, exc: BusinessException):
    """业务异常处理器"""
    logger.error(f"Business exception: {exc.message}", extra={
        "path": request.url.path,
        "method": request.method,
        "code": exc.code,
        "details": exc.details
    })
    
    return JSONResponse(
        status_code=exc.code,
        content={
            "error": True,
            "message": exc.message,
            "code": exc.code,
            "details": exc.details
        }
    )

async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    """SQLAlchemy异常处理器"""
    logger.error(f"Database error: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method,
        "exception_type": type(exc).__name__
    })
    
    # 处理特定的数据库异常
    if isinstance(exc, IntegrityError):
        if 'username' in str(exc.orig):
            message = "用户名已存在"
        elif 'email' in str(exc.orig):
            message = "邮箱已存在"
        else:
            message = "数据约束冲突"
        
        return JSONResponse(
            status_code=409,
            content={
                "error": True,
                "message": message,
                "code": 409
            }
        )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "数据库操作失败",
            "code": 500
        }
    )

async def validation_exception_handler(request: Request, exc: ValidationError):
    """Pydantic验证异常处理器"""
    logger.error(f"Validation error: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method,
        "errors": exc.errors()
    })
    
    return JSONResponse(
        status_code=422,
        content={
            "error": True,
            "message": "数据验证失败",
            "code": 422,
            "details": exc.errors()
        }
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP exception: {exc.detail}", extra={
        "path": request.url.path,
        "method": request.method,
        "status_code": exc.status_code
    })
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "code": exc.status_code
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"Unexpected error: {str(exc)}", extra={
        "path": request.url.path,
        "method": request.method,
        "exception_type": type(exc).__name__
    }, exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误",
            "code": 500
        }
    )
