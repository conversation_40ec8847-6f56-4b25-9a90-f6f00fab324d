import structlog, logging, os

ENV_MODE = os.getenv("ENV_MODE", "LOCAL")
LOGGING_LEVEL = logging.getLevelNamesMapping().get(
    os.getenv("LOGGING_LEVEL", "DEBUG").upper(), logging.DEBUG
)

# 生产环境用 JSON，开发环境用彩色控制台
renderer = [structlog.processors.JSONRenderer()]
if ENV_MODE.lower() == "local".lower() or ENV_MODE.lower() == "staging".lower():
    renderer = [structlog.dev.ConsoleRenderer()]

structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars, # 合并上下文变量
        structlog.processors.TimeStamper(fmt="iso", utc=False),
        structlog.stdlib.add_log_level,         # level 字段
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.dict_tracebacks,   # 错误堆栈
        structlog.processors.CallsiteParameterAdder(
            {
                structlog.processors.CallsiteParameter.FILENAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
                structlog.processors.CallsiteParameter.LINENO,
            }
        ),
        *renderer,
    ],
    cache_logger_on_first_use=True,
    wrapper_class=structlog.make_filtering_bound_logger(LOGGING_LEVEL),
)

logger: structlog.stdlib.BoundLogger = structlog.get_logger()