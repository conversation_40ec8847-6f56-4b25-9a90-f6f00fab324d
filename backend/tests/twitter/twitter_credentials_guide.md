# Twitter OAuth 2.0 用户认证凭据获取指南

## 📋 需要的凭据信息

### 从 Twitter Developer Portal 获取：

1. **Client ID** (客户端ID)
   - 位置：App details → OAuth 2.0 Client ID
   - 格式：类似 `aBcDeFgHiJkLmNoPqRsT123456`
   - 用途：识别你的应用

2. **Client Secret** (客户端密钥) 
   - 位置：App details → OAuth 2.0 Client Secret  
   - 格式：类似 `aBcDeFgHiJkLmNoPqRsT123456xyZ789`
   - ⚠️ 保密：不要泄露这个密钥

3. **Redirect URI** (重定向URI)
   - 位置：App details → Callback URLs
   - 示例：`http://localhost:8080/callback`
   - 必须与Portal中配置的完全一致

## 🔐 OAuth 2.0 用户认证流程

### 步骤1: 生成授权URL
```python
import urllib.parse

params = {
    'response_type': 'code',
    'client_id': 'YOUR_CLIENT_ID',  # 替换为你的Client ID
    'redirect_uri': 'http://localhost:8080/callback',  # 你的回调URL
    'scope': 'tweet.write media.write users.read',  # 请求的权限
    'state': 'random_state_string',  # 安全验证
    'code_challenge': 'code_challenge_string',  # PKCE验证
    'code_challenge_method': 'S256'
}

authorization_url = 'https://twitter.com/i/oauth2/authorize?' + urllib.parse.urlencode(params)
print("请访问:", authorization_url)
```

### 步骤2: 用户授权
- 用户访问授权URL
- Twitter显示授权页面
- 用户点击"Authorize app"
- Twitter重定向到回调URL，带上`code`参数

### 步骤3: 交换访问令牌
```python
import requests
import base64

# 构建认证头
credentials = base64.b64encode(f'{CLIENT_ID}:{CLIENT_SECRET}'.encode()).decode()

headers = {
    'Authorization': f'Basic {credentials}',
    'Content-Type': 'application/x-www-form-urlencoded'
}

data = {
    'grant_type': 'authorization_code',
    'code': 'AUTHORIZATION_CODE_FROM_CALLBACK',  # 从回调获取
    'redirect_uri': 'http://localhost:8080/callback',
    'code_verifier': 'CODE_VERIFIER_FROM_STEP1'
}

response = requests.post(
    'https://api.twitter.com/2/oauth2/token',
    headers=headers,
    data=data
)

token_data = response.json()
access_token = token_data['access_token']  # 这就是User Context令牌！
```

### 步骤4: 使用访问令牌
```python
# 现在可以代表用户上传媒体了
headers = {
    'Authorization': f'Bearer {access_token}'
}

# 上传媒体文件
with open('image.jpg', 'rb') as file:
    files = {'media': file}
    response = requests.post(
        'https://upload.twitter.com/1.1/media/upload.json',
        headers=headers,
        files=files
    )
```

## ⚡ 快速测试方法

### 使用Postman测试
1. 下载Twitter的Postman集合：https://t.co/twitter-api-postman
2. 配置OAuth 2.0认证
3. 直接测试媒体上传端点

### 使用tweepy库（推荐）
```bash
pip install tweepy
```

```python
import tweepy

# OAuth 2.0 用户认证（需要先完成授权流程获取用户令牌）
client = tweepy.Client(
    bearer_token="USER_ACCESS_TOKEN",  # 这里用的是用户访问令牌，不是Bearer Token
)

# 或者使用OAuth 1.0a（更简单）
auth = tweepy.OAuth1UserHandler(
    consumer_key="YOUR_API_KEY",
    consumer_secret="YOUR_API_SECRET",
    access_token="USER_ACCESS_TOKEN",
    access_token_secret="USER_ACCESS_TOKEN_SECRET"
)

api = tweepy.API(auth)
media = api.media_upload("image.jpg")
```

## 🚨 常见问题

1. **"Invalid callback URL"**
   - 确保回调URL在Developer Portal中已配置
   - URL必须完全匹配（包括协议、端口）

2. **"Invalid client"**
   - 检查Client ID和Client Secret是否正确
   - 确保应用权限设置为"Read and write"

3. **"Insufficient permissions"**
   - 应用权限必须是"Read and write"或以上
   - 确保请求了正确的scope (tweet.write, media.write)

## 📝 安全建议

- ✅ 使用HTTPS回调URL（生产环境）
- ✅ 实现PKCE (Proof Key for Code Exchange)
- ✅ 验证state参数防止CSRF攻击
- ✅ 安全存储Client Secret
- ✅ 定期刷新访问令牌 