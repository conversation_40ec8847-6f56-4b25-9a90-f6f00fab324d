#!/usr/bin/env python3
"""
X API /2/media/upload 接口 Token 获取和使用指南

接口信息：
- 端点：POST https://api.twitter.com/2/media/upload
- 认证：OAuth 2.0 User Context Token
- 作用：上传图片或字幕文件
"""

import requests
import base64
import urllib.parse
import secrets
import hashlib
import json
from pathlib import Path

class XMediaUploadClient:
    """X API 媒体上传客户端 - OAuth 2.0 User Context"""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.access_token = None
        
    def get_authorization_url(self) -> tuple[str, str, str]:
        """
        步骤1: 生成用户授权URL
        
        Returns:
            (authorization_url, code_verifier, state) - 授权URL和验证参数
        """
        # 生成PKCE和state参数
        state = secrets.token_urlsafe(32)
        code_verifier = secrets.token_urlsafe(32)
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip('=')
        
        # 媒体上传所需的权限范围
        params = {
            'response_type': 'code',
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': 'tweet.write media.write users.read',  # 媒体上传必需权限
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256'
        }
        
        auth_url = 'https://twitter.com/i/oauth2/authorize?' + urllib.parse.urlencode(params)
        
        print(f"🔗 请在浏览器中访问以下URL进行授权：")
        print(f"{auth_url}")
        print(f"\n📝 保存以下信息用于下一步：")
        print(f"Code Verifier: {code_verifier}")
        print(f"State: {state}")
        
        return auth_url, code_verifier, state
    
    def exchange_token(self, authorization_code: str, code_verifier: str) -> dict:
        """
        步骤2: 用授权码交换访问令牌
        
        Args:
            authorization_code: 从回调URL获取的授权码
            code_verifier: 步骤1生成的code_verifier
            
        Returns:
            Token响应数据
        """
        # Basic认证头
        credentials = base64.b64encode(f'{self.client_id}:{self.client_secret}'.encode()).decode()
        
        headers = {
            'Authorization': f'Basic {credentials}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'grant_type': 'authorization_code',
            'code': authorization_code,
            'redirect_uri': self.redirect_uri,
            'code_verifier': code_verifier
        }
        
        response = requests.post(
            'https://api.twitter.com/2/oauth2/token',
            headers=headers,
            data=data
        )
        
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']
            
            print(f"✅ 成功获取访问令牌！")
            print(f"Access Token: {self.access_token}")
            print(f"Token Type: {token_data.get('token_type', 'Bearer')}")
            print(f"Expires In: {token_data.get('expires_in', 'N/A')} 秒")
            
            return token_data
        else:
            error_data = response.json() if response.content else {}
            raise Exception(f"Token获取失败: {response.status_code} - {error_data}")
    
    def upload_media(self, file_path: str, media_category: str = "tweet_image") -> dict:
        """
        步骤3: 使用User Context Token上传媒体
        
        Args:
            file_path: 媒体文件路径
            media_category: 媒体类别 (tweet_image, dm_image, subtitles)
            
        Returns:
            上传响应数据
        """
        if not self.access_token:
            raise Exception("❌ 请先完成OAuth认证获取访问令牌")
        
        file_path = Path(file_path)
        if not file_path.exists():
            raise Exception(f"❌ 文件不存在: {file_path}")
        
        # 确定媒体类型
        media_types = {
            '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
            '.png': 'image/png', '.gif': 'image/gif',
            '.webp': 'image/webp', '.bmp': 'image/bmp',
            '.tiff': 'image/tiff', '.pjpeg': 'image/pjpeg',
            '.srt': 'text/srt', '.vtt': 'text/vtt'
        }
        
        file_extension = file_path.suffix.lower()
        media_type = media_types.get(file_extension)
        
        if not media_type:
            raise Exception(f"❌ 不支持的文件类型: {file_extension}")
        
        # 准备请求头 - 使用User Context Token
        headers = {
            'Authorization': f'Bearer {self.access_token}'
        }
        
        # 准备multipart/form-data数据
        with open(file_path, 'rb') as file:
            files = {
                'media': (file_path.name, file, media_type)
            }
            data = {
                'media_category': media_category,
                'media_type': media_type,
                'shared': 'false'
            }
            
            print(f"📤 正在上传媒体文件: {file_path.name}")
            print(f"📋 媒体类型: {media_type}")
            print(f"🏷️  媒体类别: {media_category}")
            
            response = requests.post(
                'https://api.twitter.com/2/media/upload',  # X API v2端点
                headers=headers,
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            media_data = response.json()
            print(f"✅ 媒体上传成功！")
            print(f"Media ID: {media_data['data']['id']}")
            print(f"Media Key: {media_data['data']['media_key']}")
            print(f"文件大小: {media_data['data']['size']} bytes")
            
            return media_data
        else:
            error_data = response.json() if response.content else {}
            raise Exception(f"❌ 媒体上传失败: {response.status_code} - {error_data}")
    
    def create_tweet_with_media(self, text: str, media_id: str) -> dict:
        """
        步骤4: 使用上传的媒体创建推文
        
        Args:
            text: 推文内容
            media_id: 媒体ID (从upload_media返回)
            
        Returns:
            推文创建响应
        """
        if not self.access_token:
            raise Exception("❌ 请先完成OAuth认证获取访问令牌")
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'text': text,
            'media': {
                'media_ids': [media_id]
            }
        }
        
        response = requests.post(
            'https://api.twitter.com/2/tweets',
            headers=headers,
            json=payload
        )
        
        if response.status_code == 201:
            tweet_data = response.json()
            print(f"✅ 推文发布成功！")
            print(f"Tweet ID: {tweet_data['data']['id']}")
            print(f"Tweet Text: {tweet_data['data']['text']}")
            
            return tweet_data
        else:
            error_data = response.json() if response.content else {}
            raise Exception(f"❌ 推文发布失败: {response.status_code} - {error_data}")


def main():
    """完整的使用流程示例"""
    
    print("=" * 60)
    print("🚀 X API /2/media/upload Token 获取和使用指南")
    print("=" * 60)
    
    # 配置信息 - 需要从X Developer Portal获取
    print("\n📋 步骤0: 配置应用信息")
    print("请在 https://developer.x.com/en/portal/dashboard 获取以下信息：")
    
    client_id = input("输入您的 Client ID: ").strip()
    client_secret = input("输入您的 Client Secret: ").strip()
    redirect_uri = input("输入回调URL (默认: http://localhost:8080/callback): ").strip() or "http://localhost:8080/callback"
    
    client = XMediaUploadClient(client_id, client_secret, redirect_uri)
    
    try:
        # 步骤1: 获取授权URL
        print("\n" + "=" * 40)
        print("📋 步骤1: 获取用户授权")
        print("=" * 40)
        
        auth_url, code_verifier, state = client.get_authorization_url()
        
        # 步骤2: 用户手动授权并获取授权码
        print("\n📋 步骤2: 获取授权码")
        authorization_code = input("请输入从回调URL获取的授权码: ").strip()
        
        # 步骤3: 交换访问令牌
        print("\n" + "=" * 40)
        print("🔑 步骤3: 交换访问令牌")
        print("=" * 40)
        
        token_data = client.exchange_token(authorization_code, code_verifier)
        
        # 步骤4: 上传媒体文件
        print("\n" + "=" * 40)
        print("📤 步骤4: 上传媒体文件")
        print("=" * 40)
        
        media_file = input("输入要上传的媒体文件路径: ").strip()
        if media_file:
            media_category = input("选择媒体类别 (tweet_image/dm_image/subtitles, 默认: tweet_image): ").strip() or "tweet_image"
            
            upload_result = client.upload_media(media_file, media_category)
            media_id = upload_result['data']['id']
            
            # 步骤5: 创建带媒体的推文（可选）
            print("\n" + "=" * 40)
            print("🐦 步骤5: 创建推文（可选）")
            print("=" * 40)
            
            tweet_text = input("输入推文内容（回车跳过）: ").strip()
            if tweet_text:
                client.create_tweet_with_media(tweet_text, media_id)
        
        print("\n🎉 所有步骤完成！")
        
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return False
    
    return True


if __name__ == "__main__":
    main() 