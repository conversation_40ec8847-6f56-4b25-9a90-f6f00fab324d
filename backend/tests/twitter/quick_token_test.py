#!/usr/bin/env python3
"""
快速token有效性测试
"""

import requests

def test_user_context_token():
    """测试User Context Token是否还有效"""
    
    # 之前媒体上传成功的token
    access_token = "TlBSNldwM0FTaUl3emluU25FR1ppTWIwY282eXlXMHNRV2hEMXczWk5mUGlHOjE3NTM0NTA2NDA0MzY6MToxOmF0OjE"
    
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    
    print("========================================")
    print("🔍 快速Token有效性检查")
    print("========================================")
    print(f"🔑 Token: {access_token[:20]}...")
    
    # 1. 测试用户信息端点
    print("\n📋 1. 测试 /2/users/me 端点...")
    try:
        response = requests.get(
            'https://api.twitter.com/2/users/me',
            headers=headers,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            user_data = response.json()
            print(f"   ✅ Token有效!")
            if 'data' in user_data:
                user = user_data['data']
                print(f"   👤 用户: @{user.get('username')} ({user.get('name')})")
                return True
        elif response.status_code == 401:
            print(f"   ❌ Token已过期或无效")
            error_data = response.json() if response.content else {}
            print(f"   详情: {error_data}")
            return False
        else:
            error_data = response.json() if response.content else {}
            print(f"   ❌ 其他错误: {error_data}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {str(e)}")
        return False

def test_simple_tweet():
    """测试发送简单推文"""
    
    access_token = "TlBSNldwM0FTaUl3emluU25FR1ppTWIwY282eXlXMHNRV2hEMXczWk5mUGlHOjE3NTM0NTA2NDA0MzY6MToxOmF0OjE"
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    print("\n📋 2. 测试简单推文发送...")
    
    # 发送最简单的测试推文
    test_data = {
        'text': f'API测试 - {requests.utils.default_user_agent()[:10]}'
    }
    
    try:
        response = requests.post(
            'https://api.twitter.com/2/tweets',
            headers=headers,
            json=test_data,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ 推文发送成功!")
            if 'data' in result:
                tweet_data = result['data']
                tweet_id = tweet_data.get('id')
                print(f"   🆔 推文ID: {tweet_id}")
                print(f"   📝 内容: {tweet_data.get('text')}")
                
                # 尝试删除测试推文
                print(f"   🗑️  尝试删除测试推文...")
                delete_response = requests.delete(
                    f'https://api.twitter.com/2/tweets/{tweet_id}',
                    headers={'Authorization': f'Bearer {access_token}'},
                    timeout=10
                )
                if delete_response.status_code == 200:
                    print(f"   ✅ 测试推文已删除")
                else:
                    print(f"   ⚠️  删除失败(状态码: {delete_response.status_code})")
                    
            return True
        else:
            error_data = response.json() if response.content else {}
            print(f"   ❌ 发送失败: {error_data}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {str(e)}")
        return False

def main():
    print("============================================================")
    print("🚀 Twitter API Token 快速诊断")
    print("============================================================")
    
    # 测试token有效性
    is_valid = test_user_context_token()
    
    if is_valid:
        # 如果token有效，测试推文功能
        can_tweet = test_simple_tweet()
        
        if can_tweet:
            print("\n🎉 结论: Token正常，可以发推文!")
            print("💡 你可以重新运行 upload.py 发送带媒体的推文")
        else:
            print("\n❌ 结论: Token有效但无法发推文")
            print("💡 可能需要检查应用权限设置")
    else:
        print("\n❌ 结论: Token无效或已过期")
        print("💡 需要重新获取User Context Token")
        print("🔗 请重新运行: python x_media_upload_token_guide.py")
    
    print("\n========================================")

if __name__ == "__main__":
    main() 