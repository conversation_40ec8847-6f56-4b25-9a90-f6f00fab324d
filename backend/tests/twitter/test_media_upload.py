#!/usr/bin/env python3
"""
简化的X API媒体上传测试脚本
直接使用已获取的access_token进行测试
"""

import requests
from pathlib import Path
import mimetypes

def test_media_upload(access_token: str, file_path: str, media_category: str = "tweet_image"):
    """
    测试媒体上传功能
    
    Args:
        access_token: 已获取的OAuth 2.0 User Context access token
        file_path: 要上传的媒体文件路径
        media_category: 媒体类别 (tweet_image, dm_image, subtitles)
    """
    print("========================================")
    print("🔄 开始测试媒体上传...")
    print("========================================")
    
    # 检查文件
    file_path = Path(file_path)
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    # 获取文件信息
    file_size = file_path.stat().st_size
    content_type, _ = mimetypes.guess_type(str(file_path))
    if not content_type:
        if file_path.suffix.lower() in ['.jpg', '.jpeg']:
            content_type = 'image/jpeg'
        elif file_path.suffix.lower() == '.png':
            content_type = 'image/png'
        else:
            content_type = 'application/octet-stream'
    
    print(f"📁 文件: {file_path.name}")
    print(f"📊 大小: {file_size:,} 字节")
    print(f"🎯 类型: {content_type}")
    print(f"📂 类别: {media_category}")
    
    # 准备请求
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    
    files = {
        'media': (file_path.name, open(file_path, 'rb'), content_type)
    }
    
    data = {
        'media_category': media_category
    }
    
    print(f"\n🔄 正在上传到 /2/media/upload...")
    
    try:
        response = requests.post(
            'https://api.twitter.com/2/media/upload',
            headers=headers,
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print("✅ 上传成功！")
            
            # 处理不同的响应格式
            if 'data' in result:
                # X API v2 格式
                data = result['data']
                print(f"📁 Media Key: {data.get('media_key', 'N/A')}")
                print(f"🆔 Media ID: {data.get('id', 'N/A')}")
                print(f"📊 大小: {data.get('size', 'N/A')} 字节")
                if 'image' in data:
                    img_info = data['image']
                    print(f"🖼️  图片: {img_info.get('w')}x{img_info.get('h')} ({img_info.get('image_type')})")
                print(f"⏰ 过期时间: {data.get('expires_after_secs', 'N/A')} 秒")
            else:
                # 传统格式
                print(f"📁 Media ID: {result.get('media_id_string', 'N/A')}")
                print(f"🎯 类型: {result.get('media_type', 'N/A')}")
                print(f"📊 大小: {result.get('size', 'N/A')} 字节")
            
            if 'processing_info' in result:
                print(f"⏳ 处理状态: {result['processing_info'].get('state', 'N/A')}")
            
            return result
        else:
            print("❌ 上传失败！")
            print(f"状态码: {response.status_code}")
            
            try:
                error_data = response.json()
                print(f"错误详情: {error_data}")
                
                if 'errors' in error_data:
                    for error in error_data['errors']:
                        print(f"  - {error.get('message', 'Unknown error')}")
                        
            except Exception as e:
                print(f"响应内容: {response.text}")
            
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return None
    
    finally:
        # 关闭文件
        if 'media' in files:
            files['media'][1].close()

def main():
    print("============================================================")
    print("🧪 X API 媒体上传测试工具")
    print("============================================================")
    
    # 获取用户输入
    access_token = input("请输入你的 Access Token: ").strip()
    if not access_token:
        print("❌ Access Token 不能为空")
        return
    
    file_path = input("请输入要上传的文件路径: ").strip()
    if not file_path:
        print("❌ 文件路径不能为空")
        return
    
    media_category = input("请输入媒体类别 (默认: tweet_image): ").strip()
    if not media_category:
        media_category = "tweet_image"
    
    # 执行上传测试
    result = test_media_upload(access_token, file_path, media_category)
    
    if result:
        print("\n🎉 测试完成！你可以使用这个 media_id 来发推文。")
    else:
        print("\n💡 请检查:")
        print("  1. Access Token 是否正确")
        print("  2. Token 是否有 media.write 权限")
        print("  3. 文件格式是否支持 (JPG, PNG, GIF, MP4等)")
        print("  4. 文件大小是否在限制内")

if __name__ == "__main__":
    main() 