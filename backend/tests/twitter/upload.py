import requests

# 发推文时附带媒体
# 使用 User Context Token (和媒体上传时相同的token)
headers = {
    'Authorization': f'Bearer TlBSNldwM0FTaUl3emluU05FR1ppTWIwY282eXlXMHNRV2hEMXczWk5mUGlHOjE3NTM0NTA2NDA0MzY6MToxOmF0OjE',
    'Content-Type': 'application/json'
}

data = {
    'text': '这是一条带图片的推文',
    'media': {
        'media_ids': ['1948740819688763392']  # 使用Media ID
    }
}

response = requests.post(
    'https://api.twitter.com/2/tweets',
    headers=headers,
    json=data
)

print("========================================")
print("🐦 推文发送结果")
print("========================================")
print(f"📡 响应状态: {response.status_code}")

if response.status_code == 201:
    result = response.json()
    print("✅ 推文发送成功！")
    
    if 'data' in result:
        tweet_data = result['data']
        print(f"🆔 推文ID: {tweet_data.get('id', 'N/A')}")
        print(f"📝 推文内容: {tweet_data.get('text', 'N/A')}")
        
        # 构造推文链接
        tweet_id = tweet_data.get('id')
        if tweet_id:
            print(f"🔗 推文链接: https://twitter.com/i/web/status/{tweet_id}")
    
    print(f"📊 完整响应: {result}")
else:
    print("❌ 推文发送失败！")
    print(f"状态码: {response.status_code}")
    
    try:
        error_data = response.json()
        print(f"错误详情: {error_data}")
        
        if 'errors' in error_data:
            for error in error_data['errors']:
                print(f"  - {error.get('message', 'Unknown error')}")
                
    except Exception as e:
        print(f"响应内容: {response.text}")

print("========================================")
