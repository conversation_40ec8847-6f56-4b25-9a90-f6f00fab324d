#!/usr/bin/env python3
"""
定时任务模块测试脚本

演示如何使用新的 jobs 模块管理定时任务
"""

import sys
import time
from jobs import start_scheduler, stop_scheduler
from jobs.scheduler import (
    get_scheduler, list_jobs, add_custom_job, 
    remove_job, get_all_job_configs
)


def demo_custom_job():
    """演示用的自定义任务"""
    print("🚀 演示自定义任务执行了！")


def test_jobs_module():
    """测试jobs模块功能"""
    print("=" * 60)
    print("定时任务模块 (Jobs) 功能测试")
    print("=" * 60)
    
    try:
        # 测试1: 查看所有任务配置
        print("\n📋 测试1: 查看所有任务配置")
        job_configs = get_all_job_configs()
        print(f"共配置了 {len(job_configs)} 个任务:")
        for i, config in enumerate(job_configs, 1):
            print(f"  {i}. {config.get('id')} - {config.get('description', '无描述')}")
        
        # 测试2: 启动调度器
        print("\n🎬 测试2: 启动定时任务调度器")
        scheduler = start_scheduler()
        print(f"✅ 调度器启动成功: {scheduler is not None}")
        
        # 等待一下让调度器完全启动
        time.sleep(1)
        
        # 测试3: 查看运行中的任务
        print("\n📊 测试3: 查看运行中的任务")
        running_jobs = list_jobs()
        print(f"运行中的任务 ({len(running_jobs)} 个):")
        for job_id in running_jobs:
            print(f"  - {job_id}")
        
        # 测试4: 动态添加自定义任务
        print("\n➕ 测试4: 动态添加自定义任务")
        trigger_config = {
            'trigger': 'interval',
            'seconds': 10  # 每10秒执行一次
        }
        
        success = add_custom_job(
            func=demo_custom_job,
            trigger_config=trigger_config,
            job_id='demo_custom_job',
            description='演示自定义任务: 每10秒执行'
        )
        
        print(f"✅ 动态添加任务: {'成功' if success else '失败'}")
        
        # 查看更新后的任务列表
        updated_jobs = list_jobs()
        print(f"更新后的任务 ({len(updated_jobs)} 个):")
        for job_id in updated_jobs:
            print(f"  - {job_id}")
        
        # 测试5: 获取调度器状态
        print("\n📈 测试5: 获取调度器状态")
        current_scheduler = get_scheduler()
        if current_scheduler and current_scheduler.running:
            print("✅ 调度器正在运行")
            
            # 获取详细的任务信息
            jobs = current_scheduler.get_jobs()
            print(f"调度器中的任务详情:")
            for job in jobs:
                print(f"  - ID: {job.id}")
                print(f"    下次执行: {job.next_run_time}")
                print(f"    触发器: {job.trigger}")
                print()
        else:
            print("❌ 调度器未运行")
        
        # 测试6: 移除自定义任务
        print("\n🗑️  测试6: 移除自定义任务")
        remove_success = remove_job('demo_custom_job')
        print(f"移除自定义任务: {'成功' if remove_success else '失败'}")
        
        # 再次查看任务列表
        final_jobs = list_jobs()
        print(f"最终任务列表 ({len(final_jobs)} 个):")
        for job_id in final_jobs:
            print(f"  - {job_id}")
        
        print("\n⏱️  等待5秒观察任务执行...")
        time.sleep(5)
        
        # 测试7: 停止调度器
        print("\n🛑 测试7: 停止定时任务调度器")
        stop_scheduler()
        print("✅ 调度器已停止")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def show_usage():
    """显示使用说明"""
    print("定时任务模块 (Jobs) 使用说明:")
    print("")
    print("1. 模块结构:")
    print("   backend/jobs/")
    print("   ├── __init__.py               # 模块初始化")
    print("   ├── scheduler.py              # 调度器管理")
    print("   ├── daily_settlement.py       # 日度结算统计任务")
    print("   ├── test_job.py              # 测试任务")
    print("   └── README.md                # 详细文档")
    print("")
    print("2. 基本使用:")
    print("   from jobs import start_scheduler, stop_scheduler")
    print("   ")
    print("   # 启动所有定时任务")
    print("   scheduler = start_scheduler()")
    print("   ")
    print("   # 停止所有定时任务")
    print("   stop_scheduler()")
    print("")
    print("3. 高级功能:")
    print("   from jobs.scheduler import add_custom_job, remove_job, list_jobs")
    print("   ")
    print("   # 动态添加任务")
    print("   add_custom_job(func, trigger_config, job_id)")
    print("   ")
    print("   # 移除任务")
    print("   remove_job(job_id)")
    print("   ")
    print("   # 查看所有任务")
    print("   job_ids = list_jobs()")
    print("")
    print("4. 添加新任务:")
    print("   - 在 jobs/ 目录下创建新任务文件")
    print("   - 定义任务函数和配置")
    print("   - 在 scheduler.py 中注册任务")
    print("")
    print("5. 配置定时任务:")
    print("   # Cron 触发器 (特定时间)") 
    print("   config = {")
    print("       'trigger': 'cron',")
    print("       'hour': 2, 'minute': 0  # 每天凌晨2点")
    print("   }")
    print("   ")
    print("   # Interval 触发器 (间隔时间)")
    print("   config = {")
    print("       'trigger': 'interval',")
    print("       'minutes': 30  # 每30分钟")
    print("   }")
    print("")
    print("详细文档请参考: backend/jobs/README.md")


def show_job_status():
    """显示当前任务状态"""
    print("当前定时任务状态:")
    print("=" * 40)
    
    scheduler = get_scheduler()
    if not scheduler:
        print("❌ 调度器未启动")
        return
    
    if not scheduler.running:
        print("❌ 调度器已停止")
        return
    
    print("✅ 调度器正在运行")
    
    jobs = scheduler.get_jobs()
    if not jobs:
        print("📝 暂无运行中的任务")
        return
    
    print(f"\n运行中的任务 ({len(jobs)} 个):")
    for job in jobs:
        print(f"📋 {job.id}")
        print(f"   下次执行: {job.next_run_time}")
        print(f"   触发器: {job.trigger}")
        print()


if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "help":
            show_usage()
        elif command == "status":
            show_job_status()
        else:
            print(f"未知命令: {command}")
            print("可用命令: help, status")
    else:
        test_jobs_module()
        
    print("\n💡 提示: 运行 'python test_jobs.py help' 查看使用说明")
    print("💡 提示: 运行 'python test_jobs.py status' 查看任务状态") 