"""
云存储模块测试

测试新的storage模块功能，包括阿里云OSS和AWS S3的功能。
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock
from io import BytesIO

# 添加父目录到Python路径
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

# 设置测试环境变量
os.environ['CLOUD_STORAGE_PROVIDER'] = 'oss'  # 默认测试OSS
os.environ['OSS_BUCKET_NAME'] = 'test-bucket'
os.environ['OSS_REGION'] = 'ap-southeast-1'
os.environ['OSS_ACCESS_KEY_ID'] = 'test-key-id'
os.environ['OSS_ACCESS_KEY_SECRET'] = 'test-key-secret'

from storage import (
    CloudStorageError,
    upload_to_oss,
    get_download_url,
    delete_file,
    file_exists,
    get_file_info,
    get_storage_provider,
    upload_file_with_unique_name,
    batch_delete_files,
    get_files_info,
)
from storage.providers import AlibabaCloudOSSProvider, AWSS3Provider
from storage.factory import get_cloud_storage_provider, reset_storage_provider
from storage.exceptions import (
    CloudStorageConfigError,
    CloudStorageAuthError,
    CloudStorageNetworkError,
    CloudStorageFileNotFoundError,
)


class TestCloudStorageExceptions(unittest.TestCase):
    """测试云存储异常类"""
    
    def test_cloud_storage_error(self):
        """测试基础异常类"""
        error_msg = "测试错误消息"
        error = CloudStorageError(error_msg)
        self.assertEqual(str(error), error_msg)
    
    def test_config_error(self):
        """测试配置错误异常"""
        error = CloudStorageConfigError("配置错误")
        self.assertIsInstance(error, CloudStorageError)
    
    def test_auth_error(self):
        """测试认证错误异常"""
        error = CloudStorageAuthError("认证错误")
        self.assertIsInstance(error, CloudStorageError)


class TestAlibabaCloudOSSProvider(unittest.TestCase):
    """测试阿里云OSS提供商"""
    
    @patch('storage.providers.oss_provider.oss.Client')
    @patch('storage.providers.oss_provider.oss.credentials.EnvironmentVariableCredentialsProvider')
    @patch('storage.providers.oss_provider.oss.config.load_default')
    def setUp(self, mock_config, mock_credentials, mock_client):
        """设置测试环境"""
        # 模拟配置和客户端
        mock_config.return_value = MagicMock()
        mock_credentials.return_value = MagicMock()
        self.mock_client = MagicMock()
        mock_client.return_value = self.mock_client
        
        # 创建提供商实例
        self.provider = AlibabaCloudOSSProvider()
    
    def test_upload_file_success(self):
        """测试文件上传成功"""
        # 模拟上传响应
        mock_result = MagicMock()
        mock_result.etag = 'test-etag'
        self.mock_client.put_object.return_value = mock_result
        
        # 测试上传
        file_obj = BytesIO(b'test content')
        object_name = 'test/file.txt'
        result = self.provider.upload_file(file_obj, object_name)
        
        # 验证结果
        expected_url = f"https://{self.provider.bucket_name}.oss-{self.provider.region}.aliyuncs.com/{object_name}"
        self.assertEqual(result, expected_url)
    
    def test_get_file_info_success(self):
        """测试获取文件信息"""
        # 模拟文件信息响应
        mock_result = MagicMock()
        mock_result.content_length = 1024
        mock_result.last_modified = 'test-date'
        mock_result.content_type = 'text/plain'
        mock_result.etag = 'test-etag'
        self.mock_client.head_object.return_value = mock_result
        
        # 测试获取文件信息
        result = self.provider.get_file_info('test/file.txt')
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['size'], 1024)
        self.assertEqual(result['provider'], 'oss')
        self.assertEqual(result['bucket'], self.provider.bucket_name)


class TestAWSS3Provider(unittest.TestCase):
    """测试AWS S3提供商"""
    
    def setUp(self):
        """设置测试环境"""
        # 设置S3环境变量
        os.environ['AWS_S3_BUCKET_NAME'] = 'test-s3-bucket'
        os.environ['AWS_REGION'] = 'us-east-1'
        os.environ['AWS_ACCESS_KEY_ID'] = 'test-aws-key-id'
        os.environ['AWS_SECRET_ACCESS_KEY'] = 'test-aws-secret-key'
    
    @patch('storage.providers.s3_provider.boto3.Session')
    def test_init_success(self, mock_session):
        """测试S3提供商初始化成功"""
        # 模拟boto3会话
        mock_session_instance = MagicMock()
        mock_session.return_value = mock_session_instance
        
        mock_s3_client = MagicMock()
        mock_session_instance.client.return_value = mock_s3_client
        mock_session_instance.resource.return_value = MagicMock()
        
        # 模拟存储桶验证成功
        mock_s3_client.head_bucket.return_value = None
        
        # 创建提供商实例
        provider = AWSS3Provider()
        
        # 验证初始化
        self.assertEqual(provider.bucket_name, 'test-s3-bucket')
        self.assertEqual(provider.region, 'us-east-1')
    
    def test_init_no_bucket_name(self):
        """测试S3提供商初始化失败 - 缺少存储桶名称"""
        # 移除存储桶名称环境变量
        if 'AWS_S3_BUCKET_NAME' in os.environ:
            del os.environ['AWS_S3_BUCKET_NAME']
        
        # 测试初始化失败
        with self.assertRaises(CloudStorageConfigError):
            AWSS3Provider()
        
        # 恢复环境变量
        os.environ['AWS_S3_BUCKET_NAME'] = 'test-s3-bucket'


class TestStorageFactory(unittest.TestCase):
    """测试存储工厂函数"""
    
    def setUp(self):
        """重置全局提供商"""
        reset_storage_provider()
    
    @patch('storage.factory.AlibabaCloudOSSProvider')
    def test_get_oss_provider(self, mock_oss_provider):
        """测试获取OSS提供商"""
        os.environ['CLOUD_STORAGE_PROVIDER'] = 'oss'
        
        result = get_cloud_storage_provider()
        
        mock_oss_provider.assert_called_once()
    
    @patch('storage.factory.AWSS3Provider')
    def test_get_s3_provider(self, mock_s3_provider):
        """测试获取S3提供商"""
        os.environ['CLOUD_STORAGE_PROVIDER'] = 's3'
        
        result = get_cloud_storage_provider()
        
        mock_s3_provider.assert_called_once()
    
    def test_get_unsupported_provider(self):
        """测试获取不支持的提供商"""
        os.environ['CLOUD_STORAGE_PROVIDER'] = 'unsupported'
        
        with self.assertRaises(CloudStorageConfigError):
            get_cloud_storage_provider()


class TestStorageService(unittest.TestCase):
    """测试存储服务接口"""
    
    def setUp(self):
        """重置全局提供商"""
        reset_storage_provider()
    
    @patch('storage.service.get_storage_provider')
    def test_upload_to_oss_compatibility(self, mock_get_provider):
        """测试upload_to_oss兼容性函数"""
        # 模拟提供商
        mock_provider = MagicMock()
        mock_provider.upload_file.return_value = 'test-url'
        mock_get_provider.return_value = mock_provider
        
        # 测试兼容性函数
        file_obj = BytesIO(b'test content')
        object_name = 'test/file.txt'
        result = upload_to_oss(file_obj, object_name)
        
        # 验证结果
        self.assertEqual(result, 'test-url')
        mock_provider.upload_file.assert_called_once_with(file_obj, object_name, 'text/plain')
    
    @patch('storage.service.get_storage_provider')
    def test_get_download_url_compatibility(self, mock_get_provider):
        """测试get_download_url兼容性函数"""
        # 模拟提供商
        mock_provider = MagicMock()
        mock_provider.get_download_url.return_value = 'test-download-url'
        mock_get_provider.return_value = mock_provider
        
        # 测试兼容性函数
        object_name = 'test/file.txt'
        result = get_download_url(object_name)
        
        # 验证结果
        self.assertEqual(result, 'test-download-url')
        mock_provider.get_download_url.assert_called_once_with(object_name, 3600)
    
    @patch('storage.service.get_storage_provider')
    def test_upload_file_with_unique_name(self, mock_get_provider):
        """测试使用唯一文件名上传"""
        # 模拟提供商
        mock_provider = MagicMock()
        mock_provider.upload_file.return_value = 'test-url'
        mock_get_provider.return_value = mock_provider
        
        # 测试唯一文件名上传
        file_obj = BytesIO(b'test content')
        url, object_name = upload_file_with_unique_name(
            file_obj, 
            directory="uploads", 
            file_extension=".txt"
        )
        
        # 验证结果
        self.assertEqual(url, 'test-url')
        self.assertTrue(object_name.startswith('uploads/'))
        self.assertTrue(object_name.endswith('.txt'))
    
    @patch('storage.service.get_storage_provider')
    def test_batch_delete_files(self, mock_get_provider):
        """测试批量删除文件"""
        # 模拟提供商
        mock_provider = MagicMock()
        mock_provider.delete_file.side_effect = [True, False, True]
        mock_get_provider.return_value = mock_provider
        
        # 测试批量删除
        object_names = ['file1.txt', 'file2.txt', 'file3.txt']
        results = batch_delete_files(object_names)
        
        # 验证结果
        expected_results = {
            'file1.txt': True,
            'file2.txt': False,
            'file3.txt': True
        }
        self.assertEqual(results, expected_results)
    
    @patch('storage.service.get_storage_provider')
    def test_get_files_info(self, mock_get_provider):
        """测试批量获取文件信息"""
        # 模拟提供商
        mock_provider = MagicMock()
        mock_provider.get_file_info.side_effect = [
            {'size': 100, 'content_type': 'text/plain'},
            None,  # 文件不存在
            {'size': 200, 'content_type': 'image/jpeg'}
        ]
        mock_get_provider.return_value = mock_provider
        
        # 测试批量获取文件信息
        object_names = ['file1.txt', 'missing.txt', 'image.jpg']
        results = get_files_info(object_names)
        
        # 验证结果
        self.assertIsNotNone(results['file1.txt'])
        self.assertIsNone(results['missing.txt'])
        self.assertIsNotNone(results['image.jpg'])
        self.assertEqual(results['file1.txt']['size'], 100)
        self.assertEqual(results['image.jpg']['size'], 200)
    
    def tearDown(self):
        """清理测试环境"""
        reset_storage_provider()


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2) 