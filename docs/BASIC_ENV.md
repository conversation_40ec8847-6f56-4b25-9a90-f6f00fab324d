## uv 一键安装（官方推荐）

```
curl -LsSf https://astral.sh/uv/install.sh | sh

source ~/.bashrc   # 或者 source ~/.zshrc

uv --version
```

## 安装 nvm（Node.js 版本管理器）

```
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

source ~/.bashrc

nvm --version

```

## 安装 npm（通过 nvm 安装 Node.js 自动包含）
使用 nvm 安装 Node.js（含 npm）：


```
nvm install --lts
```
设置默认版本：

```
nvm use --lts
nvm alias default lts/*
```

验证

```
node -v
npm -v
```