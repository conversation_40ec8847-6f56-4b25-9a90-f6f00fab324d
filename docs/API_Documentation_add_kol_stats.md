# KOL统计数据上传接口文档

## 接口概述

**接口名称：** KOL统计数据上传  
**接口地址：** `POST /stats/add_kol_stats`  
**接口类型：** OpenAPI 外部接口  
**认证方式：** Bearer Token (API Key)  
**内容类型：** `application/json`

## 功能说明

此接口用于渠道方通过 `channel_code` 上传KOL营销统计数据。系统会根据渠道码自动获取对应的KOL信息和任务信息，支持按天批量上传多天的统计数据。

## 请求信息

### 请求头 (Headers)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {API_KEY} 格式的认证令牌 |
| Content-Type | string | 是 | application/json |

### 请求体 (Request Body)

```json
{
  "user_id": 123,
  "channel_code": "ch001",
  "data": [
    {
      "stat_date": "2024-07-01",
      "click_count": 100,
      "register_count": 15,
      "deposit_amount": 1500.50,
      "conversion_rate": 15.0,
      "ftt": 75.25
    },
    {
      "stat_date": "2024-07-02",
      "click_count": 120,
      "register_count": 18,
      "deposit_amount": 1800.75,
      "conversion_rate": 15.0,
      "ftt": 90.37
    }
  ]
}
```

### 参数说明

#### 顶层参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | integer | 是 | 渠道/商户用户ID |
| channel_code | string | 是 | 渠道码，用于关联营销任务 |
| data | array | 是 | 统计数据数组，支持多天数据 |

#### data 数组元素参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| stat_date | string(date) | 是 | - | 统计日期，格式：YYYY-MM-DD |
| click_count | integer | 否 | 0 | 点击数量 |
| register_count | integer | 否 | 0 | 注册数量 |
| deposit_amount | number | 否 | 0 | 充值金额 |
| conversion_rate | number | 否 | 0 | 转化率(%) |
| ftt | number | 否 | 0 | FTT字段值 |

## 响应信息

### 成功响应

**HTTP状态码：** `200 OK`

```json
{
  "success": true,
  "processed_records": 2
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 处理结果，true表示成功 |
| processed_records | integer | 成功处理的记录数量 |

## 错误响应

### 认证错误

**HTTP状态码：** `401 Unauthorized`

```json
{
  "detail": "Invalid or missing Authorization header"
}
```

### 渠道码不存在

**HTTP状态码：** `400 Bad Request`

```json
{
  "detail": "渠道码不存在: ch001"
}
```

### 参数验证错误

**HTTP状态码：** `422 Unprocessable Entity`

```json
{
  "detail": [
    {
      "loc": ["body", "user_id"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 服务器内部错误

**HTTP状态码：** `500 Internal Server Error`

```json
{
  "detail": "Internal server error"
}
```

## 使用示例

### cURL 示例

```bash
curl -X POST "https://api.example.com/stats/add_kol_stats" \
  -H "Authorization: Bearer your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "channel_code": "ch001",
    "data": [
      {
        "stat_date": "2024-07-01",
        "click_count": 100,
        "register_count": 15,
        "deposit_amount": 1500.50,
        "conversion_rate": 15.0,
        "ftt": 75.25
      }
    ]
  }'
```

### Python 示例

```python
import requests
import json

url = "https://api.example.com/stats/add_kol_stats"
headers = {
    "Authorization": "Bearer your_api_key_here",
    "Content-Type": "application/json"
}

data = {
    "user_id": 123,
    "channel_code": "ch001",
    "data": [
        {
            "stat_date": "2024-07-01",
            "click_count": 100,
            "register_count": 15,
            "deposit_amount": 1500.50,
            "conversion_rate": 15.0,
            "ftt": 75.25
        },
        {
            "stat_date": "2024-07-02",
            "click_count": 120,
            "register_count": 18,
            "deposit_amount": 1800.75,
            "conversion_rate": 15.0,
            "ftt": 90.37
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
print(f"Status: {response.status_code}")
print(f"Response: {response.json()}")
```

### JavaScript 示例

```javascript
const url = 'https://api.example.com/stats/add_kol_stats';
const data = {
  user_id: 123,
  channel_code: 'ch001',
  data: [
    {
      stat_date: '2024-07-01',
      click_count: 100,
      register_count: 15,
      deposit_amount: 1500.50,
      conversion_rate: 15.0,
      ftt: 75.25
    }
  ]
};

fetch(url, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_api_key_here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 业务逻辑说明

1. **认证验证：** 系统会验证 `Authorization` 头中的 API Key 是否有效
2. **渠道码验证：** 根据 `channel_code` 查询对应的营销任务，如果不存在则返回错误
3. **数据关联：** 自动从营销任务中获取以下信息：
   - `kol_id` - KOL用户ID
   - `kol_name` - KOL姓名
   - `campaign_id` - 活动ID (任务ID)
   - `campaign_name` - 活动名称 (任务名称)
4. **数据保存：** 遍历 `data` 数组中的每一天数据，生成对应的统计记录
5. **去重处理：** 使用 `merge` 操作，相同日期的数据会被更新而不是重复插入

## 注意事项

### 重要提醒

1. **API Key 安全：** 请妥善保管您的 API Key，不要在客户端代码中暴露
2. **渠道码预设：** 确保 `channel_code` 在系统中已经存在对应的营销任务
3. **日期格式：** `stat_date` 必须使用 `YYYY-MM-DD` 格式
4. **数据类型：** 数值类型字段请确保传入正确的数据类型
5. **批量限制：** 建议单次上传不超过 100 天的数据

### 最佳实践

1. **错误处理：** 请根据不同的 HTTP 状态码进行相应的错误处理
2. **重试机制：** 对于网络错误或服务器错误，建议实施指数退避重试机制
3. **数据验证：** 在发送请求前，请在客户端进行基本的数据验证
4. **日志记录：** 建议记录请求和响应日志，便于问题排查

## 技术支持

如有疑问或需要技术支持，请联系：
- 邮箱：<EMAIL>
- 电话：xxx-xxxx-xxxx

## 更新历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-07-15 | 初始版本，支持基本的统计数据上传 | 