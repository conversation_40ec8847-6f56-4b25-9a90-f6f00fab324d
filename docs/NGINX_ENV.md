
# Nginx 安装及 https 证书设置

## Nginx 安装

```
# 确保 Nginx 已安装并运行
sudo dnf install nginx -y
sudo systemctl enable --now nginx
```

## 获取 SSL 证书

使用 Let’s Encrypt + Certbot 免费证书, 通过 snapd 安装

### 1. 添加 EPEL 仓库
```bash
sudo dnf install https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm
sudo dnf upgrade
```

### 2. 安装 snapd
```bash
sudo dnf install snapd
```

### 3. 启用并初始化 snapd
```bash
sudo systemctl enable --now snapd.socket
sudo ln -s /var/lib/snapd/snap /snap
```

### 4. 安装 Certbot（Let’s Encrypt）
```bash
sudo snap install core
sudo snap refresh core
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
```

---

### ✅ 继续配置 HTTPS（Nginx + Certbot）
```bash
# 确保 Nginx 已安装并运行

# 申请证书（替换为你的域名）
sudo certbot --nginx -d console.inflink.io
```
完成后，会在 对应的 conf 文件自动添加 https 证书
---

完成上述步骤后，你就可以正常使用 Certbot 为 `console.inflink.io` 配置 HTTPS 了。

